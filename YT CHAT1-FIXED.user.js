// ==UserScript==
// @name         YT CHAT1
// @namespace    https://greasyfork.org/en/users/160457-stan60250
// @version      1.4.0
// @description  CHATTER - Enhanced with proper timing and countdown
// <AUTHOR>
// @match        *://www.youtube.com/live*
// @match        *://www.youtube.com/live_chat?is_popout=1&v=*
// @icon         https://www.google.com/s2/favicons?domain=youtube.com
// @grant        none
// @license      MIT
// ==/UserScript==

// Configuration - 30 minutes to 1 hour intervals
var INTERVAL_SECONDS_MIN = 1800; // 30 minutes in seconds
var INTERVAL_SECONDS_MAX = 3600; // 1 hour in seconds
var STARTUP_DELAY_SECONDS = 5;

var UNICODE_SUN = '\u2600\ufe0f';
var UNICODE_MOON = '\ud83c\udf19';

(function() {
    'use strict';

    // Global variables for countdown
    var nextExecutionTime = 0;
    var countdownInterval = null;
    var originalTitle = document.title;
    var currentTimeout = null;

    // Timer function (keeping your exact working logic)
    function executeTask() {
        var url = window.location.href;
        if (url) {
            if (url.indexOf('/live_chat?') > -1) {
                sendChat(document);
            } else if (url.indexOf('/watch?') > -1) {
                var iframe = document.getElementById('chatframe');
                var chatFrame = iframe ? (iframe.contentDocument || iframe.contentWindow.document) : null;
                sendChat(chatFrame);
            }
        }
        console.log('[YT CHAT] ✅ Executed task at ' + new Date().toLocaleTimeString());
        
        // Schedule next execution
        scheduleNextExecution();
    }

    function getRandomInt(min, max) {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // Enhanced sendChat function with proper timing for submit button
    function sendChat(chatFrame) {
        if (chatFrame) {
            var chat_input = chatFrame.querySelector('div#input');
            if (chat_input) {
                var storedComments = JSON.parse(localStorage.getItem('comments')) || [];
                if (storedComments.length > 0) {
                    var msg = storedComments[Math.floor(Math.random() * storedComments.length)];
                    console.log('[YT CHAT] 💬 Preparing to send: ' + msg);

                    // Step 1: Focus and set the message
                    chat_input.focus();
                    chat_input.textContent = msg;
                    chat_input.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));

                    console.log('[YT CHAT] 📝 Message entered, waiting for submit button...');

                    // Step 2: Wait for submit button to become available and click it
                    var attempts = 0;
                    var maxAttempts = 10; // Try for up to 2 seconds

                    function trySubmit() {
                        attempts++;
                        var chat_submit = chatFrame.querySelector('.yt-spec-button-shape-next.yt-spec-button-shape-next--text.yt-spec-button-shape-next--mono.yt-spec-button-shape-next--size-m.yt-spec-button-shape-next--icon-button');

                        if (chat_submit && !chat_submit.disabled && chat_submit.offsetParent !== null) {
                            // Button is available and visible
                            console.log('[YT CHAT] 🚀 Submit button ready, clicking...');
                            chat_submit.click();
                            console.log('[YT CHAT] ✅ Message sent successfully!');
                        } else if (attempts < maxAttempts) {
                            // Button not ready yet, try again
                            console.log(`[YT CHAT] ⏳ Submit button not ready, attempt ${attempts}/${maxAttempts}`);
                            setTimeout(trySubmit, 200); // Wait 200ms and try again
                        } else {
                            // Max attempts reached
                            console.error('[YT CHAT] ❌ Submit button never became available');
                        }
                    }

                    // Start trying to submit after a short delay
                    setTimeout(trySubmit, 300);

                } else {
                    console.error('[YT CHAT] ❌ No comments found in localStorage.');
                }
            } else {
                console.error('[YT CHAT] ❌ Unable to find chat input');
            }
        } else {
            console.error('[YT CHAT] ❌ Unable to find chat iframe');
        }
    }

    // Fixed timing system - no more nested intervals
    function scheduleNextExecution() {
        // Clear any existing timeout
        if (currentTimeout) {
            clearTimeout(currentTimeout);
        }
        
        var delay = getRandomInt(INTERVAL_SECONDS_MIN, INTERVAL_SECONDS_MAX) * 1000;
        nextExecutionTime = Date.now() + delay;
        
        console.log('[YT CHAT] ⏰ Next execution in ' + Math.round(delay / 1000 / 60) + ' minutes at ' + new Date(nextExecutionTime).toLocaleTimeString());
        
        // Start countdown in title
        startCountdown();
        
        // Schedule the execution
        currentTimeout = setTimeout(executeTask, delay);
    }

    // Countdown in tab title
    function updateTabTitle() {
        if (!nextExecutionTime) return;
        
        var now = Date.now();
        var timeLeft = nextExecutionTime - now;
        
        if (timeLeft <= 0) {
            document.title = originalTitle;
            return;
        }
        
        var minutes = Math.floor(timeLeft / 60000);
        var seconds = Math.floor((timeLeft % 60000) / 1000);
        
        document.title = `⏰ ${minutes}m ${seconds}s - ${originalTitle}`;
    }

    function startCountdown() {
        if (countdownInterval) {
            clearInterval(countdownInterval);
        }
        
        updateTabTitle();
        countdownInterval = setInterval(updateTabTitle, 1000);
    }

    function stopCountdown() {
        if (countdownInterval) {
            clearInterval(countdownInterval);
            countdownInterval = null;
        }
        document.title = originalTitle;
    }

    // Keyboard shortcuts for control
    document.addEventListener('keydown', function(e) {
        // Ctrl+Shift+E to execute immediately
        if (e.ctrlKey && e.shiftKey && e.key === 'E') {
            e.preventDefault();
            console.log('[YT CHAT] 🔧 Manual execution triggered');
            stopCountdown();
            if (currentTimeout) {
                clearTimeout(currentTimeout);
            }
            executeTask();
        }
        // Ctrl+Shift+S to show status
        else if (e.ctrlKey && e.shiftKey && e.key === 'S') {
            e.preventDefault();
            var timeLeft = nextExecutionTime ? Math.round((nextExecutionTime - Date.now()) / 60000) : 0;
            var nextTime = nextExecutionTime ? new Date(nextExecutionTime).toLocaleTimeString() : 'Not scheduled';
            alert(`YT Chat Status:\n\nNext execution: ${nextTime}\nTime remaining: ${timeLeft} minutes\n\nShortcuts:\nCtrl+Shift+E - Execute now\nCtrl+Shift+S - Show status`);
        }
    });

    // Initialize - keeping your working startup logic but with fixed timing
    console.log(`[YT CHAT] 🚀 Script starting - intervals: ${INTERVAL_SECONDS_MIN/60}-${INTERVAL_SECONDS_MAX/60} minutes`);
    console.log('[YT CHAT] 🎮 Shortcuts: Ctrl+Shift+E (execute now), Ctrl+Shift+S (status)');
    
    setTimeout(() => {
        console.log('[YT CHAT] 🎯 First execution starting...');
        executeTask();
    }, STARTUP_DELAY_SECONDS * 1000);

    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        stopCountdown();
        if (currentTimeout) {
            clearTimeout(currentTimeout);
        }
    });

})();

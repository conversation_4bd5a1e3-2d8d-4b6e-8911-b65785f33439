<!DOCTYPE html>
<!-- saved from url=(0053)https://tpc.googlesyndication.com/sodar/5k7CCto5.html -->
<html data-kantu="1"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><script>
(function(){var h,aa="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){a!=Array.prototype&&a!=Object.prototype&&(a[b]=c.value)},k="undefined"!=typeof window&&window===this?this:"undefined"!=typeof global&&null!=global?global:this,ba=function(){ba=function(){};k.Symbol||(k.Symbol=ca)},ca=function(){var a=0;return function(b){return"jscomp_symbol_"+(b||"")+a++}}(),m=function(){ba();var a=k.Symbol.iterator;a||(a=k.Symbol.iterator=k.Symbol("iterator"));"function"!=typeof Array.prototype[a]&&
aa(Array.prototype,a,{configurable:!0,writable:!0,value:function(){return da(this)}});m=function(){}},da=function(a){var b=0;return ea(function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}})},ea=function(a){m();a={next:a};a[k.Symbol.iterator]=function(){return this};return a},fa="function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b},n;
if("function"==typeof Object.setPrototypeOf)n=Object.setPrototypeOf;else{var p;a:{var ha={ga:!0},ia={};try{ia.__proto__=ha;p=ia.ga;break a}catch(a){}p=!1}n=p?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var ja=n,ka=function(a){if(a){for(var b=k,c=["Number","isFinite"],d=0;d<c.length-1;d++){var e=c[d];e in b||(b[e]={});b=b[e]}c=c[c.length-1];d=b[c];a=a(d);a!=d&&null!=a&&aa(b,c,{configurable:!0,writable:!0,value:a})}};
ka(function(a){return a?a:function(a){return"number"!==typeof a?!1:!isNaN(a)&&Infinity!==a&&-Infinity!==a}});
var q=this,r=function(a){return"string"==typeof a},t=function(){},la=function(a){var b=typeof a;if("object"==b)if(a){if(a instanceof Array)return"array";if(a instanceof Object)return b;var c=Object.prototype.toString.call(a);if("[object Window]"==c)return"object";if("[object Array]"==c||"number"==typeof a.length&&"undefined"!=typeof a.splice&&"undefined"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable("splice"))return"array";if("[object Function]"==c||"undefined"!=typeof a.call&&"undefined"!=
typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable("call"))return"function"}else return"null";else if("function"==b&&"undefined"==typeof a.call)return"object";return b},u=function(a){var b=la(a);return"array"==b||"object"==b&&"number"==typeof a.length},v=function(a){return"function"==la(a)},ma=function(a){var b=typeof a;return"object"==b&&null!=a||"function"==b},na=function(a,b,c){return a.call.apply(a.bind,arguments)},oa=function(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,
2);return function(){var c=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(c,d);return a.apply(b,c)}}return function(){return a.apply(b,arguments)}},w=function(a,b,c){w=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?na:oa;return w.apply(null,arguments)},pa=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var b=c.slice();b.push.apply(b,arguments);return a.apply(this,b)}},qa=function(a,b){function c(){}c.prototype=
b.prototype;a.qa=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.ta=function(a,c,f){for(var d=Array(arguments.length-2),e=2;e<arguments.length;e++)d[e-2]=arguments[e];return b.prototype[c].apply(a,d)}};var ra=function(a){var b=Number;b=void 0===b?function(a){return a}:b;this.oa=a;this.transform=b},x={},sa=(x[1]=new ra("4CGeArbVQ"),x[6]=new ra("4CGeArbVR"),x);var y=function(a){if(Error.captureStackTrace)Error.captureStackTrace(this,y);else{var b=Error().stack;b&&(this.stack=b)}a&&(this.message=String(a))};qa(y,Error);y.prototype.name="CustomError";var ta=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(r(a))return r(b)&&1==b.length?a.indexOf(b,0):-1;for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},ua=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=r(a)?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)},va=function(a){return Array.prototype.concat.apply([],arguments)},wa=function(a){var b=a.length;if(0<
b){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};var Ea=function(a){if(!xa.test(a))return a;-1!=a.indexOf("&")&&(a=a.replace(ya,"&amp;"));-1!=a.indexOf("<")&&(a=a.replace(za,"&lt;"));-1!=a.indexOf(">")&&(a=a.replace(Aa,"&gt;"));-1!=a.indexOf('"')&&(a=a.replace(Ba,"&quot;"));-1!=a.indexOf("'")&&(a=a.replace(Ca,"&#39;"));-1!=a.indexOf("\x00")&&(a=a.replace(Da,"&#0;"));return a},ya=/&/g,za=/</g,Aa=/>/g,Ba=/"/g,Ca=/'/g,Da=/\x00/g,xa=/[\x00&<>"']/;var z;a:{var Fa=q.navigator;if(Fa){var Ga=Fa.userAgent;if(Ga){z=Ga;break a}}z=""};var A=function(a,b){this.na=100;this.ha=a;this.pa=b;this.S=0;this.R=null};A.prototype.get=function(){if(0<this.S){this.S--;var a=this.R;this.R=a.next;a.next=null}else a=this.ha();return a};A.prototype.put=function(a){this.pa(a);this.S<this.na&&(this.S++,a.next=this.R,this.R=a)};var Ha=function(a){q.setTimeout(function(){throw a;},0)},Ia,Ja=function(){var a=q.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&-1==z.indexOf("Presto")&&(a=function(){var a=document.createElement("IFRAME");a.style.display="none";a.src="";document.documentElement.appendChild(a);var b=a.contentWindow;a=b.document;a.open();a.write("");a.close();var c="callImmediate"+Math.random(),d="file:"==b.location.protocol?"*":b.location.protocol+
"//"+b.location.host;a=w(function(a){if(("*"==d||a.origin==d)&&a.data==c)this.port1.onmessage()},this);b.addEventListener("message",a,!1);this.port1={};this.port2={postMessage:function(){b.postMessage(c,d)}}});if("undefined"!==typeof a&&-1==z.indexOf("Trident")&&-1==z.indexOf("MSIE")){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var a=c.Z;c.Z=null;a()}};return function(a){d.next={Z:a};d=d.next;b.port2.postMessage(0)}}return"undefined"!==typeof document&&"onreadystatechange"in
document.createElement("SCRIPT")?function(a){var b=document.createElement("SCRIPT");b.onreadystatechange=function(){b.onreadystatechange=null;b.parentNode.removeChild(b);b=null;a();a=null};document.documentElement.appendChild(b)}:function(a){q.setTimeout(a,0)}};var Ka=function(){this.T=this.I=null},Ma=new A(function(){return new La},function(a){a.reset()});Ka.prototype.add=function(a,b){var c=Ma.get();c.set(a,b);this.T?this.T.next=c:this.I=c;this.T=c};Ka.prototype.remove=function(){var a=null;this.I&&(a=this.I,this.I=this.I.next,this.I||(this.T=null),a.next=null);return a};var La=function(){this.next=this.scope=this.V=null};La.prototype.set=function(a,b){this.V=a;this.scope=b;this.next=null};La.prototype.reset=function(){this.next=this.scope=this.V=null};var D=function(a,b){C||Na();Oa||(C(),Oa=!0);Pa.add(a,b)},C,Na=function(){if(q.Promise&&q.Promise.resolve){var a=q.Promise.resolve(void 0);C=function(){a.then(Qa)}}else C=function(){var a=Qa;!v(q.setImmediate)||q.Window&&q.Window.prototype&&-1==z.indexOf("Edge")&&q.Window.prototype.setImmediate==q.setImmediate?(Ia||(Ia=Ja()),Ia(a)):q.setImmediate(a)}},Oa=!1,Pa=new Ka,Qa=function(){for(var a;a=Pa.remove();){try{a.V.call(a.scope)}catch(b){Ha(b)}Ma.put(a)}Oa=!1};var F=function(a){this.l=0;this.da=void 0;this.F=this.u=this.C=null;this.P=this.U=!1;if(a!=t)try{var b=this;a.call(void 0,function(a){E(b,2,a)},function(a){E(b,3,a)})}catch(c){E(this,3,c)}},Ra=function(){this.next=this.context=this.G=this.L=this.A=null;this.J=!1};Ra.prototype.reset=function(){this.context=this.G=this.L=this.A=null;this.J=!1};
var Sa=new A(function(){return new Ra},function(a){a.reset()}),Ta=function(a,b,c){var d=Sa.get();d.L=a;d.G=b;d.context=c;return d},G=function(){var a=new F(t);E(a,2,void 0);return a},H=function(a){return new F(function(b,c){c(a)})},Va=function(a,b,c){Ua(a,b,c,null)||D(pa(b,a))},Wa=function(a){return new F(function(b,c){var d=a.length,e=[];if(d)for(var f=function(a,c){d--;e[a]=c;0==d&&b(e)},g=function(a){c(a)},l=0,B;l<a.length;l++)B=a[l],Va(B,pa(f,l),g);else b(e)})},I=function(){var a,b,c=new F(function(c,
e){a=c;b=e});return new Xa(c,a,b)};F.prototype.then=function(a,b,c){return Ya(this,v(a)?a:null,v(b)?b:null,c)};F.prototype.then=F.prototype.then;F.prototype.$goog_Thenable=!0;var $a=function(a,b){b=Ta(b,b,void 0);b.J=!0;Za(a,b);return a};F.prototype.cancel=function(a){0==this.l&&D(function(){var b=new J(a);ab(this,b)},this)};
var ab=function(a,b){if(0==a.l)if(a.C){var c=a.C;if(c.u){for(var d=0,e=null,f=null,g=c.u;g&&(g.J||(d++,g.A==a&&(e=g),!(e&&1<d)));g=g.next)e||(f=g);e&&(0==c.l&&1==d?ab(c,b):(f?(d=f,d.next==c.F&&(c.F=d),d.next=d.next.next):bb(c),cb(c,e,3,b)))}a.C=null}else E(a,3,b)},Za=function(a,b){a.u||2!=a.l&&3!=a.l||db(a);a.F?a.F.next=b:a.u=b;a.F=b},Ya=function(a,b,c,d){var e=Ta(null,null,null);e.A=new F(function(a,g){e.L=b?function(c){try{var e=b.call(d,c);a(e)}catch(Q){g(Q)}}:a;e.G=c?function(b){try{var e=c.call(d,
b);void 0===e&&b instanceof J?g(b):a(e)}catch(Q){g(Q)}}:g});e.A.C=a;Za(a,e);return e.A};F.prototype.ra=function(a){this.l=0;E(this,2,a)};F.prototype.sa=function(a){this.l=0;E(this,3,a)};
var E=function(a,b,c){0==a.l&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.l=1,Ua(c,a.ra,a.sa,a)||(a.da=c,a.l=b,a.C=null,db(a),3!=b||c instanceof J||eb(a,c)))},Ua=function(a,b,c,d){if(a instanceof F)return Za(a,Ta(b||t,c||null,d)),!0;if(a)try{var e=!!a.$goog_Thenable}catch(g){e=!1}else e=!1;if(e)return a.then(b,c,d),!0;if(ma(a))try{var f=a.then;if(v(f))return fb(a,f,b,c,d),!0}catch(g){return c.call(d,g),!0}return!1},fb=function(a,b,c,d,e){var f=!1,g=function(a){f||(f=!0,c.call(e,
a))},l=function(a){f||(f=!0,d.call(e,a))};try{b.call(a,g,l)}catch(B){l(B)}},db=function(a){a.U||(a.U=!0,D(a.ia,a))},bb=function(a){var b=null;a.u&&(b=a.u,a.u=b.next,b.next=null);a.u||(a.F=null);return b};F.prototype.ia=function(){for(var a;a=bb(this);)cb(this,a,this.l,this.da);this.U=!1};
var cb=function(a,b,c,d){if(3==c&&b.G&&!b.J)for(;a&&a.P;a=a.C)a.P=!1;if(b.A)b.A.C=null,gb(b,c,d);else try{b.J?b.L.call(b.context):gb(b,c,d)}catch(e){hb.call(null,e)}Sa.put(b)},gb=function(a,b,c){2==b?a.L.call(a.context,c):a.G&&a.G.call(a.context,c)},eb=function(a,b){a.P=!0;D(function(){a.P&&hb.call(null,b)})},hb=Ha,J=function(a){y.call(this,a)};qa(J,y);J.prototype.name="cancel";var Xa=function(a,b,c){this.ca=a;this.resolve=b;this.reject=c};var ib="StopIteration"in q?q.StopIteration:{message:"StopIteration",stack:""},jb=function(){};jb.prototype.next=function(){throw ib;};jb.prototype.fa=function(){return this};var K=function(a,b){this.j={};this.c=[];this.M=this.b=0;var c=arguments.length;if(1<c){if(c%2)throw Error("Uneven number of arguments");for(var d=0;d<c;d+=2)this.set(arguments[d],arguments[d+1])}else a&&this.addAll(a)};h=K.prototype;h.m=function(){L(this);for(var a=[],b=0;b<this.c.length;b++)a.push(this.j[this.c[b]]);return a};h.v=function(){L(this);return this.c.concat()};h.K=function(a){return M(this.j,a)};h.clear=function(){this.j={};this.M=this.b=this.c.length=0};
h.remove=function(a){return M(this.j,a)?(delete this.j[a],this.b--,this.M++,this.c.length>2*this.b&&L(this),!0):!1};var L=function(a){if(a.b!=a.c.length){for(var b=0,c=0;b<a.c.length;){var d=a.c[b];M(a.j,d)&&(a.c[c++]=d);b++}a.c.length=c}if(a.b!=a.c.length){var e={};for(c=b=0;b<a.c.length;)d=a.c[b],M(e,d)||(a.c[c++]=d,e[d]=1),b++;a.c.length=c}};h=K.prototype;h.get=function(a,b){return M(this.j,a)?this.j[a]:b};h.set=function(a,b){M(this.j,a)||(this.b++,this.c.push(a),this.M++);this.j[a]=b};
h.addAll=function(a){if(a instanceof K)for(var b=a.v(),c=0;c<b.length;c++)this.set(b[c],a.get(b[c]));else for(b in a)this.set(b,a[b])};h.forEach=function(a,b){for(var c=this.v(),d=0;d<c.length;d++){var e=c[d],f=this.get(e);a.call(b,f,e,this)}};h.clone=function(){return new K(this)};h.fa=function(a){L(this);var b=0,c=this.M,d=this,e=new jb;e.next=function(){if(c!=d.M)throw Error("The map has changed since the iterator was created");if(b>=d.c.length)throw ib;var e=d.c[b++];return a?e:d.j[e]};return e};
var M=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};var kb=function(a){if(a.m&&"function"==typeof a.m)return a.m();if(r(a))return a.split("");if(u(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b},lb=function(a,b,c){if(a.forEach&&"function"==typeof a.forEach)a.forEach(b,c);else if(u(a)||r(a))ua(a,b,c);else{if(a.v&&"function"==typeof a.v)var d=a.v();else if(a.m&&"function"==typeof a.m)d=void 0;else if(u(a)||r(a)){d=[];for(var e=a.length,f=0;f<e;f++)d.push(f)}else for(f in d=[],e=0,a)d[e++]=f;e=kb(a);
f=e.length;for(var g=0;g<f;g++)b.call(c,e[g],d&&d[g],a)}};var mb=/^(?:([^:/?#.]+):)?(?:\/\/(?:([^/?#]*)@)?([^/#?]*?)(?::([0-9]+))?(?=[/#?]|$))?([^?#]+)?(?:\?([^#]*))?(?:#([\s\S]*))?$/,nb=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};var N=function(a){this.s=this.D=this.w="";this.H=null;this.B=this.o="";this.h=this.ka=!1;if(a instanceof N){this.h=a.h;ob(this,a.w);var b=a.D;O(this);this.D=b;b=a.s;O(this);this.s=b;pb(this,a.H);b=a.o;O(this);this.o=b;qb(this,a.i.clone());a=a.B;O(this);this.B=a}else a&&(b=String(a).match(mb))?(this.h=!1,ob(this,b[1]||"",!0),a=b[2]||"",O(this),this.D=P(a),a=b[3]||"",O(this),this.s=P(a,!0),pb(this,b[4]),a=b[5]||"",O(this),this.o=P(a,!0),qb(this,b[6]||"",!0),a=b[7]||"",O(this),this.B=P(a)):(this.h=!1,
this.i=new R(null,this.h))};N.prototype.toString=function(){var a=[],b=this.w;b&&a.push(S(b,rb,!0),":");var c=this.s;if(c||"file"==b)a.push("//"),(b=this.D)&&a.push(S(b,rb,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.H,null!=c&&a.push(":",String(c));if(c=this.o)this.s&&"/"!=c.charAt(0)&&a.push("/"),a.push(S(c,"/"==c.charAt(0)?sb:tb,!0));(c=this.i.toString())&&a.push("?",c);(c=this.B)&&a.push("#",S(c,ub));return a.join("")};
N.prototype.resolve=function(a){var b=this.clone(),c=!!a.w;c?ob(b,a.w):c=!!a.D;if(c){var d=a.D;O(b);b.D=d}else c=!!a.s;c?(d=a.s,O(b),b.s=d):c=null!=a.H;d=a.o;if(c)pb(b,a.H);else if(c=!!a.o){if("/"!=d.charAt(0))if(this.s&&!this.o)d="/"+d;else{var e=b.o.lastIndexOf("/");-1!=e&&(d=b.o.substr(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var f=[],g=0;g<e.length;){var l=e[g++];"."==l?d&&g==e.length&&f.push(""):".."==
l?((1<f.length||1==f.length&&""!=f[0])&&f.pop(),d&&g==e.length&&f.push("")):(f.push(l),d=!0)}d=f.join("/")}else d=e}c?(O(b),b.o=d):c=""!==a.i.toString();c?qb(b,a.i.clone()):c=!!a.B;c&&(a=a.B,O(b),b.B=a);return b};N.prototype.clone=function(){return new N(this)};
var ob=function(a,b,c){O(a);a.w=c?P(b,!0):b;a.w&&(a.w=a.w.replace(/:$/,""))},pb=function(a,b){O(a);if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("Bad port number "+b);a.H=b}else a.H=null},qb=function(a,b,c){O(a);b instanceof R?(a.i=b,a.i.X(a.h)):(c||(b=S(b,vb)),a.i=new R(b,a.h))};N.prototype.getQuery=function(){return this.i.toString()};var T=function(a,b,c){O(a);a.i.set(b,c);return a};N.prototype.removeParameter=function(a){O(this);this.i.remove(a);return this};
var O=function(a){if(a.ka)throw Error("Tried to modify a read-only Uri");};N.prototype.X=function(a){this.h=a;this.i&&this.i.X(a)};
var P=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""},S=function(a,b,c){return r(a)?(a=encodeURI(a).replace(b,wb),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null},wb=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)},rb=/[#\/\?@]/g,tb=/[#\?:]/g,sb=/[#\?]/g,vb=/[#\?@]/g,ub=/#/g,R=function(a,b){this.b=this.a=null;this.f=a||null;this.h=!!b},U=function(a){a.a||(a.a=new K,a.b=0,a.f&&nb(a.f,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g,
" ")),c)}))};h=R.prototype;h.add=function(a,b){U(this);this.f=null;a=V(this,a);var c=this.a.get(a);c||this.a.set(a,c=[]);c.push(b);this.b+=1;return this};h.remove=function(a){U(this);a=V(this,a);return this.a.K(a)?(this.f=null,this.b-=this.a.get(a).length,this.a.remove(a)):!1};h.clear=function(){this.a=this.f=null;this.b=0};h.K=function(a){U(this);a=V(this,a);return this.a.K(a)};h.forEach=function(a,b){U(this);this.a.forEach(function(c,d){ua(c,function(c){a.call(b,c,d,this)},this)},this)};
h.v=function(){U(this);for(var a=this.a.m(),b=this.a.v(),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};h.m=function(a){U(this);var b=[];if(r(a))this.K(a)&&(b=va(b,this.a.get(V(this,a))));else{a=this.a.m();for(var c=0;c<a.length;c++)b=va(b,a[c])}return b};h.set=function(a,b){U(this);this.f=null;a=V(this,a);this.K(a)&&(this.b-=this.a.get(a).length);this.a.set(a,[b]);this.b+=1;return this};
h.get=function(a,b){if(!a)return b;a=this.m(a);return 0<a.length?String(a[0]):b};h.toString=function(){if(this.f)return this.f;if(!this.a)return"";for(var a=[],b=this.a.v(),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.m(d);for(var f=0;f<d.length;f++){var g=e;""!==d[f]&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.f=a.join("&")};h.clone=function(){var a=new R;a.f=this.f;this.a&&(a.a=this.a.clone(),a.b=this.b);return a};
var V=function(a,b){b=String(b);a.h&&(b=b.toLowerCase());return b};R.prototype.X=function(a){a&&!this.h&&(U(this),this.f=null,this.a.forEach(function(a,c){var b=c.toLowerCase();c!=b&&(this.remove(c),this.remove(b),0<a.length&&(this.f=null,this.a.set(V(this,b),wa(a)),this.b+=a.length))},this));this.h=a};R.prototype.extend=function(a){for(var b=0;b<arguments.length;b++)lb(arguments[b],function(a,b){this.add(b,a)},this)};var xb=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){var a=!1;try{var b=Object.defineProperty({},"passive",{get:function(){a=!0}});q.addEventListener("test",null,b)}catch(c){}return a});function yb(a){return a?a.passive&&xb()?a:a.capture||!1:a}
var zb=function(a,b,c,d){a.addEventListener?a.addEventListener(b,c,yb(d)):a.attachEvent&&a.attachEvent("on"+b,c)},Ab=function(a,b,c){a.removeEventListener?a.removeEventListener(b,c,yb(void 0)):a.detachEvent&&a.detachEvent("on"+b,c)};var Bb=function(a){var b=window,c=!1,d=!1;d=void 0===d?!1:d;c=void 0===c?!1:c;b.google_image_requests||(b.google_image_requests=[]);var e=b.document.createElement("img");if(c){var f=function(){if(c){var a=b.google_image_requests,d=ta(a,e);0<=d&&Array.prototype.splice.call(a,d,1)}Ab(e,"load",f);Ab(e,"error",f)};zb(e,"load",f);zb(e,"error",f)}d&&(e.referrerPolicy="no-referrer");e.src=a;b.google_image_requests.push(e)};var Db=function(a,b){b=void 0===b?window:b;var c=void 0===c?{}:c;return new F(function(d,e){function f(){g.onload=null;g.onerror=null;g.onreadystatechange=null;q.setTimeout(Cb(function(){return document.body.removeChild(g)},"l:lsc"),0)}var g=b.document.createElement("SCRIPT".toString());g.onload=g.onreadystatechange=function(){g.readyState&&"loaded"!=g.readyState&&"complete"!=g.readyState||(f(),d())};g.onerror=function(){f();e(null)};g.type="text/javascript";g.src=a;for(var l in c)g.setAttribute(l,
c[l]);b.document.body.appendChild(g)})};function Eb(a){return T(T(T(new N("//pagead2.googlesyndication.com/pagead/gen_204"),"id","sodar"),"v",32),"t",a)}
var W=function(a,b,c,d){var e=Eb(1);T(e,"e",a);c&&T(e,"li",c);d&&T(e,"cv",d);b&&T(e,"bgai",b);Bb(e.toString())},Hb=function(){void 0===Fb&&(Fb=Gb())},Fb,Gb=function(){var a=window.GoogleTyFxhY;if(!a)return W(13),null;if(0==a.length)return W(1),null;a=a.shift();return(a._scs_||a._cv_)&&a._bgu_&&a._bgp_?a:(W(2),null)},Cb=function(a,b){return function(){try{return a.apply(this,arguments)}catch(B){if(!(1<=Ib.count)){var c=B,d=Eb(3);new Hb;var e=(Fb||null)._scs_;e&&T(d,"bgai",e);T(d,"c",b);var f=c.toString();
c.name&&-1==f.indexOf(c.name)&&(f+=": "+c.name);c.message&&-1==f.indexOf(c.message)&&(f+=": "+c.message);if(c.stack){c=c.stack;var g=f;try{-1==c.indexOf(g)&&(c=g+"\n"+c);for(var l;c!=l;)l=c,c=c.replace(/((https?:\/..*\/)[^\/:]*:\d+(?:.|\n)*)\2/,"$1");f=c.replace(/\n */g,"\n")}catch(Q){f=g}}T(d,"ex",f);d=d.toString();2E3<d.length?W(11,e):Bb(d);Ib.count+=1}}}},Ib={count:0};function Jb(a){if(!ma(a))return!1;switch(a["0"]){case "0":return!("0"===a["0"]&&r(a["1"])&&r(a["2"])&&r(a["3"])&&r(a["4"])&&r(a["8"]))||"5"in a&&!r(a["5"])?!1:!0;case "2":return"2"===a["0"]}return!1};var X=function(a){a=void 0===a?{}:a;var b=void 0===a.la?!1:a.la;this.W=void 0===a.W?!0:a.W;this.ma=b;this.N=window;this.O=this.g=null;this.Y=I()},Pb=function(a){var b=Kb(a).then(function(b){a.g=b},function(a){return H(a||13)}).then(function(){return Lb(a)},function(a){return H(a)}),c=a.ba();return $a(Wa([b,c]).then(function(){return Mb(a)},function(a){return H(a)}).then(function(){return Nb()},function(a){return H(a)}).then(function(){return Ob(a)},function(a){return H(a)}).then(function(){return G()},
function(b){b&&a.W&&W(b,a.g?a.g._scs_:void 0,a.g?a.g._li_:void 0,a.g?a.g._cv_:void 0);return H(b)}),function(){return a.$()})},Lb=function(a){var b="//pagead2.googlesyndication.com/bg/"+Ea(a.g._bgu_)+".js";if(a.ma){var c=document.createElement("IFRAME".toString());c.style.display="none";document.body.appendChild(c);a.N=c.contentWindow||window}return Db(b,a.N).then(function(){return G()},function(){return H(7)})};X.prototype.ba=function(){return G()};
var Mb=function(a){if(!a.N.botguard||!a.N.botguard.bg)return H(5);a.O=new a.N.botguard.bg(a.g._bgp_,function(){});return G()},Nb=function(){return new F(function(a){a()})},Ob=function(a){if(!a.O||!a.O.invoke)return H(6);a.O.invoke(function(b){var c=a.g._scs_,d=a.g._li_,e=a.g._cv_,f=Eb(2);d&&T(f,"li",d);e&&T(f,"cv",e);c&&T(f,"bgai",c);T(f,"bg",b);b=f.toString();2E3<b.length?W(4,c,d,e):Bb(b);a.Y.resolve()},!0);return a.Y.ca};X.prototype.$=function(){};var Qb=I(),Rb=!1,Sb=null;window.onNewContextInstance=function(a){Sb=a;Sb.onAnalyticsEvent(function(a){if(!Rb){Rb=!0;a=a.split("&");m();var b=(b=a[Symbol.iterator])?b.call(a):da(a);a=b.next().value;b=b.next().value;if(a&&b){var d={};a=(d._bgu_=a,d._bgp_=b,d);Qb.resolve(a)}else Qb.reject(2)}})};var Y=function(){X.call(this);this.ea=I();this.ja=I();this.aa="*"};Y.prototype=fa(X.prototype);Y.prototype.constructor=Y;if(ja)ja(Y,X);else for(var Z in X)if("prototype"!=Z)if(Object.defineProperties){var Tb=Object.getOwnPropertyDescriptor(X,Z);Tb&&Object.defineProperty(Y,Z,Tb)}else Y[Z]=X[Z];Y.qa=X.prototype;
var Kb=function(a){zb(window,"message",Cb(function(b){if(b.source===window.parent&&(b=b.data,b=Jb(b)?b:null))switch(b["0"]){case "0":var c={};c=(c._scs_=b["1"],c._bgu_=b["2"],c._bgp_=b["3"],c._li_=b["4"],c._cv_=b["5"],c);a.ea.resolve(c);a.aa=b["8"];break;case "2":for(c in b)if("0"!==c){var d=sa[Number(c)];document[d.oa]=d.transform(b[c])}a.ja.resolve()}},"r:i"),{capture:void 0});return a.ea.ca};Y.prototype.ba=function(){return G()};Y.prototype.$=function(){window.parent.postMessage("",this.aa)};var Ub=[];Ub.push(new Y);Ub.forEach(function(a){return Pb(a)});}).call(this);
</script>
</head><body></body></html>
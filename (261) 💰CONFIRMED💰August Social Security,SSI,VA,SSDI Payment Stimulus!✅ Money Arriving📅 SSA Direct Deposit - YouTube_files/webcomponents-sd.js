(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var k;function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}function p(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");}function q(a){if(!(a instanceof Array)){a=p(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a}
function ba(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var ca=ba(this);function r(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

Copyright (c) 2016 The Polymer Project Authors. All rights reserved.
This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
Code distributed by Google as part of the polymer project is also
subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
function da(){}da.prototype.toJSON=function(){return{}};function u(a){a.__shady||(a.__shady=new da);return a.__shady}function v(a){return a&&a.__shady};var w=window.ShadyDOM||{};w.Rb=!(!Element.prototype.attachShadow||!Node.prototype.getRootNode);var ea=Object.getOwnPropertyDescriptor(Node.prototype,"firstChild");w.j=!!(ea&&ea.configurable&&ea.get);w.inUse=w.force||!w.Rb;w.noPatch=w.noPatch||!1;w.S=w.preferPerformance;w.qa=w.noPatch==="on-demand";var fa;var ha=w.querySelectorImplementation;fa=["native","selectorEngine"].indexOf(ha)>-1?ha:void 0;w.querySelectorImplementation=fa;var ja=navigator.userAgent.match("Trident");w.Qa=ja;
function ka(){return Document.prototype.msElementsFromPoint?"msElementsFromPoint":"elementsFromPoint"}function x(a){return(a=v(a))&&a.firstChild!==void 0}function y(a){return a instanceof ShadowRoot}function la(a){return(a=(a=v(a))&&a.root)&&a.xa()}var z=Element.prototype,ma=z.matches||z.matchesSelector||z.mozMatchesSelector||z.msMatchesSelector||z.oMatchesSelector||z.webkitMatchesSelector,na=document.createTextNode(""),oa=0,pa=[];
(new MutationObserver(function(){for(;pa.length;)try{pa.shift()()}catch(a){throw na.textContent=oa++,a;}})).observe(na,{characterData:!0});function qa(a){pa.push(a);na.textContent=oa++}var ra=document.contains?function(a,b){return a.__shady_native_contains(b)}:function(a,b){return a===b||a.documentElement&&a.documentElement.__shady_native_contains(b)};function sa(a,b){for(;b;){if(b==a)return!0;b=b.__shady_parentNode}return!1}
function ta(a){for(var b=a.length-1;b>=0;b--){var c=a[b],d=c.getAttribute("id")||c.getAttribute("name");d&&d!=="length"&&isNaN(d)&&(a[d]=c)}a.item=function(e){return a[e]};a.namedItem=function(e){if(e!=="length"&&isNaN(e)&&a[e])return a[e];for(var f=p(a),g=f.next();!g.done;g=f.next())if(g=g.value,(g.getAttribute("id")||g.getAttribute("name"))==e)return g;return null};return a}function ua(a){var b=[];for(a=a.__shady_native_firstChild;a;a=a.__shady_native_nextSibling)b.push(a);return b}
function va(a){var b=[];for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling)b.push(a);return b}function wa(a,b,c){c.configurable=!0;if(c.value)a[b]=c.value;else try{Object.defineProperty(a,b,c)}catch(d){}}function B(a,b,c,d){c=c===void 0?"":c;for(var e in b)d&&d.indexOf(e)>=0||wa(a,c+e,b[e])}function xa(a,b){for(var c in b)c in a&&wa(a,c,b[c])}function C(a){var b={};Object.getOwnPropertyNames(a).forEach(function(c){b[c]=Object.getOwnPropertyDescriptor(a,c)});return b}
function ya(a,b){for(var c=Object.getOwnPropertyNames(b),d=0,e;d<c.length;d++)e=c[d],a[e]=b[e]}function za(a){return a instanceof Node?a:document.createTextNode(""+a)}function D(){var a=r.apply(0,arguments);if(a.length===1)return za(a[0]);var b=document.createDocumentFragment();a=p(a);for(var c=a.next();!c.done;c=a.next())b.appendChild(za(c.value));return b}
function Aa(a){var b;for(b=b===void 0?1:b;b>0;b--)a=a.reduce(function(c,d){Array.isArray(d)?c.push.apply(c,q(d)):c.push(d);return c},[]);return a}function Ba(a){var b=[],c=new Set;a=p(a);for(var d=a.next();!d.done;d=a.next())d=d.value,c.has(d)||(b.push(d),c.add(d));return b};var Ca=[],Da;function Ea(a){Da||(Da=!0,qa(Fa));Ca.push(a)}function Fa(){Da=!1;for(var a=!!Ca.length;Ca.length;)Ca.shift()();return a}Fa.list=Ca;function Ga(){this.Y=!1;this.addedNodes=[];this.removedNodes=[];this.ca=new Set}function Ha(a){a.Y||(a.Y=!0,qa(function(){a.flush()}))}Ga.prototype.flush=function(){if(this.Y){this.Y=!1;var a=this.takeRecords();a.length&&this.ca.forEach(function(b){b(a)})}};Ga.prototype.takeRecords=function(){if(this.addedNodes.length||this.removedNodes.length){var a=[{addedNodes:this.addedNodes,removedNodes:this.removedNodes}];this.addedNodes=[];this.removedNodes=[];return a}return[]};
function Ia(a,b){var c=u(a);c.R||(c.R=new Ga);c.R.ca.add(b);var d=c.R;return{Xa:b,ub:d,tb:a,takeRecords:function(){return d.takeRecords()}}}function Ja(a){var b=a&&a.ub;b&&(b.ca.delete(a.Xa),b.ca.size||(u(a.tb).R=null))}
function Ka(a,b){var c=b.getRootNode();return a.map(function(d){var e=c===d.target.getRootNode();if(e&&d.addedNodes){if(e=[].slice.call(d.addedNodes).filter(function(f){return c===f.getRootNode()}),e.length)return d=Object.create(d),Object.defineProperty(d,"addedNodes",{value:e,configurable:!0}),d}else if(e)return d}).filter(function(d){return d})};var La=/[&\u00A0"]/g,Ma=/[&\u00A0<>]/g;function Na(a){switch(a){case "&":return"&amp;";case "<":return"&lt;";case ">":return"&gt;";case '"':return"&quot;";case "\u00a0":return"&nbsp;"}}function Oa(a){for(var b={},c=0;c<a.length;c++)b[a[c]]=!0;return b}var Pa=Oa("area base br col command embed hr img input keygen link meta param source track wbr".split(" ")),Qa=Oa("style script xmp iframe noembed noframes plaintext noscript".split(" "));
function Ra(a,b){a.localName==="template"&&(a=a.content);for(var c="",d=b?b(a):a.childNodes,e=0,f=d.length,g=void 0;e<f&&(g=d[e]);e++){a:{var h=g;var l=a,m=b;switch(h.nodeType){case Node.ELEMENT_NODE:l=h.localName;for(var n="<"+l,t=h.attributes,A=0,ia;ia=t[A];A++)n+=" "+ia.name+'="'+ia.value.replace(La,Na)+'"';n+=">";h=Pa[l]?n:n+Ra(h,m)+"</"+l+">";break a;case Node.TEXT_NODE:h=h.data;h=l&&Qa[l.localName]?h:h.replace(Ma,Na);break a;case Node.COMMENT_NODE:h="\x3c!--"+h.data+"--\x3e";break a;default:throw window.console.error(h),
Error("not implemented");}h=void 0}c+=h}return c};var Sa=w.j,Ta={querySelector:function(a){return this.__shady_native_querySelector(a)},querySelectorAll:function(a){return this.__shady_native_querySelectorAll(a)}},Ua={};function Va(a){Ua[a]=function(b){return b["__shady_native_"+a]}}function Wa(a,b){B(a,b,"__shady_native_");for(var c in b)Va(c)}function E(a,b){b=b===void 0?[]:b;for(var c=0;c<b.length;c++){var d=b[c],e=Object.getOwnPropertyDescriptor(a,d);e&&(Object.defineProperty(a,"__shady_native_"+d,e),e.value?Ta[d]||(Ta[d]=e.value):Va(d))}}
var F=document.createTreeWalker(document,NodeFilter.SHOW_ALL,null,!1),G=document.createTreeWalker(document,NodeFilter.SHOW_ELEMENT,null,!1),Xa=document.implementation.createHTMLDocument("inert");function Ya(a){for(var b;b=a.__shady_native_firstChild;)a.__shady_native_removeChild(b)}var Za=["firstElementChild","lastElementChild","children","childElementCount"],$a=["querySelector","querySelectorAll","append","prepend","replaceChildren"];
function ab(){var a=["dispatchEvent","addEventListener","removeEventListener"];window.EventTarget?(E(window.EventTarget.prototype,a),window.__shady_native_addEventListener===void 0&&E(Window.prototype,a)):(E(Node.prototype,a),E(Window.prototype,a),E(XMLHttpRequest.prototype,a));Sa?E(Node.prototype,"parentNode firstChild lastChild previousSibling nextSibling childNodes parentElement textContent".split(" ")):Wa(Node.prototype,{parentNode:{get:function(){F.currentNode=this;return F.parentNode()}},firstChild:{get:function(){F.currentNode=
this;return F.firstChild()}},lastChild:{get:function(){F.currentNode=this;return F.lastChild()}},previousSibling:{get:function(){F.currentNode=this;return F.previousSibling()}},nextSibling:{get:function(){F.currentNode=this;return F.nextSibling()}},childNodes:{get:function(){var b=[];F.currentNode=this;for(var c=F.firstChild();c;)b.push(c),c=F.nextSibling();return b}},parentElement:{get:function(){G.currentNode=this;return G.parentNode()}},textContent:{get:function(){switch(this.nodeType){case Node.ELEMENT_NODE:case Node.DOCUMENT_FRAGMENT_NODE:for(var b=
document.createTreeWalker(this,NodeFilter.SHOW_TEXT,null,!1),c="",d;d=b.nextNode();)c+=d.nodeValue;return c;default:return this.nodeValue}},set:function(b){if(typeof b==="undefined"||b===null)b="";switch(this.nodeType){case Node.ELEMENT_NODE:case Node.DOCUMENT_FRAGMENT_NODE:Ya(this);(b.length>0||this.nodeType===Node.ELEMENT_NODE)&&this.__shady_native_insertBefore(document.createTextNode(b),void 0);break;default:this.nodeValue=b}}}});E(Node.prototype,"appendChild insertBefore removeChild replaceChild cloneNode contains".split(" "));
E(HTMLElement.prototype,["parentElement","contains"]);a={firstElementChild:{get:function(){G.currentNode=this;return G.firstChild()}},lastElementChild:{get:function(){G.currentNode=this;return G.lastChild()}},children:{get:function(){var b=[];G.currentNode=this;for(var c=G.firstChild();c;)b.push(c),c=G.nextSibling();return ta(b)}},childElementCount:{get:function(){return this.children?this.children.length:0}}};Sa?(E(Element.prototype,Za),E(Element.prototype,["previousElementSibling","nextElementSibling",
"innerHTML","className"]),E(HTMLElement.prototype,["children","innerHTML","className"])):(Wa(Element.prototype,a),Wa(Element.prototype,{previousElementSibling:{get:function(){G.currentNode=this;return G.previousSibling()}},nextElementSibling:{get:function(){G.currentNode=this;return G.nextSibling()}},innerHTML:{get:function(){return Ra(this,ua)},set:function(b){var c=this.localName==="template"?this.content:this;Ya(c);var d=this.localName||"div";d=this.namespaceURI&&this.namespaceURI!==Xa.namespaceURI?
Xa.createElementNS(this.namespaceURI,d):Xa.createElement(d);d.innerHTML=b;for(b=this.localName==="template"?d.content:d;d=b.__shady_native_firstChild;)c.__shady_native_insertBefore(d,void 0)}},className:{get:function(){return this.getAttribute("class")||""},set:function(b){this.setAttribute("class",b)}}}));E(Element.prototype,"setAttribute getAttribute hasAttribute removeAttribute toggleAttribute focus blur".split(" "));E(Element.prototype,$a);E(HTMLElement.prototype,["focus","blur"]);window.HTMLTemplateElement&&
E(window.HTMLTemplateElement.prototype,["innerHTML"]);Sa?E(DocumentFragment.prototype,Za):Wa(DocumentFragment.prototype,a);E(DocumentFragment.prototype,$a);Sa?(E(Document.prototype,Za),E(Document.prototype,["activeElement"])):Wa(Document.prototype,a);E(Document.prototype,["importNode","getElementById","elementFromPoint",ka()]);E(Document.prototype,$a)};var bb=C({get childNodes(){return this.__shady_childNodes},get firstChild(){return this.__shady_firstChild},get lastChild(){return this.__shady_lastChild},get childElementCount(){return this.__shady_childElementCount},get children(){return this.__shady_children},get firstElementChild(){return this.__shady_firstElementChild},get lastElementChild(){return this.__shady_lastElementChild},get shadowRoot(){return this.__shady_shadowRoot}}),cb=C({get textContent(){return this.__shady_textContent},set textContent(a){this.__shady_textContent=
a},get innerHTML(){return this.__shady_innerHTML},set innerHTML(a){this.__shady_innerHTML=a}}),db=C({get parentElement(){return this.__shady_parentElement},get parentNode(){return this.__shady_parentNode},get nextSibling(){return this.__shady_nextSibling},get previousSibling(){return this.__shady_previousSibling},get nextElementSibling(){return this.__shady_nextElementSibling},get previousElementSibling(){return this.__shady_previousElementSibling},get className(){return this.__shady_className},set className(a){this.__shady_className=
a}});function eb(a){for(var b in a){var c=a[b];c&&(c.enumerable=!1)}}eb(bb);eb(cb);eb(db);var fb=w.j||w.noPatch===!0,gb=fb?function(){}:function(a){var b=u(a);b.Ta||(b.Ta=!0,xa(a,db))},hb=fb?function(){}:function(a){var b=u(a);b.Sa||(b.Sa=!0,xa(a,bb),window.customElements&&window.customElements.polyfillWrapFlushCallback&&!w.noPatch||xa(a,cb))};var ib="__eventWrappers"+Date.now(),jb=function(){var a=Object.getOwnPropertyDescriptor(Event.prototype,"composed");return a?function(b){return a.get.call(b)}:null}(),kb=function(){function a(){}var b=!1,c={get capture(){b=!0;return!1}};window.addEventListener("test",a,c);window.removeEventListener("test",a,c);return b}();function lb(a){if(a===null||typeof a!=="object"&&typeof a!=="function"){var b=!!a;var c=!1}else{b=!!a.capture;c=!!a.once;var d=a.H}return{Na:d,capture:b,once:c,Ma:kb?a:b}}
var mb={blur:!0,focus:!0,focusin:!0,focusout:!0,click:!0,dblclick:!0,mousedown:!0,mouseenter:!0,mouseleave:!0,mousemove:!0,mouseout:!0,mouseover:!0,mouseup:!0,wheel:!0,beforeinput:!0,input:!0,keydown:!0,keyup:!0,compositionstart:!0,compositionupdate:!0,compositionend:!0,touchstart:!0,touchend:!0,touchmove:!0,touchcancel:!0,pointerover:!0,pointerenter:!0,pointerdown:!0,pointermove:!0,pointerup:!0,pointercancel:!0,pointerout:!0,pointerleave:!0,gotpointercapture:!0,lostpointercapture:!0,dragstart:!0,
drag:!0,dragenter:!0,dragleave:!0,dragover:!0,drop:!0,dragend:!0,DOMActivate:!0,DOMFocusIn:!0,DOMFocusOut:!0,keypress:!0},nb={DOMAttrModified:!0,DOMAttributeNameChanged:!0,DOMCharacterDataModified:!0,DOMElementNameChanged:!0,DOMNodeInserted:!0,DOMNodeInsertedIntoDocument:!0,DOMNodeRemoved:!0,DOMNodeRemovedFromDocument:!0,DOMSubtreeModified:!0};function ob(a){return a instanceof Node?a.__shady_getRootNode():a}
function pb(a,b){var c=[],d=a;for(a=ob(a);d;)c.push(d),d=d.__shady_assignedSlot?d.__shady_assignedSlot:d.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&d.host&&(b||d!==a)?d.host:d.__shady_parentNode;c[c.length-1]===document&&c.push(window);return c}function qb(a){a.__composedPath||(a.__composedPath=pb(a.target,!0));return a.__composedPath}function rb(a,b){if(!y)return a;a=pb(a,!0);for(var c=0,d,e=void 0,f,g=void 0;c<b.length;c++)if(d=b[c],f=ob(d),f!==e&&(g=a.indexOf(f),e=f),!y(f)||g>-1)return d}
var sb={get composed(){this.__composed===void 0&&(jb?this.__composed=this.type==="focusin"||this.type==="focusout"||jb(this):this.isTrusted!==!1&&(this.__composed=mb[this.type]));return this.__composed||!1},composedPath:function(){this.__composedPath||(this.__composedPath=pb(this.__target,this.composed));return this.__composedPath},get target(){return rb(this.currentTarget||this.__previousCurrentTarget,this.composedPath())},get relatedTarget(){if(!this.__relatedTarget)return null;this.__relatedTargetComposedPath||
(this.__relatedTargetComposedPath=pb(this.__relatedTarget,!0));return rb(this.currentTarget||this.__previousCurrentTarget,this.__relatedTargetComposedPath)},stopPropagation:function(){Event.prototype.stopPropagation.call(this);this.ga=!0},stopImmediatePropagation:function(){Event.prototype.stopImmediatePropagation.call(this);this.ga=this.__immediatePropagationStopped=!0}},tb=w.j&&Object.getOwnPropertyDescriptor(Event.prototype,"eventPhase");
tb&&(Object.defineProperty(sb,"eventPhase",{get:function(){return this.currentTarget===this.target?Event.AT_TARGET:this.__shady_native_eventPhase},enumerable:!0,configurable:!0}),Object.defineProperty(sb,"__shady_native_eventPhase",tb));function ub(a){function b(c,d){c=new a(c,d);c.__composed=d&&!!d.composed;return c}b.__proto__=a;b.prototype=a.prototype;return b}var vb={focus:!0,blur:!0};function wb(a){return a.__target!==a.target||a.__relatedTarget!==a.relatedTarget}
function xb(a,b,c){if(c=b.__handlers&&b.__handlers[a.type]&&b.__handlers[a.type][c])for(var d=0,e;(e=c[d])&&(!wb(a)||a.target!==a.relatedTarget)&&(e.call(b,a),!a.__immediatePropagationStopped);d++);}var yb=(new Event("e")).hasOwnProperty("currentTarget");
function zb(a){a=yb?Object.create(a):a;var b=a.composedPath(),c=b.map(function(n){return rb(n,b)}),d=a.bubbles,e=Object.getOwnPropertyDescriptor(a,"currentTarget");Object.defineProperty(a,"currentTarget",{configurable:!0,enumerable:!0,get:function(){return l}});var f=Event.CAPTURING_PHASE,g=Object.getOwnPropertyDescriptor(a,"eventPhase");Object.defineProperty(a,"eventPhase",{configurable:!0,enumerable:!0,get:function(){return f}});try{for(var h=b.length-1;h>=0;h--){var l=b[h];f=l===c[h]?Event.AT_TARGET:
Event.CAPTURING_PHASE;xb(a,l,"capture");if(a.ga)return}for(h=0;h<b.length;h++){l=b[h];var m=l===c[h];if(m||d)if(f=m?Event.AT_TARGET:Event.BUBBLING_PHASE,xb(a,l,"bubble"),a.ga)break}}finally{yb||(e?Object.defineProperty(a,"currentTarget",e):delete a.currentTarget,g?Object.defineProperty(a,"eventPhase",g):delete a.eventPhase)}}function Ab(a,b,c,d){for(var e=0;e<a.length;e++){var f=a[e],g=f.type,h=f.capture;if(b===f.node&&c===g&&d===h)return e}return-1}
function Bb(a){Fa();return!w.S&&this instanceof Node&&!ra(document,this)?(a.__target||Cb(a,this),zb(a)):this.__shady_native_dispatchEvent(a)}
function Db(a,b,c){var d=this,e=lb(c),f=e.capture,g=e.once,h=e.Na;e=e.Ma;if(b){var l=typeof b;if(l==="function"||l==="object")if(l!=="object"||b.handleEvent&&typeof b.handleEvent==="function"){if(nb[a])return this.__shady_native_addEventListener(a,b,e);var m=h||this;if(h=b[ib]){if(Ab(h,m,a,f)>-1)return}else b[ib]=[];h=function(n){g&&d.__shady_removeEventListener(a,b,c);n.__target||Cb(n);if(m!==d){var t=Object.getOwnPropertyDescriptor(n,"currentTarget");Object.defineProperty(n,"currentTarget",{get:function(){return m},
configurable:!0});var A=Object.getOwnPropertyDescriptor(n,"eventPhase");Object.defineProperty(n,"eventPhase",{configurable:!0,enumerable:!0,get:function(){return f?Event.CAPTURING_PHASE:Event.BUBBLING_PHASE}})}n.__previousCurrentTarget=n.currentTarget;if(!y(m)&&m.localName!=="slot"||n.composedPath().indexOf(m)!=-1)if(n.composed||n.composedPath().indexOf(m)>-1)if(wb(n)&&n.target===n.relatedTarget)n.eventPhase===Event.BUBBLING_PHASE&&n.stopImmediatePropagation();else if(n.eventPhase===Event.CAPTURING_PHASE||
n.bubbles||n.target===m||m instanceof Window){var ia=l==="function"?b.call(m,n):b.handleEvent&&b.handleEvent(n);m!==d&&(t?(Object.defineProperty(n,"currentTarget",t),t=null):delete n.currentTarget,A?(Object.defineProperty(n,"eventPhase",A),A=null):delete n.eventPhase);return ia}};b[ib].push({node:m,type:a,capture:f,ec:h});this.__handlers=this.__handlers||{};this.__handlers[a]=this.__handlers[a]||{capture:[],bubble:[]};this.__handlers[a][f?"capture":"bubble"].push(h);vb[a]||this.__shady_native_addEventListener(a,
h,e)}}}function Eb(a,b,c){if(b){var d=lb(c);c=d.capture;var e=d.Na;d=d.Ma;if(nb[a])return this.__shady_native_removeEventListener(a,b,d);var f=e||this;e=void 0;var g=null;try{g=b[ib]}catch(h){}g&&(f=Ab(g,f,a,c),f>-1&&(e=g.splice(f,1)[0].ec,g.length||(b[ib]=void 0)));this.__shady_native_removeEventListener(a,e||b,d);e&&this.__handlers&&this.__handlers[a]&&(a=this.__handlers[a][c?"capture":"bubble"],b=a.indexOf(e),b>-1&&a.splice(b,1))}}
function Fb(){for(var a in vb)window.__shady_native_addEventListener(a,function(b){b.__target||(Cb(b),zb(b))},!0)}var Gb=C(sb);function Cb(a,b){b=b===void 0?a.target:b;a.__target=b;a.__relatedTarget=a.relatedTarget;if(w.j){b=Object.getPrototypeOf(a);if(!b.hasOwnProperty("__shady_patchedProto")){var c=Object.create(b);c.__shady_sourceProto=b;B(c,Gb);b.__shady_patchedProto=c}a.__proto__=b.__shady_patchedProto}else B(a,Gb)}var Hb=ub(Event),Ib=ub(CustomEvent),Jb=ub(MouseEvent);
function Kb(){if(!jb&&Object.getOwnPropertyDescriptor(Event.prototype,"isTrusted")){var a=function(){var b=new MouseEvent("click",{bubbles:!0,cancelable:!0,composed:!0});this.__shady_dispatchEvent(b)};Element.prototype.click?Element.prototype.click=a:HTMLElement.prototype.click&&(HTMLElement.prototype.click=a)}}
var Lb=Object.getOwnPropertyNames(Element.prototype).filter(function(a){return a.substring(0,2)==="on"}),Mb=Object.getOwnPropertyNames(HTMLElement.prototype).filter(function(a){return a.substring(0,2)==="on"});function Nb(a){return{set:function(b){var c=u(this),d=a.substring(2);c.G||(c.G={});c.G[a]&&this.removeEventListener(d,c.G[a]);this.__shady_addEventListener(d,b);c.G[a]=b},get:function(){var b=v(this);return b&&b.G&&b.G[a]},configurable:!0}};function Ob(a,b){return{index:a,T:[],ba:b}}
function Pb(a,b,c,d){var e=0,f=0,g=0,h=0,l=Math.min(b-e,d-f);if(e==0&&f==0)a:{for(g=0;g<l;g++)if(a[g]!==c[g])break a;g=l}if(b==a.length&&d==c.length){h=a.length;for(var m=c.length,n=0;n<l-g&&Qb(a[--h],c[--m]);)n++;h=n}e+=g;f+=g;b-=h;d-=h;if(b-e==0&&d-f==0)return[];if(e==b){for(b=Ob(e,0);f<d;)b.T.push(c[f++]);return[b]}if(f==d)return[Ob(e,b-e)];l=e;g=f;d=d-g+1;h=b-l+1;b=Array(d);for(m=0;m<d;m++)b[m]=Array(h),b[m][0]=m;for(m=0;m<h;m++)b[0][m]=m;for(m=1;m<d;m++)for(n=1;n<h;n++)if(a[l+n-1]===c[g+m-1])b[m][n]=
b[m-1][n-1];else{var t=b[m-1][n]+1,A=b[m][n-1]+1;b[m][n]=t<A?t:A}l=b.length-1;g=b[0].length-1;d=b[l][g];for(a=[];l>0||g>0;)l==0?(a.push(2),g--):g==0?(a.push(3),l--):(h=b[l-1][g-1],m=b[l-1][g],n=b[l][g-1],t=m<n?m<h?m:h:n<h?n:h,t==h?(h==d?a.push(0):(a.push(1),d=h),l--,g--):t==m?(a.push(3),l--,d=m):(a.push(2),g--,d=n));a.reverse();b=void 0;l=[];for(g=0;g<a.length;g++)switch(a[g]){case 0:b&&(l.push(b),b=void 0);e++;f++;break;case 1:b||(b=Ob(e,0));b.ba++;e++;b.T.push(c[f]);f++;break;case 2:b||(b=Ob(e,
0));b.ba++;e++;break;case 3:b||(b=Ob(e,0)),b.T.push(c[f]),f++}b&&l.push(b);return l}function Qb(a,b){return a===b};var Rb=C({dispatchEvent:Bb,addEventListener:Db,removeEventListener:Eb});var Sb=null;function H(){Sb||(Sb=window.ShadyCSS&&window.ShadyCSS.ScopingShim);return Sb||null}function Tb(a,b,c){var d=H();return d&&b==="class"?(d.setElementClass(a,c),!0):!1}function Ub(a,b){var c=H();c&&c.unscopeNode(a,b)}function Vb(a,b){var c=H();if(!c)return!0;if(a.nodeType===Node.DOCUMENT_FRAGMENT_NODE){c=!0;for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling)c=c&&Vb(a,b);return c}return a.nodeType!==Node.ELEMENT_NODE?!0:c.currentScopeForNode(a)===b}
function Wb(a){if(a.nodeType!==Node.ELEMENT_NODE)return"";var b=H();return b?b.currentScopeForNode(a):""}function Xb(a,b){if(a)for(a.nodeType===Node.ELEMENT_NODE&&b(a),a=a.__shady_firstChild;a;a=a.__shady_nextSibling)a.nodeType===Node.ELEMENT_NODE&&Xb(a,b)};var Yb=window.document,Zb=w.S,$b=Object.getOwnPropertyDescriptor(Node.prototype,"isConnected"),ac=$b&&$b.get;function bc(a){for(var b;b=a.__shady_firstChild;)a.__shady_removeChild(b)}function cc(a){var b=v(a);if(b&&b.fa!==void 0)for(b=a.__shady_firstChild;b;b=b.__shady_nextSibling)cc(b);if(a=v(a))a.fa=void 0}function dc(a){var b=a;if(a&&a.localName==="slot"){var c=v(a);(c=c&&c.L)&&(b=c.length?c[0]:dc(a.__shady_nextSibling))}return b}
function ec(a,b,c){if(a=(a=v(a))&&a.R){if(b)if(b.nodeType===Node.DOCUMENT_FRAGMENT_NODE)for(var d=0,e=b.childNodes.length;d<e;d++)a.addedNodes.push(b.childNodes[d]);else a.addedNodes.push(b);c&&a.removedNodes.push(c);Ha(a)}}
var ic=C({get parentNode(){var a=v(this);a=a&&a.parentNode;return a!==void 0?a:this.__shady_native_parentNode},get firstChild(){var a=v(this);a=a&&a.firstChild;return a!==void 0?a:this.__shady_native_firstChild},get lastChild(){var a=v(this);a=a&&a.lastChild;return a!==void 0?a:this.__shady_native_lastChild},get nextSibling(){var a=v(this);a=a&&a.nextSibling;return a!==void 0?a:this.__shady_native_nextSibling},get previousSibling(){var a=v(this);a=a&&a.previousSibling;return a!==void 0?a:this.__shady_native_previousSibling},
get childNodes(){if(x(this)){var a=v(this);if(!a.childNodes){a.childNodes=[];for(var b=this.__shady_firstChild;b;b=b.__shady_nextSibling)a.childNodes.push(b)}var c=a.childNodes}else c=this.__shady_native_childNodes;c.item=function(d){return c[d]};return c},get parentElement(){var a=v(this);(a=a&&a.parentNode)&&a.nodeType!==Node.ELEMENT_NODE&&(a=null);return a!==void 0?a:this.__shady_native_parentElement},get isConnected(){if(ac&&ac.call(this))return!0;if(this.nodeType==Node.DOCUMENT_FRAGMENT_NODE)return!1;
var a=this.ownerDocument;if(a===null||ra(a,this))return!0;for(a=this;a&&!(a instanceof Document);)a=a.__shady_parentNode||(y(a)?a.host:void 0);return!!(a&&a instanceof Document)},get textContent(){if(x(this)){for(var a=[],b=this.__shady_firstChild;b;b=b.__shady_nextSibling)b.nodeType!==Node.COMMENT_NODE&&a.push(b.__shady_textContent);return a.join("")}return this.__shady_native_textContent},set textContent(a){if(typeof a==="undefined"||a===null)a="";switch(this.nodeType){case Node.ELEMENT_NODE:case Node.DOCUMENT_FRAGMENT_NODE:if(!x(this)&&
w.j){var b=this.__shady_firstChild;(b!=this.__shady_lastChild||b&&b.nodeType!=Node.TEXT_NODE)&&bc(this);this.__shady_native_textContent=a}else bc(this),(a.length>0||this.nodeType===Node.ELEMENT_NODE)&&this.__shady_insertBefore(document.createTextNode(a));break;default:this.nodeValue=a}},insertBefore:function(a,b){if(this.ownerDocument!==Yb&&a.ownerDocument!==Yb)return this.__shady_native_insertBefore(a,b),a;if(a===this)throw Error("Failed to execute 'appendChild' on 'Node': The new child element contains the parent.");
if(b){var c=v(b);c=c&&c.parentNode;if(c!==void 0&&c!==this||c===void 0&&b.__shady_native_parentNode!==this)throw Error("Failed to execute 'insertBefore' on 'Node': The node before which the new node is to be inserted is not a child of this node.");}if(b===a)return a;ec(this,a);var d=[],e=(c=I(this))?c.host.localName:Wb(this),f=a.__shady_parentNode;if(f){var g=Wb(a);var h=!!c||!I(a)||Zb&&this.__noInsertionPoint!==void 0;f.__shady_removeChild(a,h)}f=!0;var l=(!Zb||a.__noInsertionPoint===void 0&&this.__noInsertionPoint===
void 0)&&!Vb(a,e),m=c&&!a.__noInsertionPoint&&(!Zb||a.nodeType===Node.DOCUMENT_FRAGMENT_NODE);if(m||l)l&&(g=g||Wb(a)),Xb(a,function(n){m&&n.localName==="slot"&&d.push(n);if(l){var t=g;H()&&(t&&Ub(n,t),(t=H())&&t.scopeNode(n,e))}});d.length&&(c.ta(d),c.u());x(this)&&(fc(a,this,b),h=v(this),h.root?(f=!1,la(this)&&h.root.u()):c&&this.localName==="slot"&&(f=!1,c.u()));f?(c=y(this)?this.host:this,b?(b=dc(b),c.__shady_native_insertBefore(a,b)):c.__shady_native_appendChild(a)):a.ownerDocument!==this.ownerDocument&&
this.ownerDocument.adoptNode(a);return a},appendChild:function(a){if(this!=a||!y(a))return this.__shady_insertBefore(a)},removeChild:function(a,b){b=b===void 0?!1:b;if(this.ownerDocument!==Yb)return this.__shady_native_removeChild(a);if(a.__shady_parentNode!==this)throw Error("The node to be removed is not a child of this node: "+a);ec(this,null,a);var c=I(a),d=c&&c.yb(a),e=v(this);if(x(this)&&(hc(a,this),la(this))){e.root.u();var f=!0}if(H()&&!b&&c&&a.nodeType!==Node.TEXT_NODE){var g=Wb(a);Xb(a,
function(h){Ub(h,g)})}cc(a);c&&((b=this.localName==="slot")&&(f=!0),(d||b)&&c.u());f||(f=y(this)?this.host:this,(!e.root&&a.localName!=="slot"||f===a.__shady_native_parentNode)&&f.__shady_native_removeChild(a));return a},replaceChild:function(a,b){this.__shady_insertBefore(a,b);this.__shady_removeChild(b);return a},cloneNode:function(a){if(this.localName=="template")return this.__shady_native_cloneNode(a);var b=this.__shady_native_cloneNode(!1);if(a&&b.nodeType!==Node.ATTRIBUTE_NODE){a=this.__shady_firstChild;
for(var c;a;a=a.__shady_nextSibling)c=a.__shady_cloneNode(!0),b.__shady_appendChild(c)}return b},getRootNode:function(a){if(this&&this.nodeType){var b=u(this),c=b.fa;c===void 0&&(y(this)?(c=this,b.fa=c):(c=(c=this.__shady_parentNode)?c.__shady_getRootNode(a):this,document.documentElement.__shady_native_contains(this)&&(b.fa=c)));return c}},contains:function(a){return sa(this,a)}});var jc=C({get assignedSlot(){var a=this.__shady_parentNode;(a=a&&a.__shady_shadowRoot)&&a.X();return(a=v(this))&&a.assignedSlot||null}});/*

 Copyright (c) 2022 The Polymer Project Authors
 SPDX-License-Identifier: BSD-3-Clause
*/
var kc=new Map;[["(",{end:")",ea:!0}],["[",{end:"]",ea:!0}],['"',{end:'"',ea:!1}],["'",{end:"'",ea:!1}]].forEach(function(a){var b=p(a);a=b.next().value;b=b.next().value;kc.set(a,b)});function lc(a,b,c,d){for(d=d===void 0?!0:d;b<a.length;b++)if(a[b]==="\\"&&b<a.length-1&&a[b+1]!=="\n")b++;else{if(c.indexOf(a[b])!==-1)return b;if(d&&kc.has(a[b])){var e=kc.get(a[b]);b=lc(a,b+1,[e.end],e.ea)}}return a.length}
function mc(a){function b(){if(d.length>0){for(;d[d.length-1]===" ";)d.pop();c.push({Ka:d.filter(function(l,m){return m%2===0}),Ob:d.filter(function(l,m){return m%2===1})});d.length=0}}for(var c=[],d=[],e=0;e<a.length;){var f=d[d.length-1],g=lc(a,e,[","," ",">","+","~"]),h=g===e?a[e]:a.substring(e,g);if(h===",")b();else if([void 0," ",">","+","~"].indexOf(f)===-1||h!==" ")f===" "&&[">","+","~"].indexOf(h)!==-1?d[d.length-1]=h:d.push(h);e=g+(g===e?1:0)}b();return c};function nc(a,b,c){var d=[];oc(a,b,c,d);return d}function oc(a,b,c,d){for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling){var e;if(e=a.nodeType===Node.ELEMENT_NODE){e=a;var f=b,g=c,h=d,l=f(e);l&&h.push(e);g&&g(l)?e=l:(oc(e,f,g,h),e=void 0)}if(e)break}}
var pc={get firstElementChild(){var a=v(this);if(a&&a.firstChild!==void 0){for(a=this.__shady_firstChild;a&&a.nodeType!==Node.ELEMENT_NODE;)a=a.__shady_nextSibling;return a}return this.__shady_native_firstElementChild},get lastElementChild(){var a=v(this);if(a&&a.lastChild!==void 0){for(a=this.__shady_lastChild;a&&a.nodeType!==Node.ELEMENT_NODE;)a=a.__shady_previousSibling;return a}return this.__shady_native_lastElementChild},get children(){return x(this)?ta(Array.prototype.filter.call(va(this),function(a){return a.nodeType===
Node.ELEMENT_NODE})):this.__shady_native_children},get childElementCount(){var a=this.__shady_children;return a?a.length:0}},J=C((pc.append=function(){this.__shady_insertBefore(D.apply(null,q(r.apply(0,arguments))),null)},pc.prepend=function(){this.__shady_insertBefore(D.apply(null,q(r.apply(0,arguments))),this.__shady_firstChild)},pc.replaceChildren=function(){for(var a=r.apply(0,arguments),b;(b=this.__shady_firstChild)!==null;)this.__shady_removeChild(b);this.__shady_insertBefore(D.apply(null,q(a)),
null)},pc));
function qc(a,b){function c(e,f){return(e===a||f.indexOf(":scope")===-1)&&ma.call(e,f)}var d=mc(b);if(d.length<1)return[];for(b=Aa(nc(a,function(){return!0}).map(function(e){return Aa(d.map(function(f){var g=f.Ka,h=g.length-1;return c(e,g[h])?{target:e,O:f,P:e,index:h}:[]}))}));b.some(function(e){return e.index>0});)b=Aa(b.map(function(e){if(e.index<=0)return e;var f=e.target,g=e.P,h=e.O;e=e.index-1;var l=h.Ob[e],m=h.Ka[e];if(l===" "){l=[];for(g=g.__shady_parentElement;g;g=g.__shady_parentElement)c(g,m)&&
l.push({target:f,O:h,P:g,index:e});return l}if(l===">")return g=g.__shady_parentElement,c(g,m)?{target:f,O:h,P:g,index:e}:[];if(l==="+")return(g=g.__shady_previousElementSibling)&&c(g,m)?{target:f,O:h,P:g,index:e}:[];if(l==="~"){l=[];for(g=g.__shady_previousElementSibling;g;g=g.__shady_previousElementSibling)c(g,m)&&l.push({target:f,O:h,P:g,index:e});return l}throw Error("Unrecognized combinator: '"+l+"'.");}));return Ba(b.map(function(e){return e.target}))}
var K=w.querySelectorImplementation,rc=C({querySelector:function(a){if(K==="native"){var b=Array.prototype.slice.call((this instanceof ShadowRoot?this.host:this).__shady_native_querySelectorAll(a)),c=this.__shady_getRootNode();b=p(b);for(var d=b.next();!d.done;d=b.next())if(d=d.value,d.__shady_getRootNode()==c)return d;return null}if(K==="selectorEngine")return qc(this,a)[0]||null;if(K===void 0)return nc(this,function(e){return ma.call(e,a)},function(e){return!!e})[0]||null;throw Error("Unrecognized value of ShadyDOM.querySelectorImplementation: '"+
(K+"'"));},querySelectorAll:function(a,b){if(b||K==="native"){b=Array.prototype.slice.call((this instanceof ShadowRoot?this.host:this).__shady_native_querySelectorAll(a));var c=this.__shady_getRootNode();return ta(b.filter(function(d){return d.__shady_getRootNode()==c}))}if(K==="selectorEngine")return ta(qc(this,a));if(K===void 0)return ta(nc(this,function(d){return ma.call(d,a)}));throw Error("Unrecognized value of ShadyDOM.querySelectorImplementation: '"+(K+"'"));}}),sc=w.S&&!w.noPatch?ya({},J):
J;ya(J,rc);/*

Copyright (c) 2020 The Polymer Project Authors. All rights reserved.
This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
Code distributed by Google as part of the polymer project is also
subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
var tc=C({after:function(){var a=this.__shady_parentNode;if(a!==null){var b=this.__shady_nextSibling;a.__shady_insertBefore(D.apply(null,q(r.apply(0,arguments))),b)}},before:function(){var a=this.__shady_parentNode;a!==null&&a.__shady_insertBefore(D.apply(null,q(r.apply(0,arguments))),this)},remove:function(){var a=this.__shady_parentNode;a!==null&&a.__shady_removeChild(this)},replaceWith:function(){var a=r.apply(0,arguments),b=this.__shady_parentNode;if(b!==null){var c=this.__shady_nextSibling;b.__shady_removeChild(this);
b.__shady_insertBefore(D.apply(null,q(a)),c)}}});var uc=window.document;function vc(a,b){b==="slot"?(a=a.__shady_parentNode,la(a)&&v(a).root.u()):a.localName==="slot"&&b==="name"&&(b=I(a))&&(b.Lb(a),b.u())}
var wc=C({get previousElementSibling(){var a=v(this);if(a&&a.previousSibling!==void 0){for(a=this.__shady_previousSibling;a&&a.nodeType!==Node.ELEMENT_NODE;)a=a.__shady_previousSibling;return a}return this.__shady_native_previousElementSibling},get nextElementSibling(){var a=v(this);if(a&&a.nextSibling!==void 0){for(a=this.__shady_nextSibling;a&&a.nodeType!==Node.ELEMENT_NODE;)a=a.__shady_nextSibling;return a}return this.__shady_native_nextElementSibling},get slot(){return this.getAttribute("slot")},
set slot(a){this.__shady_setAttribute("slot",a)},get className(){return this.getAttribute("class")||""},set className(a){this.__shady_setAttribute("class",a)},setAttribute:function(a,b){this.ownerDocument!==uc?this.__shady_native_setAttribute(a,b):Tb(this,a,b)||(this.__shady_native_setAttribute(a,b),vc(this,a))},removeAttribute:function(a){this.ownerDocument!==uc?this.__shady_native_removeAttribute(a):Tb(this,a,"")?this.getAttribute(a)===""&&this.__shady_native_removeAttribute(a):(this.__shady_native_removeAttribute(a),
vc(this,a))},toggleAttribute:function(a,b){if(this.ownerDocument!==uc)return this.__shady_native_toggleAttribute(a,b);if(!Tb(this,a,""))return b=this.__shady_native_toggleAttribute(a,b),vc(this,a),b;if(this.getAttribute(a)===""&&!b)return this.__shady_native_toggleAttribute(a,b)}});w.S||Lb.forEach(function(a){wc[a]=Nb(a)});
var Ac=C({attachShadow:function(a){if(!this)throw Error("Must provide a host.");if(!a)throw Error("Not enough arguments.");if(a.shadyUpgradeFragment&&!w.Qa){var b=a.shadyUpgradeFragment;b.__proto__=ShadowRoot.prototype;b.za(this,a);xc(b,b);a=b.__noInsertionPoint?null:b.querySelectorAll("slot");b.__noInsertionPoint=void 0;a&&a.length&&(b.ta(a),b.u());b.host.__shady_native_appendChild(b)}else b=new yc(zc,this,a);return this.__CE_shadowRoot=b},get shadowRoot(){var a=v(this);return a&&a.Wb||null}});
ya(wc,Ac);var Bc=document.implementation.createHTMLDocument("inert"),Cc=C({get innerHTML(){return x(this)?Ra(this.localName==="template"?this.content:this,va):this.__shady_native_innerHTML},set innerHTML(a){if(this.localName==="template")this.__shady_native_innerHTML=a;else{bc(this);var b=this.localName||"div";b=this.namespaceURI&&this.namespaceURI!==Bc.namespaceURI?Bc.createElementNS(this.namespaceURI,b):Bc.createElement(b);for(w.j?b.__shady_native_innerHTML=a:b.innerHTML=a;a=b.__shady_firstChild;)this.__shady_insertBefore(a)}}});var Dc=C({blur:function(){var a=v(this);(a=(a=a&&a.root)&&a.activeElement)?a.__shady_blur():this.__shady_native_blur()}});w.S||Mb.forEach(function(a){Dc[a]=Nb(a)});var Ec=C({assignedNodes:function(a){if(this.localName==="slot"){var b=this.__shady_getRootNode();b&&y(b)&&b.X();return(b=v(this))?(a&&a.flatten?b.L:b.assignedNodes)||[]:[]}},addEventListener:function(a,b,c){if(this.localName!=="slot"||a==="slotchange")Db.call(this,a,b,c);else{typeof c!=="object"&&(c={capture:!!c});var d=this.__shady_parentNode;if(!d)throw Error("ShadyDOM cannot attach event to slot unless it has a `parentNode`");c.H=this;d.__shady_addEventListener(a,b,c)}},removeEventListener:function(a,
b,c){if(this.localName!=="slot"||a==="slotchange")Eb.call(this,a,b,c);else{typeof c!=="object"&&(c={capture:!!c});var d=this.__shady_parentNode;if(!d)throw Error("ShadyDOM cannot attach event to slot unless it has a `parentNode`");c.H=this;d.__shady_removeEventListener(a,b,c)}}});var Fc=C({getElementById:function(a){return a===""?null:nc(this,function(b){return b.id==a},function(b){return!!b})[0]||null}});function Gc(a,b){for(var c;b&&!a.has(c=b.__shady_getRootNode());)b=c.host;return b}function Hc(a){var b=new Set;for(b.add(a);y(a)&&a.host;)a=a.host.__shady_getRootNode(),b.add(a);return b}
var Ic="__shady_native_"+ka(),Jc=C({get activeElement(){var a=w.j?document.__shady_native_activeElement:document.activeElement;if(!a||!a.nodeType)return null;var b=!!y(this);if(!(this===document||b&&this.host!==a&&this.host.__shady_native_contains(a)))return null;for(b=I(a);b&&b!==this;)a=b.host,b=I(a);return this===document?b?null:a:b===this?a:null},elementsFromPoint:function(a,b){a=document[Ic](a,b);if(this===document&&w.useNativeDocumentEFP)return a;a=[].slice.call(a);b=Hc(this);for(var c=new Set,
d=0;d<a.length;d++)c.add(Gc(b,a[d]));var e=[];c.forEach(function(f){return e.push(f)});return e},elementFromPoint:function(a,b){return this===document&&w.useNativeDocumentEFP?this.__shady_native_elementFromPoint(a,b):this.__shady_elementsFromPoint(a,b)[0]||null}});var Kc=window.document,Lc=C({importNode:function(a,b){if(a.ownerDocument!==Kc||a.localName==="template")return this.__shady_native_importNode(a,b);var c=this.__shady_native_importNode(a,!1);if(b)for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling)b=this.__shady_importNode(a,!0),c.__shady_appendChild(b);return c}});var Mc=C({dispatchEvent:Bb,addEventListener:Db.bind(window),removeEventListener:Eb.bind(window)});var L={};Object.getOwnPropertyDescriptor(HTMLElement.prototype,"parentElement")&&(L.parentElement=ic.parentElement);Object.getOwnPropertyDescriptor(HTMLElement.prototype,"contains")&&(L.contains=ic.contains);Object.getOwnPropertyDescriptor(HTMLElement.prototype,"children")&&(L.children=J.children);Object.getOwnPropertyDescriptor(HTMLElement.prototype,"innerHTML")&&(L.innerHTML=Cc.innerHTML);Object.getOwnPropertyDescriptor(HTMLElement.prototype,"className")&&(L.className=wc.className);
var M={EventTarget:[Rb],Node:[ic,window.EventTarget?null:Rb],Text:[jc],Comment:[jc],CDATASection:[jc],ProcessingInstruction:[jc],Element:[wc,J,tc,jc,!w.j||"innerHTML"in Element.prototype?Cc:null,window.HTMLSlotElement?null:Ec],HTMLElement:[Dc,L],HTMLSlotElement:[Ec],DocumentFragment:[sc,Fc],Document:[Lc,sc,Fc,Jc],Window:[Mc],CharacterData:[tc],XMLHttpRequest:[window.EventTarget?null:Rb]},Nc=w.j?null:["innerHTML","textContent"];function N(a,b,c,d){b.forEach(function(e){return a&&e&&B(a,e,c,d)})}
function Oc(a){var b=a?null:Nc,c;for(c in M)N(window[c]&&window[c].prototype,M[c],a,b)}["Text","Comment","CDATASection","ProcessingInstruction"].forEach(function(a){var b=window[a],c=Object.create(b.prototype);c.__shady_protoIsPatched=!0;N(c,M.EventTarget);N(c,M.Node);M[a]&&N(c,M[a]);b.prototype.__shady_patchedProto=c});function Pc(a){a.__shady_protoIsPatched=!0;N(a,M.EventTarget);N(a,M.Node);N(a,M.Element);N(a,M.HTMLElement);N(a,M.HTMLSlotElement);return a};var Qc=w.qa,Rc=w.j;function Sc(a,b){if(Qc&&!a.__shady_protoIsPatched&&!y(a)){var c=Object.getPrototypeOf(a),d=c.hasOwnProperty("__shady_patchedProto")&&c.__shady_patchedProto;d||(d=Object.create(c),Pc(d),c.__shady_patchedProto=d);Object.setPrototypeOf(a,d)}Rc||(b===1?gb(a):b===2&&hb(a))}
function Tc(a,b,c,d){Sc(a,1);d=d||null;var e=u(a),f=d?u(d):null;e.previousSibling=d?f.previousSibling:b.__shady_lastChild;if(f=v(e.previousSibling))f.nextSibling=a;if(f=v(e.nextSibling=d))f.previousSibling=a;e.parentNode=b;d?d===c.firstChild&&(c.firstChild=a):(c.lastChild=a,c.firstChild||(c.firstChild=a));c.childNodes=null}
function fc(a,b,c){Sc(b,2);var d=u(b);d.firstChild!==void 0&&(d.childNodes=null);if(a.nodeType===Node.DOCUMENT_FRAGMENT_NODE)for(a=a.__shady_native_firstChild;a;a=a.__shady_native_nextSibling)Tc(a,b,d,c);else Tc(a,b,d,c)}
function hc(a,b){var c=u(a);b=u(b);a===b.firstChild&&(b.firstChild=c.nextSibling);a===b.lastChild&&(b.lastChild=c.previousSibling);a=c.previousSibling;var d=c.nextSibling;a&&(u(a).nextSibling=d);d&&(u(d).previousSibling=a);c.parentNode=c.previousSibling=c.nextSibling=void 0;b.childNodes!==void 0&&(b.childNodes=null)}
function xc(a,b){var c=u(a);if(b||c.firstChild===void 0){c.childNodes=null;var d=c.firstChild=a.__shady_native_firstChild;c.lastChild=a.__shady_native_lastChild;Sc(a,2);c=d;for(d=void 0;c;c=c.__shady_native_nextSibling){var e=u(c);e.parentNode=b||a;e.nextSibling=c.__shady_native_nextSibling;e.previousSibling=d||null;d=c;Sc(c,1)}}};var Uc=C({addEventListener:function(a,b,c){typeof c!=="object"&&(c={capture:!!c});c.H=c.H||this;this.host.__shady_addEventListener(a,b,c)},removeEventListener:function(a,b,c){typeof c!=="object"&&(c={capture:!!c});c.H=c.H||this;this.host.__shady_removeEventListener(a,b,c)}});function Vc(a,b){B(a,Uc,b);B(a,Jc,b);B(a,Cc,b);B(a,J,b);w.noPatch&&!b?(B(a,ic,b),B(a,Fc,b)):w.j||(B(a,db),B(a,bb),B(a,cb))};var zc={},O=w.deferConnectionCallbacks&&document.readyState==="loading",Wc;function Xc(a){var b=[];do b.unshift(a);while(a=a.__shady_parentNode);return b}function yc(a,b,c){if(a!==zc)throw new TypeError("Illegal constructor");this.g=null;this.za(b,c)}k=yc.prototype;
k.za=function(a,b){this.host=a;this.mode=b&&b.mode;xc(this.host);a=u(this.host);a.root=this;a.Wb=this.mode!=="closed"?this:null;a=u(this);a.firstChild=a.lastChild=a.parentNode=a.nextSibling=a.previousSibling=null;if(w.preferPerformance)for(;a=this.host.__shady_native_firstChild;)this.host.__shady_native_removeChild(a);else this.u()};k.u=function(){var a=this;this.J||(this.J=!0,Ea(function(){return a.X()}))};k.mb=function(){for(var a,b=this;b;)b.J&&(a=b),b=b.lb();return a};
k.lb=function(){var a=this.host.__shady_getRootNode();if(y(a)){var b=v(this.host);if(b&&b.W>0)return a}};k.X=function(){var a=this.J&&this.mb();a&&a._renderSelf()};k.hb=function(){!this.ya&&this.J&&this.X()};
k._renderSelf=function(){var a=O;O=!0;this.J=!1;this.g&&(this.bb(),this.Za());if(!w.preferPerformance&&!this.ya)for(var b=this.host.__shady_firstChild;b;b=b.__shady_nextSibling){var c=v(b);b.__shady_native_parentNode!==this.host||b.localName!=="slot"&&c.assignedSlot||this.host.__shady_native_removeChild(b)}this.ya=!0;O=a;Wc&&Wc()};
k.bb=function(){this.aa();for(var a=0,b;a<this.g.length;a++)b=this.g[a],this.Ya(b);for(a=this.host.__shady_firstChild;a;a=a.__shady_nextSibling)this.va(a);for(a=0;a<this.g.length;a++){b=this.g[a];var c=v(b);if(!c.assignedNodes.length)for(var d=b.__shady_firstChild;d;d=d.__shady_nextSibling)this.va(d,b);(d=(d=v(b.__shady_parentNode))&&d.root)&&(d.xa()||d.J)&&d._renderSelf();this.sa(c.L,c.assignedNodes);if(d=c.Da){for(var e=0;e<d.length;e++)v(d[e]).ka=null;c.Da=null;d.length>c.assignedNodes.length&&
(c.oa=!0)}c.oa&&(c.oa=!1,this.wa(b))}};k.va=function(a,b){var c=u(a),d=c.ka;c.ka=null;b||(b=(b=this.i[a.__shady_slot||"__catchall"])&&b[0]);b?(u(b).assignedNodes.push(a),c.assignedSlot=b):c.assignedSlot=void 0;d!==c.assignedSlot&&c.assignedSlot&&(u(c.assignedSlot).oa=!0)};k.Ya=function(a){var b=v(a),c=b.assignedNodes;b.assignedNodes=[];b.L=[];if(b.Da=c)for(b=0;b<c.length;b++){var d=v(c[b]);d.ka=d.assignedSlot;d.assignedSlot===a&&(d.assignedSlot=null)}};
k.sa=function(a,b){for(var c=0,d=void 0;c<b.length&&(d=b[c]);c++)if(d.localName=="slot"){var e=v(d).assignedNodes;e&&e.length&&this.sa(a,e)}else a.push(b[c])};k.wa=function(a){a.__shady_native_dispatchEvent(new Event("slotchange"));a=v(a);a.assignedSlot&&this.wa(a.assignedSlot)};k.Za=function(){for(var a=this.g,b=[],c=0;c<a.length;c++){var d=a[c].__shady_parentNode,e=v(d);e&&e.root||!(b.indexOf(d)<0)||b.push(d)}for(a=0;a<b.length;a++)c=b[a],this.Kb(c===this?this.host:c,this.ab(c))};
k.ab=function(a){var b=[];for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling)if(this.ob(a))for(var c=v(a).L,d=0;d<c.length;d++)b.push(c[d]);else b.push(a);return b};k.ob=function(a){return a.localName=="slot"};
k.Kb=function(a,b){for(var c=ua(a),d=Pb(b,b.length,c,c.length),e=0,f=0,g=void 0;e<d.length&&(g=d[e]);e++){for(var h=0,l=void 0;h<g.T.length&&(l=g.T[h]);h++)l.__shady_native_parentNode===a&&a.__shady_native_removeChild(l),c.splice(g.index+f,1);f-=g.ba}e=0;for(f=void 0;e<d.length&&(f=d[e]);e++)for(g=c[f.index],h=f.index;h<f.index+f.ba;h++)l=b[h],a.__shady_native_insertBefore(l,g),c.splice(h,0,l)};k.gb=function(){this.F=this.F||[];this.g=this.g||[];this.i=this.i||{}};
k.ta=function(a){this.gb();this.F.push.apply(this.F,q(a))};k.aa=function(){this.F&&this.F.length&&(this.rb(this.F),this.F=[])};k.rb=function(a){for(var b,c=0;c<a.length;c++){var d=a[c];xc(d);var e=d.__shady_parentNode;xc(e);e=v(e);e.W=(e.W||0)+1;e=this.Ba(d);this.i[e]?(b=b||{},b[e]=!0,this.i[e].push(d)):this.i[e]=[d];this.g.push(d)}if(b)for(var f in b)this.i[f]=this.Fa(this.i[f])};k.Ba=function(a){var b=a.name||a.getAttribute("name")||"__catchall";return a.Ua=b};
k.Fa=function(a){return a.sort(function(b,c){b=Xc(b);for(var d=Xc(c),e=0;e<b.length;e++){c=b[e];var f=d[e];if(c!==f)return b=va(c.__shady_parentNode),b.indexOf(c)-b.indexOf(f)}})};k.yb=function(a){if(this.g){this.aa();var b=this.i,c;for(c in b)for(var d=b[c],e=0;e<d.length;e++){var f=d[e];if(sa(a,f)){d.splice(e,1);var g=this.g.indexOf(f);g>=0&&(this.g.splice(g,1),(g=v(f.__shady_parentNode))&&g.W&&g.W--);e--;this.zb(f);g=!0}}return g}};
k.Lb=function(a){if(this.g){this.aa();var b=a.Ua,c=this.Ba(a);if(c!==b){b=this.i[b];var d=b.indexOf(a);d>=0&&b.splice(d,1);b=this.i[c]||(this.i[c]=[]);b.push(a);b.length>1&&(this.i[c]=this.Fa(b))}}};k.zb=function(a){a=v(a);var b=a.L;if(b)for(var c=0;c<b.length;c++){var d=b[c],e=d.__shady_native_parentNode;e&&e.__shady_native_removeChild(d)}a.L=[];a.assignedNodes=[]};k.xa=function(){this.aa();return!(!this.g||!this.g.length)};
(function(a){a.__proto__=DocumentFragment.prototype;Vc(a,"__shady_");Vc(a);Object.defineProperties(a,{nodeType:{value:Node.DOCUMENT_FRAGMENT_NODE,configurable:!0},nodeName:{value:"#document-fragment",configurable:!0},nodeValue:{value:null,configurable:!0}});["localName","namespaceURI","prefix"].forEach(function(b){Object.defineProperty(a,b,{value:void 0,configurable:!0})});["ownerDocument","baseURI","isConnected"].forEach(function(b){Object.defineProperty(a,b,{get:function(){return this.host[b]},
configurable:!0})})})(yc.prototype);
if(window.customElements&&window.customElements.define&&w.inUse&&!w.preferPerformance){var Yc=new Map;Wc=function(){var a=[];Yc.forEach(function(d,e){a.push([e,d])});Yc.clear();for(var b=0;b<a.length;b++){var c=a[b][0];a[b][1]?c.__shadydom_connectedCallback():c.__shadydom_disconnectedCallback()}};O&&document.addEventListener("readystatechange",function(){O=!1;Wc()},{once:!0});var Zc=function(a,b,c){var d=0,e="__isConnected"+d++;if(b||c)a.prototype.connectedCallback=a.prototype.__shadydom_connectedCallback=
function(){O?Yc.set(this,!0):this[e]||(this[e]=!0,b&&b.call(this))},a.prototype.disconnectedCallback=a.prototype.__shadydom_disconnectedCallback=function(){O?this.isConnected||Yc.set(this,!1):this[e]&&(this[e]=!1,c&&c.call(this))};return a},$c=window.customElements.define,ad=function(a,b){var c=b.prototype.connectedCallback,d=b.prototype.disconnectedCallback;$c.call(window.customElements,a,Zc(b,c,d));b.prototype.connectedCallback=c;b.prototype.disconnectedCallback=d};window.customElements.define=
ad;Object.defineProperty(window.CustomElementRegistry.prototype,"define",{value:ad,configurable:!0})}function I(a){a=a.__shady_getRootNode();if(y(a))return a};function P(a){this.node=a}k=P.prototype;k.addEventListener=function(a,b,c){return this.node.__shady_addEventListener(a,b,c)};k.removeEventListener=function(a,b,c){return this.node.__shady_removeEventListener(a,b,c)};k.appendChild=function(a){return this.node.__shady_appendChild(a)};k.insertBefore=function(a,b){return this.node.__shady_insertBefore(a,b)};k.removeChild=function(a){return this.node.__shady_removeChild(a)};k.replaceChild=function(a,b){return this.node.__shady_replaceChild(a,b)};
k.cloneNode=function(a){return this.node.__shady_cloneNode(a)};k.getRootNode=function(a){return this.node.__shady_getRootNode(a)};k.contains=function(a){return this.node.__shady_contains(a)};k.dispatchEvent=function(a){return this.node.__shady_dispatchEvent(a)};k.setAttribute=function(a,b){this.node.__shady_setAttribute(a,b)};k.getAttribute=function(a){return this.node.__shady_native_getAttribute(a)};k.hasAttribute=function(a){return this.node.__shady_native_hasAttribute(a)};k.removeAttribute=function(a){this.node.__shady_removeAttribute(a)};
k.toggleAttribute=function(a,b){return this.node.__shady_toggleAttribute(a,b)};k.attachShadow=function(a){return this.node.__shady_attachShadow(a)};k.focus=function(){this.node.__shady_native_focus()};k.blur=function(){this.node.__shady_blur()};k.importNode=function(a,b){if(this.node.nodeType===Node.DOCUMENT_NODE)return this.node.__shady_importNode(a,b)};k.getElementById=function(a){if(this.node.nodeType===Node.DOCUMENT_NODE)return this.node.__shady_getElementById(a)};
k.elementsFromPoint=function(a,b){return this.node.__shady_elementsFromPoint(a,b)};k.elementFromPoint=function(a,b){return this.node.__shady_elementFromPoint(a,b)};k.querySelector=function(a){return this.node.__shady_querySelector(a)};k.querySelectorAll=function(a,b){return this.node.__shady_querySelectorAll(a,b)};k.assignedNodes=function(a){if(this.node.localName==="slot")return this.node.__shady_assignedNodes(a)};k.append=function(){return this.node.__shady_append.apply(this.node,q(r.apply(0,arguments)))};
k.prepend=function(){return this.node.__shady_prepend.apply(this.node,q(r.apply(0,arguments)))};k.replaceChildren=function(){return this.node.__shady_replaceChildren.apply(this.node,q(r.apply(0,arguments)))};k.after=function(){return this.node.__shady_after.apply(this.node,q(r.apply(0,arguments)))};k.before=function(){return this.node.__shady_before.apply(this.node,q(r.apply(0,arguments)))};k.remove=function(){return this.node.__shady_remove()};
k.replaceWith=function(){return this.node.__shady_replaceWith.apply(this.node,q(r.apply(0,arguments)))};
ca.Object.defineProperties(P.prototype,{activeElement:{configurable:!0,enumerable:!0,get:function(){if(y(this.node)||this.node.nodeType===Node.DOCUMENT_NODE)return this.node.__shady_activeElement}},_activeElement:{configurable:!0,enumerable:!0,get:function(){return this.activeElement}},host:{configurable:!0,enumerable:!0,get:function(){if(y(this.node))return this.node.host}},parentNode:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_parentNode}},firstChild:{configurable:!0,
enumerable:!0,get:function(){return this.node.__shady_firstChild}},lastChild:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_lastChild}},nextSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_nextSibling}},previousSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_previousSibling}},childNodes:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_childNodes}},parentElement:{configurable:!0,enumerable:!0,
get:function(){return this.node.__shady_parentElement}},firstElementChild:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_firstElementChild}},lastElementChild:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_lastElementChild}},nextElementSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_nextElementSibling}},previousElementSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_previousElementSibling}},
children:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_children}},childElementCount:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_childElementCount}},shadowRoot:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_shadowRoot}},assignedSlot:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_assignedSlot}},isConnected:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_isConnected}},innerHTML:{configurable:!0,
enumerable:!0,get:function(){return this.node.__shady_innerHTML},set:function(a){this.node.__shady_innerHTML=a}},textContent:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_textContent},set:function(a){this.node.__shady_textContent=a}},slot:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_slot},set:function(a){this.node.__shady_slot=a}},className:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_className},set:function(a){this.node.__shady_className=
a}}});function bd(a){Object.defineProperty(P.prototype,a,{get:function(){return this.node["__shady_"+a]},set:function(b){this.node["__shady_"+a]=b},configurable:!0})}Lb.forEach(function(a){return bd(a)});Mb.forEach(function(a){return bd(a)});var cd=new WeakMap;function dd(a){if(y(a)||a instanceof P)return a;var b=cd.get(a);b||(b=new P(a),cd.set(a,b));return b};if(w.inUse){var ed=w.j?function(a){return a}:function(a){hb(a);gb(a);return a};window.ShadyDOM={inUse:w.inUse,patch:ed,isShadyRoot:y,enqueue:Ea,flush:Fa,flushInitial:function(a){a.hb()},settings:w,filterMutations:Ka,observeChildren:Ia,unobserveChildren:Ja,deferConnectionCallbacks:w.deferConnectionCallbacks,preferPerformance:w.preferPerformance,handlesDynamicScoping:!0,wrap:w.noPatch?dd:ed,wrapIfNeeded:w.noPatch===!0?dd:function(a){return a},Wrapper:P,composedPath:qb,noPatch:w.noPatch,patchOnDemand:w.qa,
nativeMethods:Ta,nativeTree:Ua,patchElementProto:Pc,querySelectorImplementation:w.querySelectorImplementation};ab();Oc("__shady_");Object.defineProperty(document,"_activeElement",Jc.activeElement);B(Window.prototype,Mc,"__shady_");w.noPatch?w.qa&&B(Element.prototype,Ac):(Oc(),Kb());Fb();window.Event=Hb;window.CustomEvent=Ib;window.MouseEvent=Jb;window.ShadowRoot=yc};/*

Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
Code distributed by Google as part of the polymer project is also
subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
function fd(){this.end=this.start=0;this.rules=this.parent=this.previous=null;this.cssText=this.parsedCssText="";this.atRule=!1;this.type=0;this.parsedSelector=this.selector=this.keyframesName=""}
function gd(a){var b=a=a.replace(hd,"").replace(id,""),c=new fd;c.start=0;c.end=b.length;for(var d=c,e=0,f=b.length;e<f;e++)if(b[e]==="{"){d.rules||(d.rules=[]);var g=d,h=g.rules[g.rules.length-1]||null;d=new fd;d.start=e+1;d.parent=g;d.previous=h;g.rules.push(d)}else b[e]==="}"&&(d.end=e+1,d=d.parent||c);return jd(c,a)}
function jd(a,b){var c=b.substring(a.start,a.end-1);a.parsedCssText=a.cssText=c.trim();a.parent&&(c=b.substring(a.previous?a.previous.end:a.parent.start,a.start-1),c=_expandUnicodeEscapes$$module$third_party$javascript$polymer$v2$shadycss$src$css_parse(c),c=c.replace(kd," "),c=c.substring(c.lastIndexOf(";")+1),c=a.parsedSelector=a.selector=c.trim(),a.atRule=c.indexOf("@")===0,a.atRule?c.indexOf("@media")===0?a.type=4:c.match(ld)&&(a.type=7,a.keyframesName=a.selector.split(kd).pop()):a.type=c.indexOf("--")===
0?1E3:1);if(c=a.rules)for(var d=0,e=c.length,f=void 0;d<e&&(f=c[d]);d++)jd(f,b);return a}function _expandUnicodeEscapes$$module$third_party$javascript$polymer$v2$shadycss$src$css_parse(a){return a.replace(/\\([0-9a-f]{1,6})\s/gi,function(){for(var b=arguments[1],c=6-b.length;c--;)b="0"+b;return"\\"+b})}
function md(a,b,c){c=c===void 0?"":c;var d="";if(a.cssText||a.rules){var e=a.rules;if(e&&!_hasMixinRules$$module$third_party$javascript$polymer$v2$shadycss$src$css_parse(e))for(var f=0,g=e.length,h=void 0;f<g&&(h=e[f]);f++)d=md(h,b,d);else b?b=a.cssText:(b=a.cssText,b=b.replace(nd,"").replace(od,""),b=b.replace(pd,"").replace(qd,"")),(d=b.trim())&&(d="  "+d+"\n")}d&&(a.selector&&(c+=a.selector+" {\n"),c+=d,a.selector&&(c+="}\n\n"));return c}
function _hasMixinRules$$module$third_party$javascript$polymer$v2$shadycss$src$css_parse(a){a=a[0];return!!a&&!!a.selector&&a.selector.indexOf("--")===0}var hd=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//gim,id=/@import[^;]*;/gim,nd=/(?:^[^;\-\s}]+)?--[^;{}]*?:[^{};]*?(?:[;\n]|$)/gim,od=/(?:^[^;\-\s}]+)?--[^;{}]*?:[^{};]*?{[^}]*?}(?:[;\n]|$)?/gim,pd=/@apply\s*\(?[^);]*\)?\s*(?:[;\n]|$)?/gim,qd=/[^;:]*?:[^;]*?var\([^;]*\)(?:[;\n]|$)?/gim,ld=/^@[^\s]*keyframes/,kd=/\s+/g;var Q=!(window.ShadyDOM&&window.ShadyDOM.inUse),rd;function sd(a){rd=a&&a.shimcssproperties?!1:Q||!(navigator.userAgent.match(/AppleWebKit\/601|Edge\/15/)||!window.CSS||!CSS.supports||!CSS.supports("box-shadow","0 0 0 var(--foo)"))}var td;window.ShadyCSS&&window.ShadyCSS.cssBuild!==void 0&&(td=window.ShadyCSS.cssBuild);var R=!(!window.ShadyCSS||!window.ShadyCSS.disableRuntime);
window.ShadyCSS&&window.ShadyCSS.nativeCss!==void 0?rd=window.ShadyCSS.nativeCss:window.ShadyCSS?(sd(window.ShadyCSS),window.ShadyCSS=void 0):sd(window.WebComponents&&window.WebComponents.flags);var S=rd;var ud=/(?:^|[;\s{]\s*)(--[\w-]*?)\s*:\s*(?:((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};{])+)|\{([^}]*)\}(?:(?=[;\s}])|$))/gi,vd=/(?:^|\W+)@apply\s*\(?([^);\n]*)\)?/gi,wd=/(--[\w-]+)\s*([:,;)]|$)/gi,xd=/(animation\s*:)|(animation-name\s*:)/,yd=/@media\s(.*)/,zd=/\{[^}]*\}/g;var Ad=new Set;function T(a,b){if(!a)return"";typeof a==="string"&&(a=gd(a));b&&Bd(a,b);return md(a,S)}function Cd(a){!a.__cssRules&&a.textContent&&(a.__cssRules=gd(a.textContent));return a.__cssRules||null}function Dd(a){return!!a.parent&&a.parent.type===7}function Bd(a,b,c,d){if(a){var e=!1,f=a.type;if(d&&f===4){var g=a.selector.match(yd);g&&(window.matchMedia(g[1]).matches||(e=!0))}f===1?b(a):c&&f===7?c(a):f===1E3&&(e=!0);if((a=a.rules)&&!e)for(e=0,f=a.length,g=void 0;e<f&&(g=a[e]);e++)Bd(g,b,c,d)}}
function Ed(a,b,c,d){var e=document.createElement("style");b&&e.setAttribute("scope",b);e.textContent=a;if(window.enableHotReplacement&&(a=document.head.querySelector("style[scope="+b+"]")))return a.parentElement.replaceChild(e,a),e;Fd(e,c,d);return e}var U=null;function Gd(a){a=document.createComment(" Shady DOM styles for "+a+" ");var b=document.head;b.insertBefore(a,(U?U.nextSibling:null)||b.firstChild);return U=a}
function Fd(a,b,c){b=b||document.head;b.insertBefore(a,c&&c.nextSibling||b.firstChild);U?a.compareDocumentPosition(U)===Node.DOCUMENT_POSITION_PRECEDING&&(U=a):U=a}function Hd(a,b){for(var c=0,d=a.length;b<d;b++)if(a[b]==="(")c++;else if(a[b]===")"&&--c===0)return b;return-1}
function Id(a,b){var c=a.indexOf("var(");if(c===-1)return b(a,"","","");var d=Hd(a,c+3),e=a.substring(c+4,d);c=a.substring(0,c);a=Id(a.substring(d+1),b);d=e.indexOf(",");return d===-1?b(c,e.trim(),"",a):b(c,e.substring(0,d).trim(),e.substring(d+1).trim(),a)}function Jd(a,b){Q?a.setAttribute("class",b):window.ShadyDOM.nativeMethods.setAttribute.call(a,"class",b)}var Kd=window.ShadyDOM&&window.ShadyDOM.wrap||function(a){return a};
function V(a){var b=a.localName,c="";b?b.indexOf("-")>-1||(c=b,b=a.getAttribute&&a.getAttribute("is")||""):(b=a.is,c=a.extends);return{is:b,U:c}}function Ld(a){for(var b=[],c="",d=0;d>=0&&d<a.length;d++)if(a[d]==="("){var e=Hd(a,d);c+=a.slice(d,e+1);d=e}else a[d]===","?(b.push(c),c=""):c+=a[d];c&&b.push(c);return b}
function Md(a){if(td!==void 0)return td;if(a.__cssBuild===void 0){var b=a.getAttribute("css-build");if(b)a.__cssBuild=b;else{a:{b=a.localName==="template"?a.content.firstChild:a.firstChild;if(b instanceof Comment&&(b=b.textContent.trim().split(":"),b[0]==="css-build")){b=b[1];break a}b=""}if(b!==""){var c=a.localName==="template"?a.content.firstChild:a.firstChild;c.parentNode.removeChild(c)}a.__cssBuild=b}}return a.__cssBuild||""}
function Nd(a){a=a===void 0?"":a;return a!==""&&S?Q?a==="shadow":a==="shady":!1};function Od(a,b){var c=window.shadyCSSStyleTransformHooks;return c&&(c=c.didTransformSelector,typeof c==="function")?c(a,b):a}function Pd(){}function Qd(a,b){var c=W;c.Z(a,function(d){c.element(d,b||"")})}k=Pd.prototype;k.Z=function(a,b){a.nodeType===Node.ELEMENT_NODE&&b(a);if(a=a.localName==="template"?(a.content||a._content||a).childNodes:a.children||a.childNodes)for(var c=0;c<a.length;c++)this.Z(a[c],b)};
k.element=function(a,b,c){if(b)if(a.classList)c?(a.classList.remove("style-scope"),a.classList.remove(b)):(a.classList.add("style-scope"),a.classList.add(b));else if(a.getAttribute){var d=a.getAttribute("class");c?d&&(b=d.replace("style-scope","").replace(b,""),Jd(a,b)):Jd(a,(d?d+" ":"")+"style-scope "+b)}};function Rd(a,b,c){var d=W;d.Z(a,function(e){d.element(e,b,!0);d.element(e,c)})}function Sd(a,b){var c=W;c.Z(a,function(d){c.element(d,b||"",!0)})}
function Td(a,b,c,d,e){var f=W;e=e===void 0?"":e;e===""&&(Q||(d===void 0?"":d)==="shady"?e=T(b,c):(a=V(a),e=Ud(f,b,a.is,a.U,c)+"\n\n"));return e.trim()}function Ud(a,b,c,d,e){var f=a.ha(c,d);c=a.ua(c);return T(b,function(g){g.Ub||(a.Ga(g,a.ma,c,f),g.Ub=!0);e&&e(g,c,f)})}k.ua=function(a){return a?"."+a:""};k.ha=function(a,b){return b?"[is="+a+"]":a};k.Ga=function(a,b,c,d){a.selector=a.o=this.Ha(a,b,c,d)};
k.Ha=function(a,b,c,d){var e=Ld(a.selector);if(!Dd(a)){a=0;for(var f=e.length,g=void 0;a<f&&(g=e[a]);a++)e[a]=b.call(this,g,c,d)}return e.filter(function(h){return!!h}).join(",")};k.Ja=function(a){return a.replace(Vd,function(b,c,d){d.indexOf("+")>-1?d=d.replace(/\+/g,"___"):d.indexOf("___")>-1&&(d=d.replace(/___/g,"+"));return":"+c+"("+d+")"})};
k.xb=function(a){for(var b=[],c;c=a.match(Wd);){var d=c.index,e=Hd(a,d);if(e===-1)throw Error(c.input+" selector missing ')'");c=a.slice(d,e+1);a=a.replace(c,"\ue000");b.push(c)}return{ra:a,matches:b}};k.Bb=function(a,b){var c=a.split("\ue000");return b.reduce(function(d,e,f){return d+e+c[f+1]},c[0])};
k.ma=function(a,b,c){var d=this,e=!1;a=a.trim();var f=Vd.test(a);f&&(a=a.replace(Vd,function(l,m,n){return":"+m+"("+n.replace(/\s/g,"")+")"}),a=this.Ja(a));var g=Wd.test(a);if(g){var h=this.xb(a);a=h.ra;h=h.matches}a=a.replace(Xd,":host $1");a=a.replace(Yd,function(l,m,n){e||(l=d.Hb(n,m,b,c),e=e||l.stop,m=l.Nb,n=l.value);return m+n});g&&(a=this.Bb(a,h));f&&(a=this.Ja(a));a=a.replace(Zd,function(l,m,n,t){return'[dir="'+n+'"] '+m+t+", "+m+'[dir="'+n+'"]'+t});return Od(a,b)};
k.Hb=function(a,b,c,d){var e=a.indexOf("::slotted");a.indexOf(":host")>=0?a=this.Jb(a,d):e!==0&&(a=c?this.Ia(a,c):a);c=!1;e>=0&&(b="",c=!0);if(c){var f=!0;c&&(a=a.replace($d,function(g,h){return" > "+h}))}return{value:a,Nb:b,stop:f}};k.Ia=function(a,b){a=a.split(/(\[.+?\])/);for(var c=[],d=0;d<a.length;d++)if(d%2===1)c.push(a[d]);else{var e=a[d];if(e!==""||d!==a.length-1)e=e.split(":"),e[0]+=b,c.push(e.join(":"))}return c.join("")};
k.Jb=function(a,b){var c=a.match(ae);return(c=c&&c[2].trim()||"")?c[0].match(be)?a.replace(ae,function(d,e,f){return b+f}):c.split(be)[0]===b?c:"should_not_match":a.replace(":host",b)};function ce(a){a.selector===":root"&&(a.selector="html")}k.Ib=function(a){return a.match(":host")?"":a.match("::slotted")?this.ma(a,":not(.style-scope)"):Od(this.Ia(a.trim(),":not(.style-scope)"),":not(.style-scope)")};ca.Object.defineProperties(Pd.prototype,{V:{configurable:!0,enumerable:!0,get:function(){return"style-scope"}}});
var Vd=/:(nth[-\w]+)\(([^)]+)\)/,Yd=/(^|[\s>+~]+)((?:\[.+?\]|[^\s>+~=[])+)/g,be=/[[.:#*]/,Xd=RegExp("^(::slotted)"),ae=/(:host)(?:\(((?:\([^)(]*\)|[^)(]*)+?)\))/,$d=/(?:::slotted)(?:\(((?:\([^)(]*\)|[^)(]*)+?)\))/,Zd=/(.*):dir\((?:(ltr|rtl))\)(.*)/,Wd=/:(?:matches|any|-(?:webkit|moz)-any)/,W=new Pd;function de(a,b,c,d,e){this.A=a||null;this.placeholder=b||null;this.pa=c||[];this.M=null;this.cssBuild=e||"";this.U=d||"";this.K=this.v=this.D=null}function X(a){return a?a.__styleInfo:null}function ee(a,b){return a.__styleInfo=b}de.prototype.nb=function(){return this.A};de.prototype._getStyleRules=de.prototype.nb;function fe(a){var b=this.matches||this.matchesSelector||this.mozMatchesSelector||this.msMatchesSelector||this.oMatchesSelector||this.webkitMatchesSelector;return b&&b.call(this,a)}var ge=/:host\s*>\s*/,he=navigator.userAgent.match("Trident");function ie(){}function je(a){var b={},c=[],d=0;Bd(a,function(f){ke(f);f.index=d++;f=f.l.cssText;for(var g;g=wd.exec(f);){var h=g[1];g[2]!==":"&&(b[h]=!0)}},function(f){c.push(f)});a.pb=c;a=[];for(var e in b)a.push(e);return a}
function ke(a){if(!a.l){var b={},c={};le(a,c)&&(b.C=c,a.rules=null);b.cssText=a.parsedCssText.replace(zd,"").replace(ud,"");a.l=b}}function le(a,b){var c=a.l;if(c){if(c.C)return Object.assign(b,c.C),!0}else{c=a.parsedCssText;for(var d;a=ud.exec(c);){d=(a[2]||a[3]).trim();if(d!=="inherit"||d!=="unset")b[a[1].trim()]=d;d=!0}return d}}
function me(a,b,c){b&&(b=b.indexOf(";")>=0?ne(a,b,c):Id(b,function(d,e,f,g){if(!e)return d+g;(e=me(a,c[e],c))&&e!=="initial"?e==="apply-shim-inherit"&&(e="inherit"):e=me(a,c[f]||f,c)||f;return d+(e||"")+g}));return b&&b.trim()||""}
function ne(a,b,c){b=b.split(";");for(var d=0,e,f;d<b.length;d++)if(e=b[d]){vd.lastIndex=0;if(f=vd.exec(e))e=me(a,c[f[1]],c);else if(f=e.indexOf(":"),f!==-1){var g=e.substring(f);g=g.trim();g=me(a,g,c)||g;e=e.substring(0,f)+g}b[d]=e&&e.lastIndexOf(";")===e.length-1?e.slice(0,-1):e||""}return b.join(";")}
function oe(a,b){var c={},d=[];Bd(a,function(e){e.l||ke(e);var f=e.o||e.parsedSelector;b&&e.l.C&&f&&fe.call(b,f)&&(le(e,c),e=e.index,f=parseInt(e/32,10),d[f]=(d[f]||0)|1<<e%32)},null,!0);return{C:c,key:d}}
function pe(a,b,c,d){b.l||ke(b);if(b.l.C){var e=V(a);a=e.is;e=e.U;e=a?W.ha(a,e):"html";var f=b.parsedSelector;var g=!!f.match(ge)||e==="html"&&f.indexOf("html")>-1;var h=f.indexOf(":host")===0&&!g;c==="shady"&&(g=f===e+" > *."+e||f.indexOf("html")!==-1,h=!g&&f.indexOf(e)===0);if(g||h)c=e,h&&(b.o||(b.o=W.Ha(b,W.ma,W.ua(a),e)),c=b.o||e),g&&e==="html"&&(c=b.o||b.hc),d({ra:c,Tb:h,fc:g})}}
function qe(a,b,c){var d={},e={};Bd(b,function(f){pe(a,f,c,function(g){fe.call(a._element||a,g.ra)&&(g.Tb?le(f,d):le(f,e))})},null,!0);return{Xb:e,Sb:d}}
function re(a,b,c,d){var e=V(b),f=W.ha(e.is,e.U),g=new RegExp("(?:^|[^.#[:])"+(b.extends?"\\"+f.slice(0,-1)+"\\]":f)+"($|[.:[\\s>+~])"),h=X(b);e=h.A;h=h.cssBuild;var l=a.cb(b,e,d);return Td(b,e,function(m){var n="";m.l||ke(m);m.l.cssText&&(n=ne(a,m.l.cssText,c));m.cssText=n;if(!Q&&!Dd(m)&&m.cssText){var t=n=m.cssText;m.La==null&&(m.La=xd.test(n));if(m.La)if(m.da==null){m.da=[];for(var A in l)t=l[A],t=t(n),n!==t&&(n=t,m.da.push(A))}else{for(A=0;A<m.da.length;++A)t=l[m.da[A]],n=t(n);t=n}m.cssText=t;
a.Fb(m,g,f,d)}},h)}ie.prototype.cb=function(a,b,c){a=b.pb;b={};if(!Q&&a)for(var d=0,e=a[d];d<a.length;e=a[++d])this.Eb(e,c),b[e.keyframesName]=this.qb(e);return b};ie.prototype.qb=function(a){return function(b){return b.replace(a.Vb,a.Pa)}};ie.prototype.Eb=function(a,b){a.Vb=new RegExp("\\b"+a.keyframesName+"(?!\\B|-)","g");a.Pa=a.keyframesName+"-"+b;a.o=a.o||a.selector;a.selector=a.o.replace(a.keyframesName,a.Pa)};
ie.prototype.Fb=function(a,b,c,d){a.o=a.o||a.selector;d="."+d;for(var e=Ld(a.o),f=0,g=e.length,h=void 0;f<g&&(h=e[f]);f++)e[f]=h.match(b)?h.replace(c,d):d+" "+h;a.selector=e.join(",")};function se(a,b){var c=te,d=Cd(a);a.textContent=T(d,function(e){var f=e.cssText=e.parsedCssText;e.l&&e.l.cssText&&(f=f.replace(nd,"").replace(od,""),e.cssText=ne(c,f,b))})}ca.Object.defineProperties(ie.prototype,{Ra:{configurable:!0,enumerable:!0,get:function(){return"x-scope"}}});var te=new ie;var ue={},ve=window.customElements;if(ve&&!Q&&!R){var we=ve.define;ve.define=function(a,b,c){ue[a]||(ue[a]=Gd(a));we.call(ve,a,b,c)}};function xe(){this.cache={};this.cc=100}xe.prototype.Mb=function(a,b,c){for(var d=0;d<c.length;d++){var e=c[d];if(a.C[e]!==b[e])return!1}return!0};xe.prototype.store=function(a,b,c,d){var e=this.cache[a]||[];e.push({C:b,styleElement:c,v:d});e.length>this.cc&&e.shift();this.cache[a]=e};xe.prototype.fetch=function(a,b,c){if(a=this.cache[a])for(var d=a.length-1;d>=0;d--){var e=a[d];if(this.Mb(e,b,c))return e}};function ye(){}var ze=new RegExp(W.V+"\\s*([^\\s]*)");function Ae(a){return(a=(a.classList&&a.classList.value?a.classList.value:a.getAttribute("class")||"").match(ze))?a[1]:""}function Be(a){var b=Kd(a).getRootNode();return b===a||b===a.ownerDocument?"":(a=b.host)?V(a).is:""}
function Ce(a){for(var b=0;b<a.length;b++){var c=a[b];if(c.target!==document.documentElement&&c.target!==document.head)for(var d=0;d<c.addedNodes.length;d++){var e=c.addedNodes[d];if(e.nodeType===Node.ELEMENT_NODE){var f=e.getRootNode(),g=Ae(e);if(g&&f===e.ownerDocument&&(e.localName!=="style"&&e.localName!=="template"||Md(e)===""))Sd(e,g);else if(f instanceof ShadowRoot)for(f=Be(e),f!==g&&Rd(e,g,f),e=window.ShadyDOM.nativeMethods.querySelectorAll.call(e,":not(."+W.V+")"),g=0;g<e.length;g++){f=e[g];
var h=Be(f);h&&W.element(f,h)}}}}}
if(!(Q||window.ShadyDOM&&window.ShadyDOM.handlesDynamicScoping)){var De=new MutationObserver(Ce),Ee=function(a){De.observe(a,{childList:!0,subtree:!0})};if(window.customElements&&!window.customElements.polyfillWrapFlushCallback)Ee(document);else{var Fe=function(){Ee(document.body)};window.HTMLImports?window.HTMLImports.whenReady(Fe):requestAnimationFrame(function(){if(document.readyState==="loading"){var a=function(){Fe();document.removeEventListener("readystatechange",a)};document.addEventListener("readystatechange",
a)}else Fe()})}ye=function(){Ce(De.takeRecords())}};var Ge={};var He=Promise.resolve();function Ie(a){if(a=Ge[a])a._applyShimCurrentVersion=a._applyShimCurrentVersion||0,a._applyShimValidatingVersion=a._applyShimValidatingVersion||0,a._applyShimNextVersion=(a._applyShimNextVersion||0)+1}function Je(a){return a._applyShimCurrentVersion===a._applyShimNextVersion}function Ke(a){a._applyShimValidatingVersion=a._applyShimNextVersion;a._validating||(a._validating=!0,He.then(function(){a._applyShimCurrentVersion=a._applyShimNextVersion;a._validating=!1}))};var Le={},Me=new xe;function Y(){this.Ea={};this.I=document.documentElement;var a=new fd;a.rules=[];this.B=ee(this.I,new de(a));this.ia=!1;this.h=this.m=null}k=Y.prototype;k.flush=function(){ye()};k.jb=function(a){var b=this.Ea[a]=(this.Ea[a]||0)+1;return a+"-"+b};k.Qb=function(a){return Cd(a)};k.bc=function(a){return T(a)};
k.ib=function(a){var b=[];a=a.content.querySelectorAll("style");for(var c=0;c<a.length;c++){var d=a[c];if(d.hasAttribute("shady-unscoped")){if(!Q){var e=d.textContent;if(!Ad.has(e)){Ad.add(e);var f=document.createElement("style");f.setAttribute("shady-unscoped","");f.textContent=e;document.head.appendChild(f)}d.parentNode.removeChild(d)}}else b.push(d.textContent),d.parentNode.removeChild(d)}return b.join("").trim()};
k.prepareTemplate=function(a,b,c){this.prepareTemplateDom(a,b);this.prepareTemplateStyles(a,b,c)};
k.prepareTemplateStyles=function(a,b,c){if(!a._prepared&&!R){Q||ue[b]||(ue[b]=Gd(b));a._prepared=!0;a.name=b;a.extends=c;Ge[b]=a;var d=Md(a),e=Nd(d);c={is:b,extends:c};var f=this.ib(a)+(Le[b]||"");this.N();if(!e){var g;if(g=!d)g=vd.test(f)||ud.test(f),vd.lastIndex=0,ud.lastIndex=0;var h=gd(f);g&&S&&this.m&&this.m.transformRules(h,b);a._styleAst=h}g=[];S||(g=je(a._styleAst));if(!g.length||S)b=this.kb(c,a._styleAst,Q?a.content:null,ue[b]||null,d,e?f:""),a._style=b;a.wb=g}};
k.prepareAdoptedCssText=function(a,b){Le[b]=a.join(" ")};k.prepareTemplateDom=function(a,b){if(!R){var c=Md(a);Q||c==="shady"||a._domPrepared||(a._domPrepared=!0,Qd(a.content,b))}};k.kb=function(a,b,c,d,e,f){f=Td(a,b,null,e,f);return f.length?Ed(f,a.is,c,d):null};k.Ca=function(a){var b=V(a),c=b.is;b=b.U;var d=ue[c]||null,e=Ge[c];if(e){c=e._styleAst;var f=e.wb;e=Md(e);b=new de(c,d,f,b,e);ee(a,b);return b}};
k.eb=function(){return!this.m&&window.ShadyCSS&&window.ShadyCSS.ApplyShim?(this.m=window.ShadyCSS.ApplyShim,this.m.invalidCallback=Ie,!0):!1};k.fb=function(){var a=this;!this.h&&window.ShadyCSS&&window.ShadyCSS.CustomStyleInterface&&(this.h=window.ShadyCSS.CustomStyleInterface,this.h.transformCallback=function(b){a.Oa(b)},this.h.validateCallback=function(){requestAnimationFrame(function(){(a.h.enqueued||a.ia)&&a.flushCustomStyles()})})};k.N=function(){var a=this.eb();this.fb();return a};
k.flushCustomStyles=function(){if(!R){var a=this.N();if(this.h){var b=this.h.processStyles();!a&&!this.h.enqueued||Nd(this.B.cssBuild)||(S?this.B.cssBuild||this.Db(b):(this.Ab(b),this.na(this.I,this.B),this.Va(b),this.ia&&this.styleDocument()),this.h.enqueued=!1)}}};
k.Ab=function(a){var b=this;a=a.map(function(c){return b.h.getStyleForCustomStyle(c)}).filter(function(c){return!!c});a.sort(function(c,d){c=d.compareDocumentPosition(c);return c&Node.DOCUMENT_POSITION_FOLLOWING?1:c&Node.DOCUMENT_POSITION_PRECEDING?-1:0});this.B.A.rules=a.map(function(c){return Cd(c)})};
k.styleElement=function(a,b){if(R){if(b){X(a)||ee(a,new de(null));var c=X(a);this.Aa(c,b);Ne(this,a,c)}}else if(c=X(a)||this.Ca(a))this.ja(a)||(this.ia=!0),b&&this.Aa(c,b),S?Ne(this,a,c):(this.flush(),this.na(a,c),c.pa&&c.pa.length&&this.Wa(a,c))};k.Aa=function(a,b){a.M=a.M||{};Object.assign(a.M,b)};
function Ne(a,b,c){var d=V(b).is;if(c.M){var e=c.M,f;for(f in e)f===null?b.style.removeProperty(f):b.style.setProperty(f,e[f])}if(((e=Ge[d])||a.ja(b))&&(!e||Md(e)==="")&&e&&e._style&&!Je(e)){if(Je(e)||e._applyShimValidatingVersion!==e._applyShimNextVersion)a.N(),a.m&&a.m.transformRules(e._styleAst,d),e._style.textContent=Td(b,c.A),Ke(e);Q&&(a=b.shadowRoot)&&(a=a.querySelector("style"))&&(a.textContent=Td(b,c.A));c.A=e._styleAst}}
k.la=function(a){return(a=Kd(a).getRootNode().host)?X(a)||this.Ca(a)?a:this.la(a):this.I};k.ja=function(a){return a===this.I};
k.Wa=function(a,b){var c=V(a).is,d=Me.fetch(c,b.D,b.pa),e=d?d.styleElement:null,f=b.v;b.v=d&&d.v||this.jb(c);var g=b.D;var h=b.v;var l=te;g=e?e.textContent||"":re(l,a,g,h);l=X(a);var m=l.K;m&&!Q&&m!==e&&(m._useCount--,m._useCount<=0&&m.parentNode&&m.parentNode.removeChild(m));Q?l.K?(l.K.textContent=g,e=l.K):g&&(e=Ed(g,h,a.shadowRoot,l.placeholder)):e?e.parentNode||(he&&g.indexOf("@media")>-1&&(e.textContent=g),Fd(e,null,l.placeholder)):g&&(e=Ed(g,h,null,l.placeholder));e&&(e._useCount=e._useCount||
0,l.K!=e&&e._useCount++,l.K=e);h=e;Q||(e=b.v,l=g=a.getAttribute("class")||"",f&&(l=g.replace(new RegExp("\\s*x-scope\\s*"+f+"\\s*","g")," ")),l+=(l?" ":"")+"x-scope "+e,g!==l&&Jd(a,l));d||Me.store(c,b.D,h,b.v);return h};
k.na=function(a,b){var c=this.la(a),d=X(c),e=d.D;c===this.I||e||(this.na(c,d),e=d.D);c=Object.create(e||null);e=qe(a,b.A,b.cssBuild);a=oe(d.A,a).C;Object.assign(c,e.Sb,a,e.Xb);this.sb(c,b.M);a=te;d=Object.getOwnPropertyNames(c);e=0;for(var f;e<d.length;e++)f=d[e],c[f]=me(a,c[f],c);b.D=c};k.sb=function(a,b){for(var c in b){var d=b[c];if(d||d===0)a[c]=d}};k.styleDocument=function(a){this.styleSubtree(this.I,a)};
k.styleSubtree=function(a,b){var c=Kd(a),d=c.shadowRoot,e=this.ja(a);(d||e)&&this.styleElement(a,b);if(a=e?c:d)for(a=Array.from(a.querySelectorAll("*")).filter(function(f){return Kd(f).shadowRoot}),b=0;b<a.length;b++)this.styleSubtree(a[b])};k.Db=function(a){for(var b=0;b<a.length;b++){var c=this.h.getStyleForCustomStyle(a[b]);c&&this.Cb(c)}};k.Va=function(a){for(var b=0;b<a.length;b++){var c=this.h.getStyleForCustomStyle(a[b]);c&&se(c,this.B.D)}};
k.Oa=function(a){var b=this,c=Md(a);c!==this.B.cssBuild&&(this.B.cssBuild=c);if(!Nd(c)){var d=Cd(a);Bd(d,function(e){if(Q)ce(e);else{var f=W;e.selector=e.parsedSelector;ce(e);f.Ga(e,f.Ib)}S&&c===""&&(b.N(),b.m&&b.m.transformRule(e))});S?a.textContent=T(d):this.B.A.rules.push(d)}};k.Cb=function(a){if(S&&this.m){var b=Cd(a);this.N();this.m.transformRules(b);a.textContent=T(b)}};
k.getComputedStyleValue=function(a,b){var c;S||(c=(X(a)||X(this.la(a))).D[b]);return(c=c||window.getComputedStyle(a).getPropertyValue(b))?c.trim():""};k.ac=function(a,b){var c=Kd(a).getRootNode();b=b?(typeof b==="string"?b:String(b)).split(/\s/):[];c=c.host&&c.host.localName;if(!c){var d=a.getAttribute("class");if(d){d=d.split(/\s/);for(var e=0;e<d.length;e++)if(d[e]===W.V){c=d[e+1];break}}}c&&b.push(W.V,c);S||(c=X(a))&&c.v&&b.push(te.Ra,c.v);Jd(a,b.join(" "))};k.Gb=function(a){return X(a)};
k.Zb=function(a,b){W.element(a,b)};k.dc=function(a,b){W.element(a,b,!0)};k.Yb=function(a){return Be(a)};k.Pb=function(a){return Ae(a)};Y.prototype.flush=Y.prototype.flush;Y.prototype.prepareTemplate=Y.prototype.prepareTemplate;Y.prototype.styleElement=Y.prototype.styleElement;Y.prototype.styleDocument=Y.prototype.styleDocument;Y.prototype.styleSubtree=Y.prototype.styleSubtree;Y.prototype.getComputedStyleValue=Y.prototype.getComputedStyleValue;Y.prototype.setElementClass=Y.prototype.ac;
Y.prototype._styleInfoForNode=Y.prototype.Gb;Y.prototype.transformCustomStyleForDocument=Y.prototype.Oa;Y.prototype.getStyleAst=Y.prototype.Qb;Y.prototype.styleAstToString=Y.prototype.bc;Y.prototype.flushCustomStyles=Y.prototype.flushCustomStyles;Y.prototype.scopeNode=Y.prototype.Zb;Y.prototype.unscopeNode=Y.prototype.dc;Y.prototype.scopeForNode=Y.prototype.Yb;Y.prototype.currentScopeForNode=Y.prototype.Pb;Y.prototype.prepareAdoptedCssText=Y.prototype.prepareAdoptedCssText;
Object.defineProperties(Y.prototype,{nativeShadow:{get:function(){return Q}},nativeCss:{get:function(){return S}}});var Z=new Y,Oe,Pe;window.ShadyCSS&&(Oe=window.ShadyCSS.ApplyShim,Pe=window.ShadyCSS.CustomStyleInterface);
window.ShadyCSS={ScopingShim:Z,prepareTemplate:function(a,b,c){Z.flushCustomStyles();Z.prepareTemplate(a,b,c)},prepareTemplateDom:function(a,b){Z.prepareTemplateDom(a,b)},prepareTemplateStyles:function(a,b,c){Z.flushCustomStyles();Z.prepareTemplateStyles(a,b,c)},styleSubtree:function(a,b){Z.flushCustomStyles();Z.styleSubtree(a,b)},styleElement:function(a){Z.flushCustomStyles();Z.styleElement(a)},styleDocument:function(a){Z.flushCustomStyles();Z.styleDocument(a)},flushCustomStyles:function(){Z.flushCustomStyles()},
getComputedStyleValue:function(a,b){return Z.getComputedStyleValue(a,b)},nativeCss:S,nativeShadow:Q,cssBuild:td,disableRuntime:R};Oe&&(window.ShadyCSS.ApplyShim=Oe);Pe&&(window.ShadyCSS.CustomStyleInterface=Pe);})();
//# sourceMappingURL=blaze-out/k8-opt/bin/third_party/javascript/polymer/v2/webcomponentsjs/webcomponents-sd.js.sourcemap

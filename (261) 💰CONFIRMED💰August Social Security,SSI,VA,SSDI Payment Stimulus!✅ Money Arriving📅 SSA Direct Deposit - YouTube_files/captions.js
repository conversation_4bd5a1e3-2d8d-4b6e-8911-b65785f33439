(function(g){var window=this;'use strict';var B_Z=function(e,a,r){g.Y(function(P){e.S=g.ge(a,r);g.OO(P)})},fq=function(e){e.isActive()||e.start()},hDB=function(e,a){return a?e.captionsInitialState:"CAPTIONS_INITIAL_STATE_UNKNOWN"},cvN=function(e){return g.tZ(e)||e.X("web_enable_caption_language_preference_stickiness")},oC9=function(e,a){var r=new g.zF;
r.languageCode=e.languageCode;r.languageName=e.languageName;r.name=e.name;r.displayName=e.displayName;r.kind=e.kind;r.isDefault=!1;r.S=e.S;r.isTranslateable=e.isTranslateable;r.vssId=e.vssId;r.url=e.url;r.translationLanguage=a;e.xtags&&(r.xtags=e.xtags);e.captionId&&(r.captionId=e.captionId);return r},nCG=function(e,a){var r,P,B;
return g.Y(function(h){if(h.T==1)return r=e+"|"+a,g.l(h,g.mQ(),2);if(h.T!=3){P=h.S;if(!P)throw g.GE("gct");return g.l(h,g.r3(P),3)}B=h.S;return h.return(B.get("captions",r))})},yvl=function(e,a,r){nCG(e,a).then(function(P){P&&r(P.trackData,new g.zF(P.metadata))})},fKM=function(e){if(!T_Z.test(e))throw Error("'"+e+"' is not a valid hex color");
e.length==4&&(e=e.replace(XjN,"#$1$1$2$2$3$3"));e=e.toLowerCase();e=parseInt(e.slice(1),16);return[e>>16,e>>8&255,e&255]},AvG=function(){var e=void 0;
e=e===void 0?{}:e;var a="suggest_correction"in g.wob?g.wob.suggest_correction:"Edit caption";a=a||"";var r={},P;for(P in e){r={SB:r.SB};r.SB=P;var B=function(h){return function(){return String(e[h.SB])}}(r);
a=a.replace(new RegExp("\\$\\{"+r.SB+"\\}","gi"),B);a=a.replace(new RegExp("\\$"+r.SB,"gi"),B)}return a},UBV=function(){return g.$f("yt-player-caption-display-settings")},Ay=function(){this.segments=[]},CAV=function(e,a){var r=g.HB(e.segments,a);
r>=0||r<0&&(-r-1)%2===1||(r=-r-1,r>0&&a-e.segments[r-1]===1&&r<e.segments.length&&e.segments[r]-a===1?(g.OG(e.segments,r),g.OG(e.segments,r-1)):r>0&&a-e.segments[r-1]===1?e.segments[r-1]=a:r<e.segments.length&&e.segments[r]-a===1?e.segments[r]=a:(g.NM(e.segments,r,0,a),g.NM(e.segments,r+1,0,a)))},GRZ=function(e,a,r,P,B,h){g.N.call(this);
this.policy=e;this.player=a;this.HW=r;this.Y=P;this.C=B;this.N=h;this.L=new Ay;this.Z=-1;this.B=this.T=this.S=null;this.oH=0;this.D=new g.vP(this.Rj,1E3,this);this.events=new g.Zc(this);g.x(this,this.D);g.x(this,this.events);this.events.j(a,"SEEK_COMPLETE",this.zd);this.zd();this.Rj()},ECN=function(e){return e.S&&e.S.Z?e.S.Z+e.player.t9()<e.player.getCurrentTime():!1},bvG=function(e,a){if(e.policy.by&&e.player.xZ()){var r=g.nz(a,e.policy,{});
r.set("pot",e.player.xZ());r=r.ju()}else r=g.nz(a,e.policy,{}).ju();var P={format:"RAW",withCredentials:!0};if(e.policy.Cf){P.method="POST";var B=a.L;B&&Object.keys(B).length>0?P.postBody=g.S_(B,g.WH):P.postBody=(0,g.YA)([120,0])}e.C&&(P.responseType="arraybuffer");var h=++e.oH,c=(0,g.Ov)();e.B=g.de(r,P,3,100,-1,function(y){y.errorCode==="net.timeout"&&e.player.nf("capnt",{rn:h++})}).then(function(y){if(e.policy.wN&&h%100===1){var T=(0,g.Ov)();
e.player.nf("caprsp",{rn:h,ms:T-c,kb:(y.xhr.responseText.length/1024).toFixed()})}a:{y=y.xhr;e.wP();if(e.T){var X=!(e.C?y.response:y.responseText)||y.status>=400;if(T=g.nFj(y)){y=g.nz(e.T,e.policy,{});e.T.QY(y,T);bvG(e,e.T);break a}if(X)e.player.nf("capfail",{status:y.status});else{var f;g.KZ("fcb_r",(0,g.Ov)(),((f=e.player.getVideoData())==null?void 0:f.HW)||"");f=e.T.Vm[0].lW;e.Y!=null&&e.Z!==f&&(T=e.T.Vm[0],e.C?e.Y(y.response,(T.startTime+e.player.t9())*1E3):e.Y(y.responseText,(T.startTime+e.player.t9())*
1E3),e.Z=f)}}e.T=null;e.B=null}}).Vl(function(y){e.T=null;
e.B=null;var T;e.player.nf("capfail",{rn:h,status:(T=y.xhr)==null?void 0:T.status})});
e.T=a;CAV(e.L,e.T.Vm[0].lW)},U_=function(e,a){g.Qn.call(this,a);
this.S=e;this.W=a;this.L=null;this.Z=!1;this.logger=new g.bl("caps");this.D=g.qJN(this.W,this.S)},gCw=function(e,a){var r=[],P;
for(P in e.S.S)if(e.S.S.hasOwnProperty(P)){var B=e.S.S[P];if(g.l6(B,a||null)){var h=B.info.id,c=h,y="."+h,T="",X="";if(B=B.info.captionTrack)h=B.languageCode,c=B.displayName,y=B.vssId,T=B.kind,X=B.id;else{B=h;var f=g.hAM.get(B);f==null&&(f=tuB[B]||tuB[B.replace(/-/g,"_")],g.hAM.set(B,f));B=f;c=B||c}r.push(new g.zF({id:P,languageCode:h,languageName:c,is_servable:!0,is_default:!0,is_translateable:!1,vss_id:y,kind:T,captionId:X}))}}return r},ZvN=function(e,a){return a!=null&&a in e.S.S?e.S.S[a]:null},
OvG=function(e,a,r){var P=[],B;
for(B in e.S.S)if(e.S.S.hasOwnProperty(B)){var h=e.S.S[B];if(g.l6(h,r||null)){var c=h.info.captionTrack;c&&c.languageCode===a&&P.push(h)}}return P.length?P[0]:null},Cq=function(e,a,r,P,B,h,c,y,T,X){var f=X.isInline()&&!0,A={};
Object.assign(A,a);Object.assign(A,e.params);Object.assign(A,r);var U={};Object.assign(U,a.BG);e.params.BG&&Object.assign(U,e.params.BG);Object.assign(U,r.BG);f&&(A.windowOpacity=.6,U.backgroundOpacity=0);A.BG=U;var C=A.s2===1,G=[{K:"span",U:"captions-text",J:{style:"word-wrap: normal; display: block;"}}],O,d,W;(y=y.FM("caption_edit_on_hover")&&((O=X.getVideoData().getPlayerResponse())==null?void 0:(d=O.captions)==null?void 0:(W=d.playerCaptionsTracklistRenderer)==null?void 0:W.openTranscriptCommand))&&
G.unshift({K:"button",U:"caption-edit",J:{tabindex:"0","aria-label":AvG()},V:[{K:"svg",J:{fill:"#e3e3e3",height:"100%",viewBox:"5 5 38 38",width:"100%"},V:[{K:"path",J:{d:"M9 39h2.2l24.25-24.25-1.1-1.1-1.1-1.1L9 36.8Zm-3 3v-6.4L35.4 6.2q.85-.85 2.12-.82 1.27.02 2.12.87L41.8 8.4q.85.85.85 2.1t-.85 2.1L12.4 42Zm33.5-31.55L37.45 8.4Zm-4.05 4.3-1.1-1.1-1.1-1.1 2.2 2.2Z"}}]}]});g.v.call(this,{K:"div",U:"caption-window",J:{id:"caption-window-"+e.id,dir:C?"rtl":"ltr",tabindex:"0",lang:A.lang},V:G});var V=
this;this.D=[];this.Lf=!1;this.T=e;this.e1=this.Cf=null;this.Q2=h;this.Te=c;this.Y=null;this.maxWidth=h*.96;this.maxHeight=c*.96;this.S=A;this.J9=r;this.oH=a;this.L=this.BW("captions-text");this.ID=this.L.style.getPropertyValue("box-decoration-break")!==""||this.L.style.getPropertyValue("-webkit-box-decoration-break")!=="";this.Uo=kRl(P,B,h,c);this.dW=T;y&&(this.Z=this.BW("caption-edit"),this.j(this.Z,"click",function(){V.dW()}));
this.type=0;this.GQ=this.Uo*dBN(U);this.HA=f;e=new g.qp(this.element,!0);g.x(this,e);e.subscribe("dragstart",this.OV,this);e.subscribe("dragmove",this.ge,this);e.subscribe("dragend",this.BF,this);this.PW=this.ym=this.rP=this.k$=0;e="";this.S.windowOpacity&&(e=fKM(this.S.windowColor),e="rgba("+e[0]+","+e[1]+","+e[2]+","+this.S.windowOpacity+")");a={"background-color":e,display:this.S.isVisible===!1?"none":"","text-align":JvB[this.S.textAlign]};this.ID&&(a["border-radius"]=e?this.GQ/8+"px":"");(this.B=
this.T.params.s2===2||this.T.params.s2===3)&&DBM(this,this.element);g.vo(this.element,a);if(f){var L;(L=this.element.parentElement)==null||L.style.setProperty("--caption-window-color",e)}switch(this.S.LW){case 0:case 1:case 2:g.rr(this.element,"ytp-caption-window-top");break;case 6:case 7:case 8:g.rr(this.element,"ytp-caption-window-bottom")}},DBM=function(e,a){var r="vertical-rl";
e.S.aS===1&&(r="vertical-lr");g.T$&&(r=r==="vertical-lr"?"tb-lr":"tb-rl");g.vo(a,"-o-writing-mode",r);g.vo(a,"-webkit-writing-mode",r);g.vo(a,"writing-mode",r);g.vo(a,"text-orientation","upright");g.rr(a,"ytp-vertical-caption");e.T.params.s2===3&&(g.vo(a,"text-orientation",""),g.vo(a,"transform","rotate(180deg)"))},dBN=function(e){var a=1+.25*(e.fontSizeIncrement||0);
if(e.offset===0||e.offset===2)a*=.8;return a},VuV=function(e,a){var r={},P=a.background?a.background:e.S.BG.background;
if(a.backgroundOpacity!=null||a.background){var B=a.backgroundOpacity!=null?a.backgroundOpacity:e.S.BG.backgroundOpacity;P=fKM(P);r.background="rgba("+P[0]+","+P[1]+","+P[2]+","+B+")";e.ID&&(r["box-decoration-break"]="clone",r["border-radius"]=e.GQ/8+"px")}if(a.fontSizeIncrement!=null||a.offset!=null)r["font-size"]=e.Uo*dBN(a)+"px";P=1;B=a.color||e.S.BG.color;if(a.color||a.textOpacity!=null)B=fKM(B),P=a.textOpacity==null?e.S.BG.textOpacity:a.textOpacity,B="rgba("+B[0]+","+B[1]+","+B[2]+","+P+")",
r.color=B,r.fill=B;var h=a.charEdgeStyle;h===0&&(h=void 0);if(h){B="rgba(34, 34, 34, "+P+")";var c="rgba(204, 204, 204, "+P+")";a.e7&&(c=B=a.e7);var y=e.Uo/16/2,T=Math.max(y,1),X=Math.max(2*y,1),f=Math.max(3*y,1),A=Math.max(5*y,1);P=[];switch(h){case 4:for(;f<=A;f+=y)P.push(X+"px "+X+"px "+f+"px "+B);break;case 1:X=window.devicePixelRatio>=2?.5:1;for(h=T;h<=f;h+=X)P.push(h+"px "+h+"px "+B);break;case 2:P.push(T+"px "+T+"px "+c);P.push("-"+T+"px -"+T+"px "+B);break;case 3:for(f=0;f<5;f++)P.push("0 0 "+
X+"px "+B)}r["text-shadow"]=P.join(", ")}B="";switch(a.fontFamily){case 1:B='"Courier New", Courier, "Nimbus Mono L", "Cutive Mono", monospace';break;case 2:B='"Times New Roman", Times, Georgia, Cambria, "PT Serif Caption", serif';break;case 3:B='"Deja Vu Sans Mono", "Lucida Console", Monaco, Consolas, "PT Mono", monospace';break;case 5:B='"Comic Sans MS", Impact, Handlee, fantasy';break;case 6:B='"Monotype Corsiva", "URW Chancery L", "Apple Chancery", "Dancing Script", cursive';break;case 7:B=g.x2()?
'"Carrois Gothic SC", sans-serif-smallcaps':'Arial, Helvetica, Verdana, "Marcellus SC", sans-serif';break;case 0:case 4:B='"YouTube Noto", Roboto, Arial, Helvetica, Verdana, "PT Sans Caption", sans-serif'}B&&(r["font-family"]=B);B=a.offset;B==null&&(B=e.S.BG.offset);switch(B){case 0:r["vertical-align"]="sub";break;case 2:r["vertical-align"]="super"}a.fontFamily===7&&(r["font-variant"]="small-caps");a.bold&&(r["font-weight"]="bold");a.italic&&(r["font-style"]="italic");a.underline&&(r["text-decoration"]=
"underline");a.pI&&(r.visibility="hidden");a.Lp===1&&e.B&&(r["text-combine-upright"]="all",r["text-orientation"]="mixed",B=g.kG||g.Uv,e.T.params.s2===3?r.transform=B?"rotate(90deg)":"rotate(180deg)":B&&(r.transform="rotate(-90deg)"));if(a.textEmphasis===1||a.textEmphasis===2||a.textEmphasis===3||a.textEmphasis===4||a.textEmphasis===5)if(g.kG)r["font-weight"]="bold";else switch(r["text-emphasis-style"]="filled circle",r["text-emphasis-color"]="currentcolor",r["webkit-text-emphasis"]="filled circle",
a.textEmphasis){case 4:case 3:r["text-emphasis-position"]="under left";r["webkit-text-emphasis-position"]="under left";break;case 5:case 2:r["text-emphasis-position"]="over right",r["webkit-text-emphasis-position"]="over right"}return r},G_=function(e){e=e.split("px");
return e.length>0?(e=Number(e[0]))?e:0:0},$Bw=function(e){e.Y=g.U9("SPAN");
g.vo(e.Y,{display:"block"});g.rr(e.Y,"caption-visual-line");e.L.appendChild(e.Y)},Q_B=function(e,a){var r=g.U9("SPAN");
g.vo(r,{display:"inline-block","white-space":"pre-wrap"});g.vo(r,VuV(e,a));r.classList.add("ytp-caption-segment");e.Y.appendChild(r);r.previousElementSibling&&(g.vo(r.previousElementSibling,{"border-top-right-radius":"0","border-bottom-right-radius":"0"}),g.vo(r,{"border-top-left-radius":"0","border-bottom-left-radius":"0"}));return r},WMB=function(e,a,r){e.Lf=e.Lf||!!r;
var P={};Object.assign(P,e.S.BG);Object.assign(P,r||a.S);Object.assign(P,e.J9.BG);(r=!e.Y)&&$Bw(e);for(var B=e.Cf&&e.e1&&g.Cf(P,e.e1)?e.Cf:Q_B(e,P),h=typeof a.text==="string",c=h?a.text.split("\n"):[a.text],y=0;y<c.length;y++){var T=y>0||!a.append,X=c[y];T&&!r?($Bw(e),B=Q_B(e,P)):T&&r&&(r=!1);X&&(B.appendChild(h?g.CO(X):X),h||X.tagName!=="RUBY"||X.childElementCount!==4||g.kG||!g.S2(X.children[2],"text-emphasis")||(T=e.B?"padding-right":"padding-top",g.S2(X.children[2],"text-emphasis-position")&&(T=
e.B?"padding-left":"padding-bottom"),g.T7?g.vo(B,T,"1em"):g.vo(B,T,"0.5em")))}e.e1=P;e.Cf=B;e.D.push(a)},kRl=function(e,a,r,P){var B=a/360*16;
a>=e&&(e=640,P>r*1.3&&(e=480),B=r/e*16);return B},LMG=function(){this.B=this.time=this.mode=this.T=0;
this.L=new lKb(this);this.C=new lKb(this);this.S=[];this.clear()},wj8=function(e,a,r){if(e===255&&a===255||!e&&!a)return{q6:e,
Ng:a,result:0};e=Y2l[e];a=Y2l[a];if(e&128){var P;if(P=!(a&128))P=a,P=r.Fw()&&r.Ng===P;if(P)return{q6:e,Ng:a,result:1}}else if(a&128&&1<=e&&e<=31)return{q6:e,Ng:a,result:2};return{q6:e,Ng:a,result:3}},ivB=function(e,a,r,P){a===255&&r===255||!a&&!r?(++e.B===45&&e.reset(),e.L.T.clear(),e.C.T.clear()):(e.B=0,pjB(e.L,a,r,P))},zDB=function(e,a){e.S.sort(function(B,h){var c=B.time-h.time;
return c===0?B.order-h.order:c});
for(var r=g.Z(e.S),P=r.next();!P.done;P=r.next())P=P.value,e.time=P.time,P.type===0?ivB(e,P.oB,P.Sz,a):P.type===1&&e.T&496&&pjB(e.C,P.oB,P.Sz,a);e.S.length=0},E_=function(){this.type=0},b1=function(){this.state=this.Ng=this.q6=0},N_N=function(){this.timestamp=this.S=0},ty=function(e){this.C=e;
this.B=[];this.S=this.T=this.row=0;this.style=new E_;for(e=this.L=0;e<=15;e++){this.B[e]=[];for(var a=0;a<=32;a++)this.B[e][a]=new N_N}},g$=function(e,a){if(e.style.type===3){for(var r=0,P=0,B=e.C.time+0,h="",c="",y=B,T=1;T<=15;++T){for(var X=!1,f=P?P:1;f<=32;++f){var A=e.B[T][f];
if(A.S!==0){r===0&&(r=T,P=f);X=String.fromCharCode(A.S);var U=A.timestamp;U<B&&(B=U);A.timestamp=y;c&&(h+=c,c="");h+=X;X=!0}if((A.S===0||f===32)&&X){c="\n";break}else if(P&&!X)break}if(r&&!X)break}h&&a.L(r,P,B,y,h)}else for(P=r=0,h=B=e.C.time+0,c=1;c<=15;++c)for(y="",T=1;T<=32;++T)if(f=e.B[c][T],A=f.S,A!==0&&(r===0&&(r=c,P=T),X=String.fromCharCode(A),U=f.timestamp,U<=B&&(B=U),y+=X,f.reset()),T===32||A===0)y&&a.L(r,P,B,h,y),B=h,y="",P=r=0},vCl=function(e,a){switch(e){case 0:return xBN[(a&127)-32];
case 1:return IK9[a&15];case 2:return HvB[a&31];case 3:return q2Z[a&31]}return 0},Z2=function(e){return e.B[e.row][e.T]},O_=function(e,a,r){a>=2&&e.T>1&&(--e.T,Z2(e).S=0);
var P=Z2(e);P.timestamp=e.C.time+0;P.S=vCl(a,r);e.T<32&&e.T++},RDG=function(e,a,r,P){for(var B=0;B<P;B++)for(var h=0;h<=32;h++){var c=e.B[a+B][h],y=e.B[r+B][h];
c.S=y.S;c.timestamp=y.timestamp}},k5=function(e,a,r){for(var P=0;P<r;P++)for(var B=0;B<=32;B++)e.B[a+P][B].reset()},S2V=function(e){e.row=e.S>0?e.S:1;
e.T=1;k5(e,0,15)},s_9=function(e){this.B=e;
this.C=0;this.style=new E_;this.Z=new ty(this.B);this.D=new ty(this.B);this.text=new ty(this.B);this.T=this.Z;this.L=this.D;this.S=this.T},mBV=function(e,a,r){var P=e.T,B=!1;
switch(e.style.get()){case 4:case 1:case 2:e.style.get()===4&&P.S>0||(g$(P,r),S2V(e.T),S2V(e.L),P.row=15,P.S=a,B=!0)}e.style.set(3);e.S=P;e.S.style=e.style;e.B.mode=1<<P.L;B?P.T=1:P.S!==a&&(P.S>a?(g$(P,r),k5(P,P.row-P.S,a)):P.row<a&&(a=P.S),P.S=a)},FMG=function(e){e.style.set(1);
e.S=e.L;e.S.S=0;e.S.style=e.style;e.B.mode=1<<e.S.L},MuZ=function(e){e.style.set(4);
e.S=e.text;e.S.style=e.style;e.B.mode=1<<e.S.L},lKb=function(e){this.S=e;
this.C=0;this.B=new s_9(this.S);this.Z=new s_9(this.S);this.T=new b1;this.L=this.B},pjB=function(e,a,r,P){e.T.update();
a=wj8(a,r,e.T);switch(a.result){case 0:return;case 1:case 2:return}var B=a.q6;r=a.Ng;if(32<=B||!B)e.S.mode&e.S.T&&(a=B,a&128&&(a=127),r&128&&(r=127),e=e.L.S,a&96&&O_(e,0,a),r&96&&O_(e,0,r),a!==0&&r!==0&&e.style.type===3&&g$(e,P));else if(B&16)a:if(!e.T.matches(B,r)&&(a=e.T,a.q6=B,a.Ng=r,a.state=2,a=B&8?e.Z:e.B,e.L=a,e.S.mode=1<<(e.C<<2)+(a.C<<1)+(a.style.type===4?1:0),(e.S.mode|1<<(e.C<<2)+(a.C<<1)+(a.style.type!==4?1:0))&e.S.T))if(r&64){P=[11,11,1,2,3,4,12,13,14,15,5,6,7,8,9,10][(B&7)<<1|r>>5&1];
e=r&16?((r&14)>>1)*4:0;r=a.S;switch(a.style.get()){case 4:P=r.row;break;case 3:if(P!==r.row){if(P<r.S&&(P=r.S,P===r.row))break;var h=1+r.row-r.S,c=1+P-r.S;RDG(r,c,h,r.S);a=h;B=r.S;c<h?(h=c+B-h,h>0&&(a+=h,B-=h)):(h=h+B-c,h>0&&(B-=h));k5(r,a,B)}}r.row=P;r.T=e+1}else switch(B&7){case 1:switch(r&112){case 32:O_(a.S,0,32);break a;case 48:r===57?(P=a.S,Z2(P).S=0,P.T<32&&P.T++):O_(a.S,1,r&15)}break;case 2:r&32&&O_(a.S,2,r&31);break;case 3:r&32&&O_(a.S,3,r&31);break;case 4:case 5:if(32<=r&&r<=47)switch(r){case 32:FMG(a);
break;case 33:P=a.S;P.T>1&&(--P.T,Z2(P).S=0);break;case 36:P=a.S;a=Z2(P);for(e=0;e<=15;e++)for(r=0;r<=32;r++)if(P.B[e][r]===a){for(;r<=32;r++)P.B[e][r].reset();break}break;case 37:mBV(a,2,P);break;case 38:mBV(a,3,P);break;case 39:mBV(a,4,P);break;case 40:O_(a.S,0,32);break;case 41:a.style.set(2);a.S=a.T;a.S.S=0;a.S.style=a.style;a.B.mode=1<<a.S.L;break;case 42:P=a.text;P.S=15;P.style.set(4);S2V(P);MuZ(a);break;case 43:MuZ(a);break;case 44:e=a.T;switch(a.style.get()){case 1:case 2:case 3:g$(e,P)}k5(e,
0,15);break;case 45:b:{e=a.S;switch(a.style.get()){default:case 2:case 1:break b;case 4:if(e.row<15){++e.row;e.T=1;break b}break;case 3:}e.S<2&&(e.S=2,e.row<e.S&&(e.row=e.S));a=e.row-e.S+1;g$(e,P);RDG(e,a,a+1,e.S-1);k5(e,e.row,1)}break;case 46:k5(a.L,0,15);break;case 47:g$(a.T,P),a.L.updateTime(a.B.time+0),P=a.L,a.L=a.T,a.T=P,FMG(a)}break;case 7:switch(r){case 33:case 34:case 35:P=a.S,(P.T+=r&3)>32&&(P.T=32)}}},j_a=function(){},d$=function(e,a,r,P,B,h,c){h=h===void 0?!1:h;
c=c===void 0?null:c;g.v_.call(this,e,e+a,{priority:r,namespace:"captions"});this.windowId=P;this.text=B;this.append=h;this.S=c},KMG=function(e,a,r,P,B,h,c){var y=h[0],T=c[y.getAttribute("p")];
if(T.eT===1){var X=h[1],f=h[2];h=h[3];y.getAttribute("t");X.getAttribute("t");f.getAttribute("t");h.getAttribute("t");y.getAttribute("p");X.getAttribute("p");h.getAttribute("p");c=c[f.getAttribute("p")];y=uKw(y.textContent,X.textContent,f.textContent,h.textContent,c);return new d$(e,a,B,r,y,P,T)}switch(T.eT){case 9:case 10:T.textEmphasis=1;break;case 11:T.textEmphasis=2;break;case 12:T.textEmphasis=3;break;case 13:T.textEmphasis=4;break;case 14:T.textEmphasis=5}return new d$(e,a,B,r,y.textContent||
"",P,T)},uKw=function(e,a,r,P,B){var h=g.x2(),c=h?g.U9("DIV"):g.U9("RUBY"),y=g.U9("SPAN");
y.textContent=e;c.appendChild(y);e=h?g.U9("DIV"):g.U9("RP");e.textContent=a;c.appendChild(e);a=h?g.U9("DIV"):g.U9("RT");a.textContent=r;c.appendChild(a);r=B.eT;if(r===10||r===11||r===12||r===13||r===14)if(g.vo(a,"text-emphasis-style","filled circle"),g.vo(a,"text-emphasis-color","currentcolor"),g.vo(a,"webkit-text-emphasis","filled circle"),B.eT===11||B.eT===13)g.vo(a,"webkit-text-emphasis-position","under left"),g.vo(a,"text-emphasis-position","under left");r=!0;if(B.eT===4||B.eT===7||B.eT===12||
B.eT===14)g.vo(c,"ruby-position","over"),g.vo(c,"-webkit-ruby-position","before");else if(B.eT===5||B.eT===6||B.eT===11||B.eT===13)g.vo(c,"ruby-position","under"),g.vo(c,"-webkit-ruby-position","after"),r=!1;B=h?g.U9("DIV"):g.U9("RP");B.textContent=P;c.appendChild(B);h&&(P=r,g.vo(c,{display:"inline-block",position:"relative"}),h=c.firstElementChild.nextElementSibling,g.vo(h,"display","none"),h=h.nextElementSibling,g.vo(h,{"font-size":"0.5em","line-height":"1.2em","text-align":"center",position:"absolute",
left:"50%",transform:"translateX(-50%)",width:"400%"}),g.vo(c.lastElementChild,"display","none"),P?(g.vo(c,"padding-top","0.6em"),g.vo(h,"top","0")):(g.vo(c,"padding-bottom","0.6em"),g.vo(h,"bottom","0")));return c},Jy=function(){g.N.apply(this,arguments)},D2=function(e,a,r,P,B){g.v_.call(this,e,e+a,{priority:r,
namespace:"captions"});this.id=P;this.params=B;this.S=[]},ebb=function(e){var a="_"+Vo++;
return new D2(0,0x8000000000000,0,a,e)},$5=function(e){Jy.call(this);
this.L=e;this.pens={};this.Y={};this.N={};this.C="_"+Vo++;this.D={};this.T=this.S=null;this.Z=!0},Qo=function(e,a){e=e.getAttribute(a);
if(e!=null)return Number(e)},WY=function(e,a){e=e.getAttribute(a);
if(e!=null)return e==="1"},l1=function(e,a){e=Qo(e,a);
return e!==void 0?e:null},Y5=function(e,a){e=e.getAttribute(a);
if(e!=null)return Lq.test(e),e},ayB=function(e,a){var r={},P=a.getAttribute("ws");
Object.assign(r,P?e.N[P]:e.L);e=l1(a,"mh");e!=null&&(r.Pu=e);e=l1(a,"ju");e!=null&&(r.textAlign=e);e=l1(a,"pd");e!=null&&(r.s2=e);e=l1(a,"sd");e!=null&&(r.aS=e);e=Y5(a,"wfc");e!=null&&(r.windowColor=e);a=Qo(a,"wfo");a!==void 0&&(r.windowOpacity=a/255);return r},rsG=function(e,a){var r={},P=a.getAttribute("wp");
P&&Object.assign(r,e.Y[P]);e=l1(a,"ap");e!=null&&(r.LW=e);e=Qo(a,"cc");e!=null&&(r.Sk=e);e=Qo(a,"ah");e!=null&&(r.fh=e);e=Qo(a,"rc");e!=null&&(r.I2=e);a=Qo(a,"av");a!=null&&(r.O9=a);return r},Ptl=function(e,a,r,P){var B={};
Object.assign(B,rsG(e,a));Object.assign(B,ayB(e,a));P?g.Cf(B,e.L)?(P=e.C,B=e.L):P="_"+Vo++:P=a.getAttribute("id")||"_"+Vo++;e=Qo(a,"t")+r;a=Qo(a,"d")||0x8000000000000;if(B.s2===2||B.s2===3)r=B.I2,B.I2=B.Sk,B.Sk=r;return new D2(e,a,0,P,B)},w$=function(e){Jy.call(this);
this.Z=e;this.S=new Map;this.L=new Map;this.C=new Map;this.T=new Map},pq=function(e){e=g.ID(Math.round(e),0,16777215).toString(16).toUpperCase();
return"#000000".substring(0,7-e.length)+e},B8M=function(e,a,r,P,B){P===0&&(P=0x8000000000000);
var h={};a.wpWinPosId&&Object.assign(h,e.L.get(a.wpWinPosId));a.wsWinStyleId&&Object.assign(h,e.C.get(a.wsWinStyleId));e=a.rcRowCount;e!==void 0&&(h.I2=e);a=a.ccColCount;a!==void 0&&(h.Sk=a);if(h.s2===2||h.s2===3)a=h.I2,h.I2=h.Sk,h.Sk=a;return new D2(r,P,0,B,h)},i1=function(e,a,r){g.Qn.call(this,e);
this.videoData=a;this.audioTrack=r;this.C=a.TL},z_=function(e,a,r,P,B,h,c,y,T,X){Cq.call(this,e,a,r,P,B,h,c,y,T,X);
this.type=1},Nm=function(e,a,r){this.trackData=e;
this.Z=r;this.version=this.C=this.B=this.byteOffset=0;this.T=[];this.S=new DataView(this.trackData)},x5=function(e){var a=e.byteOffset;
e.byteOffset+=1;return e.S.getUint8(a)},I3=function(e){var a=e.byteOffset;
e.byteOffset+=4;return e.S.getUint32(a)},HY=function(e,a){Jy.call(this);
this.T=e;this.L=a;this.track=this.L.languageName==="CC3"?4:0;this.S=new LMG;this.S.T=1<<this.track},csb=function(e){if(typeof e==="string")return!1;
e=new Nm(e,8,0);return hbG(e)},hbG=function(e){if(!(e.byteOffset<e.S.byteLength)||I3(e)!==1380139777)return!1;
e.version=x5(e);if(e.version>1)return!1;x5(e);x5(e);x5(e);return!0},qm=function(e,a,r,P,B,h,c,y,T,X){Cq.call(this,e,a,r,P,B,h,c,y,T,X);
var f=this;this.type=2;this.TQ=[];this.HW=this.N=this.XM=0;this.Eo=NaN;this.bv=0;this.h9=null;this.T$=new g.vP(this.w$,433,this);this.Z&&(X.createClientVe(this.Z,this,167342),this.j(this.Z,"click",function(){X.logClick(f.Z)}),e=new g.qp(this.element,!0),g.x(this,e),e.subscribe("hoverstart",function(){X.logVisibility(f.Z,!0)},this));
g.rr(this.element,"ytp-caption-window-rollup");g.x(this,this.T$);g.vo(this.element,"overflow","hidden")},oXN=function(e,a){if(!a)return"";
e.B&&e.T.params.aS!==1&&(a*=-1);return"translate"+(e.B?"X":"Y")+"("+a+"px)"},nXZ=function(e){e.TQ=Array.from(e.element.getElementsByClassName("caption-visual-line"));
for(var a=e.T.params.I2,r=0,P=0,B=e.TQ.length-1;r<a&&B>-1;){var h=e.TQ[B];P+=e.B?h.offsetWidth:h.offsetHeight;r++;B--}e.N=P;a=Math;r=a.max;isNaN(e.Eo)&&((P=e.S.Sk)?(B=g.U9("SPAN"),g.ZF(B,"\u2013".repeat(P)),g.vo(B,VuV(e,e.S.BG)),e.L.appendChild(B),e.Eo=B.offsetWidth,e.L.removeChild(B)):e.Eo=0);P=e.L;e.HW=r.call(a,e.Eo,e.bv,(e.B?P.offsetHeight:P.offsetWidth)+1)},ysN=function(e,a){nXZ(e);
var r=e.TQ.reduce(function(h,c){return(e.B?c.offsetWidth:c.offsetHeight)+h},0);
r=e.N-r;if(r!==e.XM){var P=r>0&&e.XM===0,B=r<e.XM;a||isNaN(r)||P||!B||(g.rr(e.element,"ytp-rollup-mode"),e.j(e.element,"transitionend",e.w$));g.vo(e.L,"transform",oXN(e,r));e.XM=r}nXZ(e)},T8N=function(e,a,r,P){var B=this;
this.W=e;this.B=a;this.logger=r;this.QX=P;this.BO=[];this.xg=[];this.S=null;this.Ob={Ihr:function(){return B.S},
vV:function(){return B.BO}};
e=g.eS(this.W.G().experiments,"html5_override_micro_discontinuities_threshold_ms");this.T=e>0?e:10},Asa=function(e,a){e.S=function(r,P){if(r.info.L){var B=r;
if(e.xg.length>0){for(B=e.xg.shift();e.xg.length>0;)B=g.Ox(B,e.xg.shift());B=g.Ox(B,r)}if(B){r=B;try{var h=g.kE(r)*1E3}catch(X){h=r.info.startTime*1E3}try{var c=g.uQ4(r)*1E3}catch(X){c=r.info.duration*1E3}if(h<0||c<0)h<0&&(h=r.info.startTime*1E3),c<0&&(c=r.info.duration*1E3);r.info.startTime=h/1E3;r.info.C=h/1E3;r.info.duration=c/1E3;r.info.Y=c/1E3;h=e.w9(B);c=h.lW;h={formatId:h.formatId,startTimeMs:h.startTimeMs,durationMs:h.durationMs,R$:c,gL:c};c=Xal(e.BO,h.startTimeMs);var y=(r=c>=0?e.BO[c]:null)?
r.startTimeMs+r.durationMs:0,T=h.startTimeMs+h.durationMs;!r||h.startTimeMs-y>e.T?e.BO.splice(c+1,0,h):(r.durationMs=Math.max(y,T)-r.startTimeMs,r.gL=Math.max(r.gL,h.gL));P(e.BO);P=g.Z5(B);fya(e.B,P.buffer.slice(P.byteOffset,P.byteLength+P.byteOffset),a,B.info.C)}else e.logger.S(350058965,"Empty slice")}else e.xg.push(r)};
e.W.addEventListener("sabrCaptionsDataLoaded",e.S)},Xal=function(e,a){e=g.HB(e,{startTimeMs:a},function(r,P){return r.startTimeMs-P.startTimeMs});
return e>=0?e:-e-2},vY=function(e,a){g.Qn.call(this,a);
this.S=e;this.W=a;this.logger=new g.bl("caps");this.L=this.Z=null;this.D=new T8N(this.W,this,this.logger,this.S.QX)},fya=function(e,a,r,P){e.logger.debug(function(){return"SABR captions data received for "+P});
e.Z?e.L==null?e.logger.S(350058965,"Null loaded track meta data at captions data received"):r.lC(a,e.L,P*1E3):e.logger.S(350058965,"Null Representation at captions data received")},Ut8=function(e,a){var r=[],P;
for(P in e.S.S)if(e.S.S.hasOwnProperty(P)){var B=e.S.S[P];if(g.l6(B,a||null)){var h=B.info.id,c=h,y="."+h,T="",X="";if(B=B.info.captionTrack)h=B.languageCode,c=B.displayName,y=B.vssId,T=B.kind,X=B.id;r.push(new g.zF({id:P,languageCode:h,languageName:c,is_servable:!0,is_default:!0,is_translateable:!1,vss_id:y,kind:T,captionId:X}))}}return r},CtG=function(e,a,r){var P=[],B;
for(B in e.S.S)if(e.S.S.hasOwnProperty(B)){var h=e.S.S[B];if(g.l6(h,r||null)){var c=h.info.captionTrack;c&&c.languageCode===a&&P.push(h)}}return P.length?P[0]:null},GSG=function(e,a){if(!g.j0q(e)||e.S!=null&&g.qJN(a,e.S)&&e.S.S.rawcc!=null)return!1;
a=!!e.S&&e.S.isManifestless&&Object.values(e.S.S).some(function(r){return g.l6(r,"386")});
e=!!e.S&&!e.S.isManifestless&&g.fTL(e.S);return a||e},EXN=function(){Jy.call(this)},tgN=function(e){var a=bal.length;
if(e.byteLength<a)return!1;e=new Uint8Array(e,0,a);for(var r=0;r<a;r++)if(bal[r]!==e[r])return!1;return!0},gXa=function(e,a,r,P,B,h,c,y,T){switch(c.tagName){case "b":y.bold=!0;
break;case "i":y.italic=!0;break;case "u":y.underline=!0}for(var X=0;X<c.childNodes.length;X++){var f=c.childNodes[X];if(f.nodeType===3)f=new d$(a,r,P,B.id,f.nodeValue,h||X>0,g.Ak(y)?void 0:y),T.push(f),B.S.push(f);else{var A={};Object.assign(A,y);gXa(e,a,r,P,B,!0,f,A,T)}}},Zab=function(e){var a=e.split(":");
e=0;a=g.Z(a);for(var r=a.next();!r.done;r=a.next())e=e*60+Number(r.value);return e*1E3},Oa5=function(e,a,r,P){P=Object.assign({Pu:0},P);
return new D2(e,a,r,"_"+Vo++,P)},R3=function(e,a){g.N.call(this);
this.W=e;this.iW=a;this.S=null;this.T=this.W.s8();this.logger=new g.bl("caps")},dtB=function(e,a,r){if(typeof a==="string"||csb(a))return[{trackData:a,
aJ:r}];if(typeof a==="string"&&a.substring(0,6)==="WEBVTT"||typeof a!=="string"&&tgN(a))return[{trackData:a,aJ:r}];var P=new DataView(a);if(P.byteLength<=8||P.getUint32(4)!==1718909296)return[];var B=g.e9L(P);if(e.T&&B){var h=g.Sp3(B),c=g.s2O(B);B=B.F6;h&&B&&e.T.Ns(B,h,c)}e=g.gS(P,1835295092);if(!e||!e.length||!e[0].size)return[];h=[];for(c=0;c<e.length;c++)B=e[c],B=new Uint8Array(a,B.dataOffset,B.size-(B.dataOffset-B.offset)),B=g.AT(B),h.push({trackData:B,aJ:r+c*1E3});kSG(P,h,r);return h=h.filter(function(y){return!!y.trackData})},
kSG=function(e,a,r){var P=g.XH(e,0,1836476516),B=9E4;
P&&(B=g.fW(P)||9E4);P=0;for(var h=g.gS(e,1836019558),c=0;c<h.length;c++){var y=h[c];c<a.length&&(y=g.XH(e,y.dataOffset,1953653094))&&(y=g.XH(e,y.dataOffset,1952867444))&&(y=g.b_(y)/B*1E3,c===0&&(P=y),a[c].aJ=y-P+r||r*c*1E3)}},JsG=function(e,a,r){e.S||(e.S=new EXN);
e=e.S.B(a,r);Math.random()<.01&&g.VJ(Error("Deprecated subtitles format in web player: WebVTT"));return e},DtZ=function(e){var a={};
if(e=g.Im(e))a.lang=e,g.AU9.test(e)&&(a.s2=1);return a},QBb=function(e){g.Vn.call(this,e);
var a=this;this.W=e;this.Cf=[];this.TQ={};this.GQ={};this.Y=!1;this.L="NONE";this.S=this.N=this.HW=this.Uo=this.k$=null;this.T$={hH:function(){a.hH()},
lC:function(h,c,y,T){a.lC(h,c,y,T)}};
this.T=null;this.iW=this.W.G();this.videoData=this.W.getVideoData();this.rP=this.W.MF();this.B={BG:{}};this.Z={BG:{}};g.UL(this.videoData)?this.L="OFFLINE":GSG(this.videoData,this.W)?this.L="SABR_LIVE":g.vSL(this.videoData,this.W)?this.L="LIVE":this.videoData.captionTracks.length?this.L="INNERTUBE":this.videoData.Yf&&(this.L="TTS");this.ym=this.iW.controlsType==="3";this.PW=new R3(this.W,this.iW);this.Lf=new g.Zc(this);this.D=new g.v({K:"div",U:"ytp-caption-window-container",J:{id:"ytp-caption-window-container"}});
this.oH={top:0,right:0,bottom:0,left:0,width:1,height:1};var r=null,P=g.LT("yt-html5-player-modules::subtitlesModuleData");P&&(r=new g.kx(P));this.storage=r;var B;this.HA=!((B=e.rg())==null||!B.L6());this.C=VgM(this);this.Eo=!this.C&&this.ym&&this.HA&&(this.L==="LIVE"||this.L==="SABR_LIVE");g.x(this,this.PW);this.C?this.XM=this.e1=null:(this.e1=new g.qe(this.i9,void 0,this),g.x(this,this.e1),this.XM=new g.vP(this.E21,2E3,this),g.x(this,this.XM));g.x(this,this.Lf);g.MY(this.player,this.D.element,4);
g.x(this,this.D);this.C||this.Lf.j(e,"resize",this.xN);(this.bv=g.Pb(this.iW)&&!g.fE()&&!this.W.isFullscreen()&&!this.C&&!this.Eo)&&this.Lf.j(e,"resize",this.lDW);this.Lf.j(e,"onPlaybackAudioChange",this.hdQ);this.Lf.j(e,g.SS("captions"),function(h){a.onCueRangeEnter(h)});
this.Lf.j(e,g.sh("captions"),function(h){a.onCueRangeExit(h)});
$t8(this,UBV()||{});this.player.Og("onCaptionsModuleAvailable")},LwB=function(e){if(e.iW.H8===1||e.videoData.H1===1||g.RG(e.videoData,"yt:cc")==="alwayson")return!0;
if(e.videoData.captionTracks.length)var a=e.getAudioTrack().T;if(e.iW.H8===2){if(g.tZ(e.iW))var r=WwB(e);else if(e.storage)try{r=e.storage.get("module-enabled")}catch(B){e.storage.remove("module-enabled")}else r=null;if(r!=null)return!!r}r=hDB(e.player.getAudioTrack(),g.tZ(e.iW));var P=g.RG(e.videoData,"yt:cc");if(ly9(e)===void 0){if(r==="CAPTIONS_INITIAL_STATE_ON_RECOMMENDED")return P?P==="on":!0;if(r==="CAPTIONS_INITIAL_STATE_OFF_RECOMMENDED")return P==="on"}else return P==="on";return a==="ON"||
g.RG(e.videoData,"yt:cc")==="on"},Sd=function(e,a){if(e.T&&(a===void 0||!a)||!e.videoData.captionTracks.length)return!1;
e=e.getAudioTrack();return!!e.S||e.T==="FORCED_ON"},ly9=function(e){var a=void 0,r=g.pE(g.wu(),65);
if(g.tZ(e.iW)&&r!=null){if(WwB(e)!=null)return!1;a=!r}return a},m8=function(e,a){if(!e.S)return null;
if(e.N&&e.N.L)return e.N.L;a=YWZ(e,a);a=g.o7(e.S.T,a);var r=null;if(cvN(e.iW)){var P=e.W.isInline()?void 0:g.$f("yt-player-caption-sticky-language");for(var B=[P,e.videoData.captionsLanguagePreference,e.iW.captionsLanguagePreference,g.RG(e.videoData,"yt:cc_default_lang")],h=!1,c=0;c<B.length;c++){var y=B[c];if(y){h=!0;for(var T=0;T<a.length;T++)if(g.Im(a[T])===y)return a[T];for(T=0;T<a.length;T++)if(g.Im(a[T]).split("-")[0]===y.split("-")[0])return a[T]}}if(h&&e.S&&(B=e.S.C,B.length))for(B=g.Z(B),
h=B.next();!h.done;h=B.next())if(h=h.value,h.languageCode===P){r=h;break}}else for(P=[e.videoData.captionsLanguagePreference,e.iW.captionsLanguagePreference,g.RG(e.videoData,"yt:cc_default_lang")],B=0;B<P.length;B++)for(h=0;h<a.length;h++)if(g.Im(a[h])===P[B])return a[h];P=null;e.N&&e.N.B&&(P=e.N.B);P||(P=a.find(function(X){return X.isDefault})||null);
P||(P=a[0]||s_(e));P&&r&&g.Im(P).split("-")[0]!==r.languageCode.split("-")[0]&&(P=oC9(P,r));return P},s_=function(e){return e.N&&e.N.S},FY=function(e){var a=s_(e);
return!!a&&e.T===a},wal=function(e,a){var r=e.W.rg().EM().textTracks;
e=e.T.toString();for(var P=0;P<r.length;P++){var B=r[P];B.id===e&&(a?B.mode!=="showing"&&(B.mode="showing"):B.mode==="showing"&&(B.mode="disabled"))}},u1=function(e,a,r){e.loaded&&e.unload();
r!=null&&(e.Y=r,e.Y&&(g.tZ(e.iW)?Mm(e,!!a):jd(e,!!a)));a!==null||Sd(e,!0)||e.Gu(a,!!a,e.Y?"m":"s");e.T=a;Sd(e)&&(e.T=s_(e));var P;pa8(e,(P=e.T)!=null?P:void 0);e.load()},zbb=function(e,a){if(a instanceof D2){var r=e.TQ[a.id];
r&&r.T!==a&&(r.dispose(),delete e.TQ[a.id],r=null);r||(r=iab(e,a))&&(e.TQ[a.id]=r)}else r=a.windowId,e.GQ[r]||(e.GQ[r]=[]),e.GQ[r].push(a)},iab=function(e,a){var r=N8B(e);
if(!r)return null;var P=e.T?g.Im(e.T):null;P&&g.AU9.test(P)&&(a.params.s2=1);var B=e.rP.getPlayerSize();P=B.height*e.oH.height;B=B.width*e.oH.width;e.iW.playerStyle!=="google-live"||e.B.isDefault||Object.assign(a.params,e.B);switch(a.params.Pu!=null?a.params.Pu:a.S.length>1?1:0){case 1:return new z_(a,e.B,e.Z,r.width,r.height,B,P,e.iW.experiments,e.u8.bind(e),e.W);case 2:return new qm(a,e.B,e.Z,r.width,r.height,B,P,e.iW.experiments,e.u8.bind(e),e.W);default:return new Cq(a,e.B,e.Z,r.width,r.height,
B,P,e.iW.experiments,e.u8.bind(e),e.W)}},$t8=function(e,a,r){r=r===void 0?!1:r;
var P=Kq.BG;e.B={};Object.assign(e.B,Kq);e.B.BG={};Object.assign(e.B.BG,P);e.Z={BG:{}};var B=a.backgroundOverride?e.Z:e.B,h=a.background||P.background;Lq.test(h);B.BG.background=h;B=a.colorOverride?e.Z:e.B;h=a.color||P.color;Lq.test(h);B.BG.color=h;B=a.windowColorOverride?e.Z:e.B;h=a.windowColor||Kq.windowColor;Lq.test(h);B.windowColor=h;B=a.backgroundOpacityOverride?e.Z:e.B;h=a.backgroundOpacity;h==null&&(h=P.backgroundOpacity);B.BG.backgroundOpacity=h;B=a.fontSizeIncrementOverride?e.Z:e.B;h=a.fontSizeIncrement;
h==null&&(h=P.fontSizeIncrement);B.BG.fontSizeIncrement=h;h=a.fontStyleOverride?e.Z:e.B;B=a.fontStyle;B==null&&(B=P.bold&&P.italic?3:P.bold?1:P.italic?2:0);h=h.BG;switch(B){case 1:h.bold=!0;delete h.italic;break;case 2:delete h.bold;h.italic=!0;break;case 3:h.bold=!0;h.italic=!0;break;default:delete h.bold,delete h.italic}B=a.textOpacityOverride?e.Z:e.B;h=a.textOpacity;h==null&&(h=P.textOpacity);B.BG.textOpacity=h;B=a.windowOpacityOverride?e.Z:e.B;h=a.windowOpacity;h==null&&(h=Kq.windowOpacity);B.windowOpacity=
h;B=a.charEdgeStyleOverride?e.Z:e.B;h=a.charEdgeStyle;h==null&&(h=P.charEdgeStyle);B.BG.charEdgeStyle=h;B=a.fontFamilyOverride?e.Z:e.B;h=a.fontFamily;h==null&&(h=P.fontFamily);B.BG.fontFamily=h;e.loaded&&e.xN();r&&g.Vy("yt-player-caption-display-settings",a,2592E3)},xt8=function(e,a){if(!e.S)return{};
if(a){g.Ak(a)||e.yJ(a.vss_id,"m");if(e.C||!g.hw(a))return;if(g.Ak(a)){u1(e,null,!0);return}for(var r,P=g.o7(e.S.T,!0),B=0;B<P.length;B++){var h=P[B];h.languageCode!==a.languageCode||r&&(h.languageName!==a.languageName||(h.captionId||"")!==(a.captionId||"")||g.Ng(h)!==a.displayName)||(r=a.translationLanguage?oC9(h,a.translationLanguage):h)}e.hw(a.position);!r||r===e.T&&e.loaded||(a=g.E$(),P=g.Im(r),a.length&&P===a[a.length-1]||(a.push(P),g.Vy("yt-player-caption-language-preferences",a)),cvN(e.iW)&&
!e.W.isInline()&&g.Vy("yt-player-caption-sticky-language",P,2592E3),u1(e,r,!0))}else return e.loaded&&e.T&&!FY(e)?g.xG(e.T):{};return""},HaN=function(e,a,r){a&&!e.HW?(a=ebb({s2:0,
lang:"en"}),e.HW=[a,new d$(a.start,a.end-a.start,0,a.id,r!=null?r:"Captions look like this")],e.player.Ji(e.HW)):!a&&e.HW&&(IyG(e,e.HW),e.HW=null)},IyG=function(e,a){e.player.FU(a);
a=g.Z(a);for(var r=a.next();!r.done;r=a.next())g.k9(e.Cf,r.value);fq(e.e1)},pa8=function(e,a){e.iW.X("html5_modify_caption_vss_logging")&&(e.videoData.G7=a)},N8B=function(e){var a=e.rP.getVideoContentRect(!0).height,r=e.rP.getVideoContentRect(!0).width;
if(!a||!r)return null;a*=e.oH.height;r*=e.oH.width;return{width:r,height:a}},jd=function(e,a){if(e.storage)try{e.storage.set("module-enabled",a)}catch(r){}},Mm=function(e,a){e.W.isInline()||g.Vy("yt-player-sticky-caption",a,2592E3)},WwB=function(e){if(!e.W.isInline())return g.$f("yt-player-sticky-caption")},VgM=function(e){var a,r=!((a=e.W.rg())==null||!a.O6());
return e.ym&&r&&e.L!=="LIVE"&&e.L!=="SABR_LIVE"},YWZ=function(e,a){g.Bx(e.videoData)&&(a=!0);
a||(a=e.L==="TTS"?!1:e.L==="INNERTUBE"?!1:!0);return a||e.iW.X("web_deprecate_always_includes_asr_setting")&&g.tZ(e.iW)?!0:!!g.pE(g.wu(),66)};
g.Qn.prototype.M3=g.BI(71,function(){return!1});
g.Qn.prototype.xX=g.BI(70,function(){});
g.Wp.prototype.xX=g.BI(69,function(e,a,r){var P=this;this.wP();a=this.zz(e,a);var B=this.j1.G().X("html5_report_captions_ctmp_qoe"),h=(0,g.Ov)();this.IG();B_Z(this,a,{format:"RAW",onSuccess:function(c){P.S=null;if(B){var y=(c.responseText.length/1024).toFixed(),T=(0,g.Ov)();P.videoData.nf("capresp",{ms:T-h,kb:y})}r.lC(c.responseText,e)},
onError:B?function(c){var y;c=(y=c==null?void 0:c.status)!=null?y:0;P.videoData.nf("capfail",{status:c})}:void 0,
withCredentials:!0})});
g.Lk.prototype.xX=g.BI(68,function(e,a,r){var P=this;this.wP();a=this.zz(e,a);this.IG();this.S=g.ge(a,{format:"RAW",onSuccess:function(B){P.S=null;r.lC(B.responseText,e)},
withCredentials:!0})});
g.ji.prototype.n9=g.BI(67,function(){for(var e=g.o8(document,"track",void 0,this.S),a=0;a<e.length;a++)g.t5(e[a])});
g.RA.prototype.n9=g.BI(66,function(){this.mediaElement.n9()});
g.ji.prototype.L6=g.BI(65,function(){return!(!this.S.textTracks||!this.S.textTracks.addEventListener)});
g.RA.prototype.L6=g.BI(64,function(){return this.mediaElement.L6()});
g.ji.prototype.O6=g.BI(63,function(){return!!this.S.textTracks});
g.RA.prototype.O6=g.BI(62,function(){return this.mediaElement.O6()});
g.ji.prototype.WC=g.BI(61,function(e){for(var a=0;a<e.length;a++)this.S.appendChild(e[a])});
g.RA.prototype.WC=g.BI(60,function(e){this.mediaElement.WC(e)});
g.Au.prototype.Ns=g.BI(59,function(e,a,r){this.cO.Ns(e,a,r)});
g.B0.prototype.Ns=g.BI(58,function(e,a,r){this.T.set(e,{g2:a,Fg:r})});
g.hN.prototype.Ns=g.BI(57,function(e,a,r){this.oH.Ns(e,a,r)});
g.Au.prototype.Lj=g.BI(56,function(e){return this.cO.Lj(e)});
g.hN.prototype.Lj=g.BI(55,function(e){var a=2;this.C.has(e)?a=0:g.mZJ(this,e)&&(a=1);return a});
g.Hx.prototype.Vn=g.BI(43,function(e){var a=this.app.sb({playerType:void 0});return a?a.Vn(e):!1});
g.ra.prototype.Vn=g.BI(42,function(e){return this.uW.Ca.Vn(e)});
g.zq.prototype.Vn=g.BI(41,function(e){return this.AR().some(function(a){return a.namespace===e})});
g.M3.prototype.Vn=g.BI(40,function(){return!1});
g.Hx.prototype.yJ=g.BI(38,function(e,a){this.app.Y$().yJ(e,a)});
g.ra.prototype.yJ=g.BI(37,function(e,a){this.uW.yJ(e,a)});
g.B7.prototype.yJ=g.BI(36,function(e,a){e=[e,a];g.P7(this,g.c7(this.provider),"cfi",e)});
g.iH.prototype.yJ=g.BI(35,function(e,a){this.qoe&&this.qoe.yJ(e,a)});
g.on.prototype.yJ=g.BI(34,function(e,a){this.cA.yJ(e,a)});
g.M3.prototype.yJ=g.BI(33,function(){});
g.Hx.prototype.Gu=g.BI(32,function(e,a,r){this.app.Y$().Gu(e,a,r)});
g.ra.prototype.Gu=g.BI(31,function(e,a,r){this.uW.Gu(e,a,r)});
g.B7.prototype.Gu=g.BI(30,function(e,a,r){if(this.e1!==e||this.dW!==a)a=a==="rawcc"?"":a,r=[e,a,this.e1,r],g.P7(this,g.c7(this.provider),"cfs",r),this.e1=e,this.dW=a});
g.iH.prototype.Gu=g.BI(29,function(e,a,r){this.qoe&&this.qoe.Gu(e,a,r)});
g.on.prototype.Gu=g.BI(28,function(e,a,r){this.cA.Gu(e,a,r)});
g.M3.prototype.Gu=g.BI(27,function(){});
g.fJ.prototype.m$=g.BI(12,function(){return this.Y});
g.gZ.prototype.m$=g.BI(11,function(){return this.Y$().s8()});
g.Hx.prototype.s8=g.BI(10,function(){return this.app.m$()});
g.ra.prototype.s8=g.BI(9,function(){return this.uW.s8()});
g.on.prototype.s8=g.BI(8,function(){var e;return((e=this.loader)==null?void 0:e.m$())||null});
g.M3.prototype.s8=g.BI(7,function(){return null});
g.uf.prototype.pF=g.BI(1,function(e){return(e=this.Ds(e))?e.S:0});
g.VH.prototype.pF=g.BI(0,function(){return 0});
var XjN=/#(.)(.)(.)/,T_Z=/^#(?:[0-9a-f]{3}){1,2}$/i,tuB={aa:"Afar",ab:"Abkhazian",ace:"Acehnese",ach:"Acoli",ada:"Adangme",ady:"Adyghe",ae:"Avestan",aeb:"Tunisian Arabic",af:"Afrikaans",afh:"Afrihili",agq:"Aghem",ain:"Ainu",ak:"Akan",akk:"Akkadian",akz:"Alabama",ale:"Aleut",aln:"Gheg Albanian",alt:"Southern Altai",am:"Amharic",an:"Aragonese",ang:"Old English",anp:"Angika",ar:"Arabic",ar_001:"Arabic (world)",arc:"Aramaic",arn:"Mapuche",aro:"Araona",arp:"Arapaho",arq:"Algerian Arabic",ars:"Najdi Arabic",
arw:"Arawak",ary:"Moroccan Arabic",arz:"Egyptian Arabic",as:"Assamese",asa:"Asu",ase:"American Sign Language",ast:"Asturian",av:"Avaric",avk:"Kotava",awa:"Awadhi",ay:"Aymara",az:"Azerbaijani",az_Cyrl:"Azerbaijani (Cyrillic)",az_Latn:"Azerbaijani (Latin)",ba:"Bashkir",bal:"Baluchi",ban:"Balinese",bar:"Bavarian",bas:"Basaa",bax:"Bamun",bbc:"Batak Toba",bbj:"Ghomala",be:"Belarusian",bej:"Beja",bem:"Bemba",bew:"Betawi",bez:"Bena",bfd:"Bafut",bfq:"Badaga",bg:"Bulgarian",bgc:"Haryanvi",bgn:"Western Balochi",
bho:"Bhojpuri",bi:"Bislama",bik:"Bikol",bin:"Bini",bjn:"Banjar",bkm:"Kom",bla:"Siksik\u00e1",blo:"Anii",bm:"Bambara",bn:"Bangla",bo:"Tibetan",bpy:"Bishnupriya",bqi:"Bakhtiari",br:"Breton",bra:"Braj",brh:"Brahui",brx:"Bodo",bs:"Bosnian",bs_Cyrl:"Bosnian (Cyrillic)",bs_Latn:"Bosnian (Latin)",bss:"Akoose",bua:"Buriat",bug:"Buginese",bum:"Bulu",byn:"Blin",byv:"Medumba",ca:"Catalan",cad:"Caddo",car:"Carib",cay:"Cayuga",cch:"Atsam",ccp:"Chakma",ce:"Chechen",ceb:"Cebuano",cgg:"Chiga",ch:"Chamorro",chb:"Chibcha",
chg:"Chagatai",chk:"Chuukese",chm:"Mari",chn:"Chinook Jargon",cho:"Choctaw",chp:"Chipewyan",chr:"Cherokee",chy:"Cheyenne",ckb:"Central Kurdish",co:"Corsican",cop:"Coptic",cps:"Capiznon",cr:"Cree",crh:"Crimean Tatar",cs:"Czech",csb:"Kashubian",csw:"Swampy Cree",cu:"Church Slavic",cv:"Chuvash",cy:"Welsh",da:"Danish",dak:"Dakota",dar:"Dargwa",dav:"Taita",de:"German",de_AT:"German (Austria)",de_CH:"German (Switzerland)",del:"Delaware",den:"Slave",dgr:"Dogrib",din:"Dinka",dje:"Zarma",doi:"Dogri",dsb:"Lower Sorbian",
dua:"Duala",dum:"Middle Dutch",dv:"Divehi",dyo:"Jola-Fonyi",dyu:"Dyula",dz:"Dzongkha",dzg:"Dazaga",ebu:"Embu",ee:"Ewe",efi:"Efik",egy:"Ancient Egyptian",eka:"Ekajuk",el:"Greek",elx:"Elamite",en:"English",en_AU:"English (Australia)",en_CA:"English (Canada)",en_GB:"English (United Kingdom)",en_US:"English (United States)",enm:"Middle English",eo:"Esperanto",es:"Spanish",es_419:"Spanish (Latin America)",es_ES:"Spanish (Spain)",es_MX:"Spanish (Mexico)",et:"Estonian",eu:"Basque",ewo:"Ewondo",fa:"Persian",
fa_AF:"Persian (Afghanistan)",fan:"Fang",fat:"Fanti",ff:"Fula",ff_Adlm:"Fula (Adlam)",ff_Latn:"Fula (Latin)",fi:"Finnish",fil:"Filipino",fj:"Fijian",fo:"Faroese",fon:"Fon",fr:"French",fr_CA:"French (Canada)",fr_CH:"French (Switzerland)",frm:"Middle French",fro:"Old French",frr:"Northern Frisian",frs:"Eastern Frisian",fur:"Friulian",fy:"Western Frisian",ga:"Irish",gaa:"Ga",gay:"Gayo",gba:"Gbaya",gd:"Scottish Gaelic",gez:"Geez",gil:"Gilbertese",gl:"Galician",gmh:"Middle High German",gn:"Guarani",goh:"Old High German",
gon:"Gondi",gor:"Gorontalo",got:"Gothic",grb:"Grebo",grc:"Ancient Greek",gsw:"Swiss German",gu:"Gujarati",guz:"Gusii",gv:"Manx",gwi:"Gwich\u02bcin",ha:"Hausa",hai:"Haida",haw:"Hawaiian",he:"Hebrew",hi:"Hindi",hi_Latn:"Hindi (Latin)",hil:"Hiligaynon",hit:"Hittite",hmn:"Hmong",ho:"Hiri Motu",hr:"Croatian",hsb:"Upper Sorbian",ht:"Haitian Creole",hu:"Hungarian",hup:"Hupa",hy:"Armenian",hz:"Herero",ia:"Interlingua",iba:"Iban",ibb:"Ibibio",id:"Indonesian",ie:"Interlingue",ig:"Igbo",ii:"Sichuan Yi",ik:"Inupiaq",
ilo:"Iloko","in":"Indonesian",inh:"Ingush",io:"Ido",is:"Icelandic",it:"Italian",iu:"Inuktitut",iw:"Hebrew",ja:"Japanese",jbo:"Lojban",jgo:"Ngomba",jmc:"Machame",jpr:"Judeo-Persian",jrb:"Judeo-Arabic",jv:"Javanese",ka:"Georgian",kaa:"Kara-Kalpak",kab:"Kabyle",kac:"Kachin",kaj:"Jju",kam:"Kamba",kaw:"Kawi",kbd:"Kabardian",kbl:"Kanembu",kcg:"Tyap",kde:"Makonde",kea:"Kabuverdianu",kfo:"Koro",kg:"Kongo",kgp:"Kaingang",kha:"Khasi",kho:"Khotanese",khq:"Koyra Chiini",ki:"Kikuyu",kj:"Kuanyama",kk:"Kazakh",
kk_Cyrl:"Kazakh (Cyrillic)",kkj:"Kako",kl:"Kalaallisut",kln:"Kalenjin",km:"Khmer",kmb:"Kimbundu",kn:"Kannada",ko:"Korean",kok:"Konkani",kok_Deva:"Konkani (Devanagari)",kok_Latn:"Konkani (Latin)",kos:"Kosraean",kpe:"Kpelle",kr:"Kanuri",krc:"Karachay-Balkar",krl:"Karelian",kru:"Kurukh",ks:"Kashmiri",ks_Arab:"Kashmiri (Arabic)",ks_Deva:"Kashmiri (Devanagari)",ksb:"Shambala",ksf:"Bafia",ksh:"Colognian",ku:"Kurdish",kum:"Kumyk",kut:"Kutenai",kv:"Komi",kw:"Cornish",kxv:"Kuvi",kxv_Deva:"Kuvi (Devanagari)",
kxv_Latn:"Kuvi (Latin)",kxv_Orya:"Kuvi (Odia)",kxv_Telu:"Kuvi (Telugu)",ky:"Kyrgyz",la:"Latin",lad:"Ladino",lag:"Langi",lah:"Western Panjabi",lam:"Lamba",lb:"Luxembourgish",lez:"Lezghian",lg:"Ganda",li:"Limburgish",lij:"Ligurian",lkt:"Lakota",lmo:"Lombard",ln:"Lingala",lo:"Lao",lol:"Mongo",loz:"Lozi",lrc:"Northern Luri",lt:"Lithuanian",lu:"Luba-Katanga",lua:"Luba-Lulua",lui:"Luiseno",lun:"Lunda",luo:"Luo",lus:"Mizo",luy:"Luyia",lv:"Latvian",mad:"Madurese",maf:"Mafa",mag:"Magahi",mai:"Maithili",mak:"Makasar",
man:"Mandingo",mas:"Masai",mde:"Maba",mdf:"Moksha",mdr:"Mandar",men:"Mende",mer:"Meru",mfe:"Morisyen",mg:"Malagasy",mga:"Middle Irish",mgh:"Makhuwa-Meetto",mgo:"Meta\u02bc",mh:"Marshallese",mi:"M\u0101ori",mic:"Mi'kmaw",min:"Minangkabau",mk:"Macedonian",ml:"Malayalam",mn:"Mongolian",mnc:"Manchu",mni:"Manipuri",mni_Beng:"Manipuri (Bangla)",mo:"Romanian",moh:"Mohawk",mos:"Mossi",mr:"Marathi",ms:"Malay",mt:"Maltese",mua:"Mundang",mul:"Multiple languages",mus:"Muscogee",mwl:"Mirandese",mwr:"Marwari",
my:"Burmese",mye:"Myene",myv:"Erzya",mzn:"Mazanderani",na:"Nauru",nap:"Neapolitan",naq:"Nama",nb:"Norwegian Bokm\u00e5l",nd:"North Ndebele",nds:"Low German",nds_NL:"Low German (Netherlands)",ne:"Nepali","new":"Newari",ng:"Ndonga",nia:"Nias",niu:"Niuean",nl:"Dutch",nl_BE:"Dutch (Belgium)",nmg:"Kwasio",nn:"Norwegian Nynorsk",nnh:"Ngiemboon",no:"Norwegian",nog:"Nogai",non:"Old Norse",nqo:"N\u2019Ko",nr:"South Ndebele",nso:"Northern Sotho",nus:"Nuer",nv:"Navajo",nwc:"Classical Newari",ny:"Nyanja",nym:"Nyamwezi",
nyn:"Nyankole",nyo:"Nyoro",nzi:"Nzima",oc:"Occitan",oj:"Ojibwa",om:"Oromo",or:"Odia",os:"Ossetic",osa:"Osage",ota:"Ottoman Turkish",pa:"Punjabi",pa_Arab:"Punjabi (Arabic)",pa_Guru:"Punjabi (Gurmukhi)",pag:"Pangasinan",pal:"Pahlavi",pam:"Pampanga",pap:"Papiamento",pau:"Palauan",pcm:"Nigerian Pidgin",peo:"Old Persian",phn:"Phoenician",pi:"Pali",pl:"Polish",pon:"Pohnpeian",prg:"Prussian",pro:"Old Proven\u00e7al",ps:"Pashto",pt:"Portuguese",pt_BR:"Portuguese (Brazil)",pt_PT:"Portuguese (Portugal)",qu:"Quechua",
raj:"Rajasthani",rap:"Rapanui",rar:"Rarotongan",rm:"Romansh",rn:"Rundi",ro:"Romanian",ro_MD:"Romanian (Moldova)",rof:"Rombo",rom:"Romany",ru:"Russian",rup:"Aromanian",rw:"Kinyarwanda",rwk:"Rwa",sa:"Sanskrit",sad:"Sandawe",sah:"Yakut",sam:"Samaritan Aramaic",saq:"Samburu",sas:"Sasak",sat:"Santali",sat_Olck:"Santali (Ol Chiki)",sba:"Ngambay",sbp:"Sangu",sc:"Sardinian",scn:"Sicilian",sco:"Scots",sd:"Sindhi",sd_Arab:"Sindhi (Arabic)",sd_Deva:"Sindhi (Devanagari)",se:"Northern Sami",see:"Seneca",seh:"Sena",
sel:"Selkup",ses:"Koyraboro Senni",sg:"Sango",sga:"Old Irish",sh:"Serbo-Croatian",shi:"Tachelhit",shi_Latn:"Tachelhit (Latin)",shi_Tfng:"Tachelhit (Tifinagh)",shn:"Shan",shu:"Chadian Arabic",si:"Sinhala",sid:"Sidamo",sk:"Slovak",sl:"Slovenian",sm:"Samoan",sma:"Southern Sami",smj:"Lule Sami",smn:"Inari Sami",sms:"Skolt Sami",sn:"Shona",snk:"Soninke",so:"Somali",sog:"Sogdien",sq:"Albanian",sr:"Serbian",sr_Cyrl:"Serbian (Cyrillic)",sr_Latn:"Serbian (Latin)",srn:"Sranan Tongo",srr:"Serer",ss:"Swati",
ssy:"Saho",st:"Southern Sotho",su:"Sundanese",su_Latn:"Sundanese (Latin)",suk:"Sukuma",sus:"Susu",sux:"Sumerian",sv:"Swedish",sw:"Swahili",sw_CD:"Swahili (Congo - Kinshasa)",swb:"Comorian",syc:"Classical Syriac",syr:"Syriac",szl:"Silesian",ta:"Tamil",te:"Telugu",tem:"Timne",teo:"Teso",ter:"Tereno",tet:"Tetum",tg:"Tajik",th:"Thai",ti:"Tigrinya",tig:"Tigre",tiv:"Tiv",tk:"Turkmen",tkl:"Tokelau",tl:"Tagalog",tlh:"Klingon",tli:"Tlingit",tmh:"Tamashek",tn:"Tswana",to:"Tongan",tog:"Nyasa Tonga",tok:"Toki Pona",
tpi:"Tok Pisin",tr:"Turkish",trv:"Taroko",ts:"Tsonga",tsi:"Tsimshian",tt:"Tatar",tum:"Tumbuka",tvl:"Tuvalu",tw:"Twi",twq:"Tasawaq",ty:"Tahitian",tyv:"Tuvinian",tzm:"Central Atlas Tamazight",udm:"Udmurt",ug:"Uyghur",uga:"Ugaritic",uk:"Ukrainian",umb:"Umbundu",ur:"Urdu",uz:"Uzbek",uz_Arab:"Uzbek (Arabic)",uz_Cyrl:"Uzbek (Cyrillic)",uz_Latn:"Uzbek (Latin)",vai:"Vai",vai_Latn:"Vai (Latin)",vai_Vaii:"Vai (Vai)",ve:"Venda",vec:"Venetian",vi:"Vietnamese",vmw:"Makhuwa",vo:"Volap\u00fck",vot:"Votic",vun:"Vunjo",
wa:"Walloon",wae:"Walser",wal:"Wolaytta",war:"Waray",was:"Washo",wo:"Wolof",xal:"Kalmyk",xh:"Xhosa",xnr:"Kangri",xog:"Soga",yao:"Yao",yap:"Yapese",yav:"Yangben",ybb:"Yemba",yi:"Yiddish",yo:"Yoruba",yrl:"Nheengatu",yue:"Cantonese",yue_Hans:"Cantonese (Simplified)",yue_Hant:"Cantonese (Traditional)",za:"Zhuang",zap:"Zapotec",zbl:"Blissymbols",zen:"Zenaga",zgh:"Standard Moroccan Tamazight",zh:"Chinese",zh_Hans:"Chinese (Simplified)",zh_Hant:"Chinese (Traditional)",zh_TW:"Chinese (Taiwan)",zu:"Zulu",
zun:"Zuni",zxx:"No linguistic content",zza:"Zaza"};Ay.prototype.contains=function(e){e=g.HB(this.segments,e);return e>=0||e<0&&(-e-1)%2===1};
Ay.prototype.length=function(){return this.segments.length/2};
g.b(GRZ,g.N);g.E=GRZ.prototype;g.E.RH=function(){g.N.prototype.RH.call(this);this.B&&this.B.cancel()};
g.E.zd=function(){this.seekTo(this.player.getCurrentTime())};
g.E.seekTo=function(e){e-=this.player.t9();var a=this.S;this.S=g.GV(this.HW.Uc(e).Vm);a!==this.S&&this.N&&this.N()};
g.E.reset=function(){this.L=new Ay;this.Z=-1;this.B&&(this.B.cancel(),this.B=null)};
g.E.Rj=function(){this.wP();var e;if(e=this.S!=null)e=this.S,e=e.S.Gg(e);if(e&&!this.B&&!(this.S&&this.S.startTime-this.player.getCurrentTime()>30)){e=this.S;e=e.S.tX(e);var a=e.Vm[0],r;if((r=this.player.getVideoData())==null?0:r.enableServerStitchedDai)if(r=this.player.s8()){var P=a.S.info.id,B=a.lW,h=e.Vm[0].C;if(this.policy.Cf){if(r=r.Bh(h,B,P,3))e.L=r}else if(P=r.jE(h,B,P,3))if(r=r.Lj(B),r===0)P&&(e.S=new g.v2(P));else if(r===2){this.D.start();ECN(this)&&this.seekTo(this.player.getCurrentTime());
return}}a.S.index.du(a.lW)?(this.L.contains(e.Vm[0].lW)||bvG(this,e),this.S=g.GV(e.Vm)):ECN(this)&&this.seekTo(this.player.getCurrentTime())}this.D.start()};
g.b(U_,g.Qn);g.E=U_.prototype;g.E.xX=function(e,a,r){var P=this;this.IG();a=ZvN(this,e.getId());a||(a=e.languageCode,a=this.S.isManifestless?OvG(this,a,"386"):OvG(this,a));if(a){var B=(a.index.pF(a.index.mQ())-a.index.getStartTime(a.index.mQ()))*1E3,h=this.W.G(),c=new g.wMj(h),y=function(){P.L&&P.L.reset();P.Z=!0};
h.X("html5_keep_caption_data_after_seek")&&(y=null);this.L=new GRZ(c,this.W,a,function(T,X){r.lC(T,e,X,B)},this.D||g.m2(a.info),y)}};
g.E.M3=function(){var e=this.Z;this.Z=!1;return e};
g.E.ov=function(e){var a=this.W.G().X("html5_fallback_if_rawcc_missing");var r=this.S.S.rawcc!=null;if(!this.D||!r&&a)a=this.S.isManifestless?gCw(this,"386"):gCw(this);else{if(!r){this.logger.S(386248249,"rawcc used but unavailable");return}a=[new g.zF({id:"rawcc",languageCode:"rawcc",languageName:"CC1",is_servable:!0,is_default:!0,is_translateable:!1,vss_id:".en"}),new g.zF({id:"rawcc",languageCode:"rawcc",languageName:"CC3",is_servable:!0,is_default:!0,is_translateable:!1,vss_id:".en"})]}a=g.Z(a);
for(r=a.next();!r.done;r=a.next())g.$p(this.T,r.value);e.hH()};
g.E.IG=function(){this.L&&(this.L.dispose(),this.L=null)};
g.E.zz=function(){return""};
var Lq=/^#(?:[0-9a-f]{3}){1,2}$/i;var JvB=["left","right","center","justify"];g.b(Cq,g.v);g.E=Cq.prototype;g.E.OV=function(e,a){this.ym=e;this.PW=a;var r=g.eN(this.element,this.element.parentElement);this.k$=e-r.x;this.rP=a-r.y};
g.E.ge=function(e,a){if(e!==this.ym||a!==this.PW){g.ac(this.element,"ytp-dragging")||g.rr(this.element,"ytp-dragging");var r=g.rF(this.element);e=e-this.k$-.02*this.Q2;var P=a-this.rP-.02*this.Te,B=(e+r.width/2)/this.maxWidth*3;B=Math.floor(g.ID(B,0,2));var h=(P+r.height/2)/this.maxHeight*3;h=Math.floor(g.ID(h,0,2));a=B+h*3;e=(e+B/2*r.width)/this.maxWidth;e=g.ID(e,0,1)*100;r=(P+h/2*r.height)/this.maxHeight;r=g.ID(r,0,1)*100;this.T.params.LW=a;this.T.params.O9=r;this.T.params.fh=e;this.T.params.isDefault=
!1;this.S.LW=a;this.S.O9=r;this.S.fh=e;this.S.isDefault=!1;this.oH.LW=a;this.oH.O9=r;this.oH.fh=e;this.oH.isDefault=!1;this.Z8()}};
g.E.BF=function(){g.BD(this.element,"ytp-dragging")};
g.E.Z8=function(){this.hU(this.D)};
g.E.hU=function(e){var a=this.HA?0:Math.min(this.KO(),this.maxWidth),r=this.EW(),P=this.HA;if(P){var B=getComputedStyle(this.L.parentNode);B=G_(B.borderLeftWidth)+G_(B.borderRightWidth)+G_(B.paddingLeft)+G_(B.paddingRight)}else B=0;var h=B;B="";this.T.params.s2===3&&(B="rotate(180deg)");var c=P?"calc(96% - "+h+"px)":"96%";g.vo(this.element,{top:0,left:0,right:"",bottom:"",width:a?a+"px":"",height:r?r+"px":"","max-width":c,"max-height":c,margin:"",transform:""});this.u3(e);B={transform:B,top:"",left:"",
width:a?a+"px":"",height:r?r+"px":"","max-width":"","max-height":""};var y=this.S.fh*.96+2;c=this.S.LW;switch(c){case 0:case 3:case 6:(P=this.S.BG.fontSizeIncrement)&&P>0&&this.S.s2!==2&&this.S.s2!==3&&(y=Math.max(y/(1+P*2),2));B.left=y+"%";break;case 1:case 4:case 7:B.left=y+"%";y=this.L.offsetWidth;a||y?(a=a||y+1,B.width=a+"px",B["margin-left"]=P?a/-2-h/2+"px":a/-2+"px"):B.transform+=" translateX(-50%)";break;case 2:case 5:case 8:B.right=100-y+"%"}P=this.S.O9*.96+2;switch(c){case 0:case 1:case 2:B.top=
P+"%";break;case 3:case 4:case 5:B.top=P+"%";(r=r||this.element.clientHeight)?(B.height=r+"px",B["margin-top"]=r/-2+"px"):B.transform+=" translateY(-50%)";break;case 6:case 7:case 8:B.bottom=100-P+"%"}g.vo(this.element,B);if(this.Z){r=this.L.offsetHeight;P=1;for(a=0;a<e.length;a++)B=e[a],typeof B.text==="string"&&(P+=B.text.split("\n").length-1,B.append||a===0||P++);r/=P;this.Z.style.height=r+"px";this.Z.style.width=r+"px";this.element.style.paddingLeft=r+5+"px";this.element.style.paddingRight=r+
5+"px";e=Number(this.element.style.marginLeft.replace("px",""))-r-5;r=Number(this.element.style.marginRight.replace("px",""))-r-5;this.element.style.marginLeft=e+"px";this.element.style.marginRight=r+"px"}};
g.E.u3=function(e){var a;for(a=0;a<e.length&&e[a]===this.D[a];a++);if(this.Lf||this.D.length>a)a=0,this.Lf=!1,this.D=[],this.Y=this.e1=this.Cf=null,g.E9(this.L);for(;a<e.length;a++)WMB(this,e[a])};
g.E.KO=function(){return 0};
g.E.EW=function(){return 0};
g.E.toString=function(){return g.v.prototype.toString.call(this)};LMG.prototype.clear=function(){this.B=this.time=this.mode=0;this.S=[];this.reset()};
LMG.prototype.reset=function(){this.mode=0;this.L.reset(0);this.C.reset(1)};
var Y2l=[128,1,2,131,4,133,134,7,8,137,138,11,140,13,14,143,16,145,146,19,148,21,22,151,152,25,26,155,28,157,158,31,32,161,162,35,164,37,38,167,168,41,42,171,44,173,174,47,176,49,50,179,52,181,182,55,56,185,186,59,188,61,62,191,64,193,194,67,196,69,70,199,200,73,74,203,76,205,206,79,208,81,82,211,84,213,214,87,88,217,218,91,220,93,94,223,224,97,98,227,100,229,230,103,104,233,234,107,236,109,110,239,112,241,242,115,244,117,118,247,248,121,122,251,124,253,254,127,0,129,130,3,132,5,6,135,136,9,10,139,
12,141,142,15,144,17,18,147,20,149,150,23,24,153,154,27,156,29,30,159,160,33,34,163,36,165,166,39,40,169,170,43,172,45,46,175,48,177,178,51,180,53,54,183,184,57,58,187,60,189,190,63,192,65,66,195,68,197,198,71,72,201,202,75,204,77,78,207,80,209,210,83,212,85,86,215,216,89,90,219,92,221,222,95,96,225,226,99,228,101,102,231,232,105,106,235,108,237,238,111,240,113,114,243,116,245,246,119,120,249,250,123,252,125,126,255];E_.prototype.set=function(e){this.type=e};
E_.prototype.get=function(){return this.type};
b1.prototype.clear=function(){this.state=0};
b1.prototype.update=function(){this.state=this.state===2?1:0};
b1.prototype.Fw=function(){return this.state!==0};
b1.prototype.matches=function(e,a){return this.Fw()&&e===this.q6&&a===this.Ng};
N_N.prototype.reset=function(){this.timestamp=this.S=0};
ty.prototype.updateTime=function(e){for(var a=1;a<=15;++a)for(var r=1;r<=32;++r)this.B[a][r].timestamp=e};
ty.prototype.debugString=function(){for(var e="\n",a=1;a<=15;++a){for(var r=1;r<=32;++r){var P=this.B[a][r];e=P.S===0?e+"_":e+String.fromCharCode(P.S)}e+="\n"}return e};
ty.prototype.reset=function(e){for(var a=0;a<=15;a++)for(var r=0;r<=32;r++)this.B[a][r].reset();this.L=e;this.S=0;this.T=this.row=1};
var xBN=[32,33,34,35,36,37,38,39,40,41,225,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,233,93,237,243,250,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,231,247,209,241,9632],IK9=[174,176,189,191,8482,162,163,9834,224,32,232,226,234,238,244,251],HvB=[193,201,211,218,220,252,8216,161,42,39,9473,169,8480,183,8220,8221,192,194,199,200,202,203,235,
206,207,239,212,217,249,219,171,187],q2Z=[195,227,205,204,236,210,242,213,245,123,125,92,94,95,124,126,196,228,214,246,223,165,164,9475,197,229,216,248,9487,9491,9495,9499];s_9.prototype.reset=function(e,a){this.C=a;this.style.set(2);this.T=this.Z;this.L=this.D;this.S=this.T;var r=(e<<2)+(a<<1);this.Z.reset(r);this.D.reset(r);this.text.reset((e<<2)+(a<<1)+1)};
lKb.prototype.reset=function(e){this.C=e;this.T.clear();this.L=this.B;this.B.reset(e,0);this.Z.reset(e,1)};j_a.prototype.L=function(){};g.b(d$,g.v_);d$.prototype.toString=function(){return g.v_.prototype.toString.call(this)};g.b(Jy,g.N);Jy.prototype.reset=function(){};g.b(D2,g.v_);D2.prototype.toString=function(){return g.v_.prototype.toString.call(this)};
var Vo=0;g.b($5,Jy);$5.prototype.reset=function(){this.D={};this.T=this.S=null;this.Z=!0};
$5.prototype.B=function(e,a){e=e.firstChild;e.getAttribute("format");a=a||0;Number.isFinite(a);e=Array.from(e.childNodes);e=g.Z(e);for(var r=e.next();!r.done;r=e.next())if(r=r.value,r.nodeType===1)switch(r.tagName){case "head":var P=r;break;case "body":var B=r}if(P)for(P=Array.from(P.childNodes),P=g.Z(P),e=P.next();!e.done;e=P.next())if(e=e.value,e.nodeType===1)switch(e.tagName){case "pen":r=e.getAttribute("id");var h=this.pens,c={},y=e.getAttribute("p");y&&Object.assign(c,this.pens[y]);y=WY(e,"b");
y!=null&&(c.bold=y);y=WY(e,"i");y!=null&&(c.italic=y);y=WY(e,"u");y!=null&&(c.underline=y);y=l1(e,"et");y!=null&&(c.charEdgeStyle=y);y=l1(e,"of");y!=null&&(c.offset=y);y=Y5(e,"bc");y!=null&&(c.background=y);y=Y5(e,"ec");y!=null&&(c.e7=y);y=Y5(e,"fc");y!=null&&(c.color=y);y=l1(e,"fs");y!=null&&y!==0&&(c.fontFamily=y);y=Qo(e,"sz");y!==void 0&&(c.fontSizeIncrement=y/100-1);y=Qo(e,"bo");y!==void 0&&(c.backgroundOpacity=y/255);y=Qo(e,"fo");y!==void 0&&(c.textOpacity=y/255);y=l1(e,"rb");y!=null&&y!==10&&
y!==0&&(c.eT=y>10?y-1:y);e=l1(e,"hg");e!=null&&(c.Lp=e);h[r]=c;break;case "ws":r=e.getAttribute("id");this.N[r]=ayB(this,e);break;case "wp":r=e.getAttribute("id"),this.Y[r]=rsG(this,e)}if(B){P=[];B=Array.from(B.childNodes);B=g.Z(B);for(e=B.next();!e.done;e=B.next())if(e=e.value,e.nodeType===1)switch(e.tagName){case "w":this.S=Ptl(this,e,a,!1);(e=this.D[this.S.id])&&e.end>this.S.start&&(e.end=this.S.start);this.D[this.S.id]=this.S;P.push(this.S);break;case "p":var T=e;y=a;r=[];h=T.getAttribute("w")||
this.C;c=!!WY(T,"a");y=(Qo(T,"t")||0)+y;var X=Qo(T,"d")||5E3;c||(!this.Z&&this.T&&this.T.windowId===h&&this.T.end>y&&(this.T.end=y),this.T&&this.T.text==="\n"&&(this.T.text=""));var f=c?6:5,A=T.getAttribute("p");A=A?this.pens[A]:null;var U=Array.from(T.childNodes);U.length&&(this.Z=T.getAttribute("d")!=null);for(T=0;T<U.length;T++){var C=U[T],G=void 0;T>0&&(c=!0);var O=void 0;C.nodeType===1&&(O=C);if(O&&O.tagName==="s"){if((C=(C=O.getAttribute("p"))?this.pens[C]:null)&&C.eT&&(C.eT===1?(C=U.slice(T,
T+4),C.length===4&&(G=KMG(y,X,h,c,f,C,this.pens),T+=3)):G=KMG(y,X,h,c,f,[O],this.pens)),!G){var d=O;G=y;O=X;C=h;var W=c,V=f,L=d.textContent?d.textContent:"",z=d.getAttribute("p");z=z?this.pens[z]:null;d=Qo(d,"t")||0;G=new d$(G+d,O-d,V,C,L,W,z)}}else G=new d$(y,X,f,h,C.textContent||"",c,A);r.push(G);this.T=G}if(r.length>0)for(r[0].windowId===this.C&&(this.S=Ptl(this,e,a,!0),P.push(this.S)),e=g.Z(r),r=e.next();!r.done;r=e.next())r=r.value,r.windowId=this.S.id,this.S.S.push(r),P.push(r)}a=P}else a=[];
return a};var qWa=new Map([[9,1],[10,1],[11,2],[12,3],[13,4],[14,5]]);g.b(w$,Jy);w$.prototype.reset=function(){this.T.clear()};
w$.prototype.B=function(e,a){var r=JSON.parse(e);if(!r)return[];if(r.pens){e=0;for(var P=g.Z(r.pens),B=P.next();!B.done;B=P.next()){B=B.value;var h={},c=B.pParentId;c&&Object.assign(h,this.S.get(c));B.bAttr&&(h.bold=!0);B.iAttr&&(h.italic=!0);B.uAttr&&(h.underline=!0);c=B.ofOffset;c!=null&&(h.offset=c);B.szPenSize!==void 0&&(h.fontSizeIncrement=B.szPenSize/100-1);c=B.etEdgeType;c!=null&&(h.charEdgeStyle=c);B.ecEdgeColor!==void 0&&(h.e7=pq(B.ecEdgeColor));c=B.fsFontStyle;c!=null&&c!==0&&(h.fontFamily=
c);B.fcForeColor!==void 0&&(h.color=pq(B.fcForeColor));B.foForeAlpha!==void 0&&(h.textOpacity=B.foForeAlpha/255);B.bcBackColor!==void 0&&(h.background=pq(B.bcBackColor));B.boBackAlpha!==void 0&&(h.backgroundOpacity=B.boBackAlpha/255);(c=B.rbRuby)&&c!==10&&(h.eT=c>10?c-1:c,h.textEmphasis=qWa.get(h.eT));B.hgHorizGroup&&(h.Lp=B.hgHorizGroup);this.S.set(e++,h)}}if(r.wsWinStyles)for(e=0,P=g.Z(r.wsWinStyles),B=P.next();!B.done;B=P.next())B=B.value,h={},(c=B.wsParentId)?Object.assign(h,this.C.get(c)):Object.assign(h,
this.Z),B.mhModeHint!==void 0&&(h.Pu=B.mhModeHint),B.juJustifCode!==void 0&&(h.textAlign=B.juJustifCode),B.pdPrintDir!==void 0&&(h.s2=B.pdPrintDir),B.sdScrollDir!==void 0&&(h.aS=B.sdScrollDir),B.wfcWinFillColor!==void 0&&(h.windowColor=pq(B.wfcWinFillColor)),B.wfoWinFillAlpha!==void 0&&(h.windowOpacity=B.wfoWinFillAlpha/255),this.C.set(e++,h);if(r.wpWinPositions)for(e=0,P=g.Z(r.wpWinPositions),B=P.next();!B.done;B=P.next())B=B.value,h={},(c=B.wpParentId)&&Object.assign(h,this.L.get(c)),B.ahHorPos!==
void 0&&(h.fh=B.ahHorPos),B.apPoint!==void 0&&(h.LW=B.apPoint),B.avVerPos!==void 0&&(h.O9=B.avVerPos),B.ccCols!==void 0&&(h.Sk=B.ccCols),B.rcRows!==void 0&&(h.I2=B.rcRows),this.L.set(e++,h);if(r.events){e=[];r=g.Z(r.events);for(P=r.next();!P.done;P=r.next()){var y=P.value;B=(y.tStartMs||0)+a;h=y.dDurationMs||0;if(y.id)c=String(y.id),P=B8M(this,y,B,h,c),e.push(P),this.T.set(c,P);else{y.wWinId?c=y.wWinId.toString():(c="_"+Vo++,P=B8M(this,y,B,h,c),e.push(P),this.T.set(c,P));P=e;var T=y;h===0&&(h=5E3);
y=this.T.get(c);var X=!!T.aAppend,f=X?6:5,A=T.segs,U=null;T.pPenId&&(U=this.S.get(T.pPenId));for(T=0;T<A.length;T++){var C=A[T],G=C.utf8;if(G){var O=C.tOffsetMs||0,d=null;C.pPenId&&(d=this.S.get(C.pPenId));if((y.params.Pu!=null?y.params.Pu:y.S.length>1?1:0)===2&&X&&G==="\n")continue;C=null;var W=[],V;if(V=d&&d.eT===1){V=A;var L=T;if(L+3>=V.length||!V[L+1].pPenId||!V[L+2].pPenId||!V[L+3].pPenId)V=!1;else{var z=V[L+1].pPenId;(z=this.S.get(z))&&z.eT&&z.eT===2?(z=V[L+2].pPenId,z=this.S.get(z),!z||!z.eT||
z.eT<3?V=!1:(z=V[L+3].pPenId,V=(z=this.S.get(z))&&z.eT&&z.eT===2?!0:!1)):V=!1}}if(V)O=A[T+1].utf8,C=A[T+3].utf8,V=A[T+2].utf8,L=this.S.get(A[T+2].pPenId),G=uKw(G,O,V,C,L),C=new d$(B,h,f,c,G,X,d),T+=3;else{if(G.indexOf("<")>-1){var q=void 0;W=d;V=U;L=B;z=h;var M=O,m=f,cI=X,CL=[],nL=g.KS("<html>"+G+"</html>");if(!nL.getElementsByTagName("parsererror").length&&((q=nL.firstChild)==null?0:q.childNodes.length))for(q=g.Z(nL.firstChild.childNodes),nL=q.next();!nL.done;nL=q.next()){nL=nL.value;var y3=void 0,
aT=void 0,$N=(aT=(y3=nL.textContent)==null?void 0:y3.replace(/\n/g,""))!=null?aT:"";if(nL.nodeType!==3||$N&&$N.match(/^ *$/)==null){y3={};Object.assign(y3,W||V);aT=void 0;switch((aT=nL)==null?void 0:aT.tagName){case "b":y3.bold=!0;break;case "i":y3.italic=!0;break;case "u":y3.underline=!0}CL.push(new d$(L+M,z-M,m,y.id,$N,cI,y3))}}W=CL}W.length||(W=[new d$(B+O,h-O,f,y.id,G,X,d||U)])}if(W.length)for(X=g.Z(W),d=X.next();!d.done;d=X.next())d=d.value,P.push(d),y.S.push(d);else C&&(P.push(C),y.S.push(C))}X=
!0}}}a=e}else a=[];return a};g.b(i1,g.Qn);i1.prototype.xX=function(e,a,r){yvl(this.videoData.videoId,e.vssId,r.lC)};
i1.prototype.ov=function(e){if(this.audioTrack)for(var a=g.Z(this.audioTrack.captionTracks),r=a.next();!r.done;r=a.next())g.$p(this.T,r.value);e.hH()};g.b(z_,Cq);z_.prototype.u3=function(e){var a=this.T.S;Cq.prototype.u3.call(this,e);for(e=e.length;e<a.length;e++){var r=a[e];if(h&&r.S===B)var P=h;else{P={};Object.assign(P,r.S);Object.assign(P,vXG);var B=r.S;var h=P}WMB(this,r,P)}};
var vXG={pI:!0};g.b(Nm,j_a);Nm.prototype.L=function(e,a,r,P,B){if(r<P){var h="_"+Vo++;r=r/1E3-this.Z;P=P/1E3-this.Z;e=new D2(r,P-r,5,h,{textAlign:0,LW:0,fh:a*2.5,O9:e*5.33});B=new d$(r,P-r,5,h,B);this.T.push(e);this.T.push(B)}};
g.b(HY,Jy);HY.prototype.B=function(e){e=new Nm(e,e.byteLength,this.T);if(hbG(e)){for(;e.byteOffset<e.S.byteLength;)for(e.version===0?e.B=I3(e)*(1E3/45):e.version===1&&(e.B=I3(e)*4294967296+I3(e)),e.C=x5(e);e.C>0;e.C--){var a=x5(e),r=x5(e),P=x5(e);a&4&&(a&3)===this.track&&(this.track===0||this.track===1)&&(a=this.S,a.S.push({time:e.B,type:this.track,oB:r,Sz:P,order:a.S.length}))}zDB(this.S,e);return e.T}return[]};
HY.prototype.reset=function(){this.S.clear()};g.b(qm,Cq);g.E=qm.prototype;g.E.Z8=function(){g.ms(this.T$)};
g.E.w$=function(){this.element.removeEventListener("transitionend",this.w$,!1);g.BD(this.element,"ytp-rollup-mode");this.hU(this.h9,!0)};
g.E.EW=function(){return this.B?this.HW:this.N};
g.E.KO=function(){return this.B?this.N:this.HW};
g.E.hU=function(e,a){this.h9=e;if(this.T.params.I2){for(var r=0,P=0;P<this.D.length&&r<e.length;P++)this.D[P]===e[r]&&r++;r>0&&r<e.length&&(e=this.D.concat(e.slice(r)));this.bv=this.HW;this.N=this.HW=0;Cq.prototype.hU.call(this,e);ysN(this,a)}Cq.prototype.hU.call(this,e)};T8N.prototype.unload=function(){this.S!=null&&(this.W.removeEventListener("sabrCaptionsDataLoaded",this.S),this.S=null);this.BO=[];this.W.publish("sabrCaptionsBufferedRangesUpdated",this.BO)};
T8N.prototype.w9=function(e){return{formatId:g.MW(e.info.S.info,this.QX),lW:e.info.lW+(this.QX?0:1),startTimeMs:e.info.C*1E3,durationMs:e.info.Y*1E3}};g.b(vY,g.Qn);vY.prototype.xX=function(e,a,r){this.IG();a=e.getId();a=a!=null&&a in this.S.S?this.S.S[a]:null;a||(a=e.languageCode,a=this.S.isManifestless?CtG(this,a,"386"):CtG(this,a));a&&(this.L=e,this.Z=a,Asa(this.D,r),this.W.publish("sabrCaptionsTrackChanged",g.MW(a.info,this.S.QX)))};
vY.prototype.ov=function(e){var a=this.S.isManifestless?Ut8(this,"386"):Ut8(this);a=g.Z(a);for(var r=a.next();!r.done;r=a.next())g.$p(this.T,r.value);e.hH()};
vY.prototype.IG=function(){this.L&&(this.L=this.Z=null,this.D.unload(),this.W.publish("sabrCaptionsTrackChanged",null))};
vY.prototype.zz=function(){return""};var bal="WEBVTT".split("").map(function(e){return e.charCodeAt(0)});
g.b(EXN,Jy);
EXN.prototype.B=function(e,a){e instanceof ArrayBuffer&&(e=g.AT(new Uint8Array(e)));var r=[];e=e.split(RbN);for(var P=1;P<e.length;P++){var B=e[P],h=a;if(B!==""&&!SWN.test(B)){var c=sBN.exec(B);if(c&&c.length>=4){var y=Zab(c[1]),T=Zab(c[2])-y;y+=h;var X=(c=c[3])?c.split(" "):[];c={};var f=null;var A="";var U=null,C="";X=g.Z(X);for(var G=X.next();!G.done;G=X.next())if(G=G.value.split(":"),G.length===2){var O=G[1];switch(G[0]){case "line":G=O.split(",");G[0].endsWith("%")&&(f=G[0],c.O9=Number.parseInt(f,
10),G.length===2&&(A=G[1].trim()));break;case "position":G=O.split(",");U=G[0];c.fh=Number.parseInt(U,10);G.length===2&&(C=G[1].trim());break;case "align":switch(O){case "start":c.textAlign=0;break;case "middle":c.textAlign=2;break;case "end":c.textAlign=1}}}f||A||(A="end");if(!U)switch(c.textAlign){case 0:c.fh=0;break;case 1:c.fh=100;break;case 2:c.fh=50}if(c.textAlign!=null){f=0;switch(A){case "center":f+=3;break;case "end":f+=6;break;default:f+=0}switch(C){case "line-left":f+=0;break;case "center":f+=
1;break;case "line-right":f+=2;break;default:switch(c.textAlign){case 0:f+=0;break;case 2:f+=1;break;case 1:f+=2}}A=f<0||f>8?7:f;c.LW=A}B=B.substring(sBN.lastIndex).replace(/[\x01-\x09\x0b-\x1f]/g,"");C=c;c=B;B={};if(c.indexOf("<")<0&&c.indexOf("&")<0)h=Oa5(y,T,5,C),T=new d$(y,T,5,h.id,c,!1,g.Ak(B)?void 0:B),r.push(h),r.push(T),h.S.push(T);else for(A=c.split(mtN),A.length===1?(c=5,C=Oa5(y,T,c,C)):(f=c=6,C=Object.assign({Sk:32},C),C=new D2(y,T,f,"_"+Vo++,C)),r.push(C),f=y,U=0;U<A.length;U++)X=A[U],
U%2===0?(G=g.KS("<html>"+X+"</html>"),G.getElementsByTagName("parsererror").length?(O=G.createElement("span"),O.appendChild(G.createTextNode(X))):O=G.firstChild,gXa(this,f,T-(f-y),c,C,U>0,O,B,r)):f=Zab(X)+h}sBN.lastIndex=0}}return r};
var SWN=/^NOTE/,RbN=/(?:\r\n|\r|\n){2,}/,sBN=RegExp("^((?:[\\d]{2}:)?[\\d]{2}:[\\d]{2}\\.[\\d]{3})[\\t ]+--\x3e[\\t ]+((?:[\\d]{2}:)?[\\d]{2}:[\\d]{2}\\.[\\d]{3})(?:[\\t ]*)(.*)(?:\\r\\n|\\r|\\n)","gm"),mtN=RegExp("<((?:[\\d]{2}:)?[\\d]{2}:[\\d]{2}\\.[\\d]{3})>");g.b(R3,g.N);R3.prototype.clear=function(){this.S&&this.S.dispose();this.S=null};
R3.prototype.reset=function(){this.S&&this.S.reset()};
R3.prototype.RH=function(){g.N.prototype.RH.call(this);this.clear()};var Kq={windowColor:"#080808",windowOpacity:0,textAlign:2,LW:7,fh:50,O9:100,isDefault:!0,BG:{background:"#080808",backgroundOpacity:.75,charEdgeStyle:0,color:"#fff",fontFamily:4,fontSizeIncrement:0,textOpacity:1,offset:1}};g.b(QBb,g.Vn);g.E=QBb.prototype;g.E.RH=function(){if(this.C||this.Eo){var e=this.W.rg();e&&!e.wP()&&e.n9()}else HaN(this,!1);g.Vn.prototype.RH.call(this)};
g.E.MP=function(){return this.iW.X("html5_honor_caption_availabilities_in_audio_track")&&this.L!=="LIVE"&&this.L!=="SABR_LIVE"};
g.E.VB=function(){if(this.ym)return this.C||this.Eo;var e=this.getAudioTrack();if(this.MP()){if(!e.captionTracks.length)return!1;if(!this.S)return!0}e=hDB(e,g.tZ(this.iW));return e==="CAPTIONS_INITIAL_STATE_ON_REQUIRED"?!0:e==="CAPTIONS_INITIAL_STATE_OFF_REQUIRED"?Sd(this):ly9(this)||Sd(this)?!0:LwB(this)};
g.E.load=function(){g.Vn.prototype.load.call(this);this.N=this.getAudioTrack();if(this.S)this.T&&(this.PW.clear(),this.C?wal(this,!0):this.player.getPresentingPlayerType()!==3&&this.S.xX(this.T,"json3",this.T$),this.C||this.Eo||FY(this)||this.player.Og("captionschanged",g.xG(this.T)));else{var e;this.L==="OFFLINE"?e=new i1(this.player,this.videoData,this.getAudioTrack()):this.L==="SABR_LIVE"?e=new vY(this.videoData.S,this.player):this.L==="LIVE"?e=new U_(this.videoData.S,this.player):this.L==="INNERTUBE"?
e=new g.Wp(this.player,this.videoData,this.getAudioTrack()):e=new g.Lk(this.player,this.videoData.Yf,this.videoData.videoId,g.a3O(this.videoData),this.videoData.SQ,this.videoData.eventId);this.S=e;g.x(this,this.S);this.S.ov(this.T$)}};
g.E.unload=function(){this.C&&this.T?wal(this,!1):(this.XM&&g.Fu(this.XM),this.player.Ai("captions"),this.Cf=[],this.S&&this.S.IG(),this.PW.clear(),this.HW&&this.player.Ji(this.HW),this.xN());g.Vn.prototype.unload.call(this);this.player.s3();this.player.Og("captionschanged",{})};
g.E.create=function(){this.VB()&&this.load();var e;a:{var a,r,P;if(this.iW.X("web_player_nitrate_promo_tooltip")&&((a=this.videoData.getPlayerResponse())==null?0:(r=a.captions)==null?0:(P=r.playerCaptionsTracklistRenderer)==null?0:P.enableTouchCaptionsNitrate)){var B,h;if(a=(e=this.videoData.getPlayerResponse())==null?void 0:(B=e.captions)==null?void 0:(h=B.playerCaptionsTracklistRenderer)==null?void 0:h.captionTracks)for(e=g.Z(a),B=e.next();!B.done;B=e.next())if(B=B.value,B.kind==="asr"&&B.languageCode===
"en"){e=!0;break a}}e=!1}e&&this.W.publish("showpromotooltip",this.D.element)};
g.E.hH=function(){var e=hDB(this.player.getAudioTrack(),g.tZ(this.iW));var a=e==="CAPTIONS_INITIAL_STATE_ON_REQUIRED"?m8(this,this.Y):e==="CAPTIONS_INITIAL_STATE_OFF_REQUIRED"&&Sd(this)?s_(this):ly9(this)||this.Y||LwB(this)?m8(this,this.Y):Sd(this)?s_(this):null;if(this.C||this.Eo){var r=g.o7(this.S.T,!0);e=[];for(var P=0;P<r.length;P++){var B=r[P],h=g.U9("TRACK");h.setAttribute("kind","subtitles");h.setAttribute("label",g.Ng(B));h.setAttribute("srclang",g.Im(B));h.setAttribute("id",B.toString());
this.Eo||h.setAttribute("src",this.S.zz(B,"vtt"));B===a&&h.setAttribute("default","1");e.push(h)}a=this.W.rg();a.WC(e);e=a.EM();this.HA&&this.Lf.j(e.textTracks,"change",this.Efo)}else!this.T&&a&&u1(this,a),this.player.Og("onCaptionsTrackListChanged"),this.player.cG("onApiChange")};
g.E.Efo=function(){for(var e=this.W.rg().EM().textTracks,a=null,r=0;r<e.length;r++)if(e[r].mode==="showing")a:{a=g.o7(this.S.T,!0);for(var P=0;P<a.length;P++)if(a[P].toString()===e[r].id){a=a[P];break a}a=null}(this.loaded?this.T:null)!==a&&u1(this,a,!0)};
g.E.Fqd=function(){!this.T&&this.C||this.unload()};
g.E.lC=function(e,a,r,P){if(e){var B;pa8(this,(B=this.T)!=null?B:void 0);this.S.M3()&&(this.Cf=[],this.W.Ai("captions"),fq(this.e1),this.PW.reset());a:{B=this.PW;P=P||0;r=dtB(B,e,r||0);e=[];try{for(var h=g.Z(r),c=h.next();!c.done;c=h.next()){var y=c.value,T=y.trackData,X=y.aJ,f=B.iW.X("safari_live_drm_captions_fix");if(typeof T!=="string"){r=e;var A=r.concat;if(f&&tgN(T))var U=JsG(B,T,X);else{var C=B,G=a,O=T,d=X,W=P;if(!csb(O))throw Error("Invalid binary caption track data");C.S||(C.S=new HY(W,G));
U=C.S.B(O,d)}var V=A.call(r,U)}else{if(T.substring(0,6)==="WEBVTT")var L=e.concat(JsG(B,T,X));else{r=e;var z=r.concat;b:{G=C=void 0;O=B;d=a;if(T[0]==="{")try{O.S||(O.S=new w$(DtZ(d)));var q=G=O.S.B(T,X);break b}catch(aT){g.D6(aT);q=[];break b}var M=g.KS(T);if(!M||!M.firstChild){var m=Error("Invalid caption track data");m.params=T;throw m;}if(M.firstChild.tagName==="timedtext"){if(Number(M.firstChild.getAttribute("format"))===3){O.S||(O.S=new $5(DtZ(d),O.iW));q=C=O.S.B(M,X);break b}var cI=Error("Unsupported subtitles format in web player (Format2)");
cI.params=T;throw cI;}if(M.firstChild.tagName==="transcript"){var CL=Error("Unsupported subtitles format in web player (Format1)");CL.params=T;throw CL;}var nL=Error("Invalid caption track data");nL.params=T;throw nL;}L=z.call(r,q)}V=L}e=V}var y3=e;break a}catch(aT){B.logger.S(187101178,"Captions parsing failed: "+aT.message+". ");B.clear();y3=[];break a}y3=void 0}a=y3;a.length>0&&(y3=this.T,this.Gu(y3,!!y3,FY(this)?"g":this.Y?"m":"s"));y3=!this.iW.X("html5_keep_caption_data_after_seek")&&(this.L===
"LIVE"||this.L==="SABR_LIVE");this.player.Ji(a,void 0,y3);!this.Y||this.Eo||FY(this)||g.Uh(this.iW)||g.hZ(this.iW)||g.L$(this.iW)||this.iW.Lf==="shortspage"||this.player.isInline()||(g.Fu(this.XM),a=ebb({LW:0,fh:5,O9:5,I2:2,textAlign:0,s2:0,lang:"en"}),this.Uo=[a],y3=["Click "," for settings"],this.k$||(h=new g.Ps(g.kT()),g.x(this,h),this.k$=h.element),h=a.end-a.start,(c=g.Ng(this.T))&&this.Uo.push(new d$(a.start,h,0,a.id,c)),this.Uo.push(new d$(a.start,h,0,a.id,y3[0]),new d$(a.start,h,0,a.id,this.k$,
!0),new d$(a.start,h,0,a.id,y3[1],!0)),this.player.Ji(this.Uo),g.sC(this.XM));!this.Y||this.Eo||FY(this)||(g.tZ(this.iW)?Mm(this,!0):jd(this,!0),this.N&&(this.N.L=this.T),this.player.s3());this.Y=!1}};
g.E.onCueRangeEnter=function(e){this.Cf.push(e);fq(this.e1)};
g.E.onCueRangeExit=function(e){g.k9(this.Cf,e);this.S instanceof U_&&this.S.D&&this.player.FU([e]);fq(this.e1)};
g.E.getCaptionWindowContainerId=function(){return this.D.element.id};
g.E.E21=function(){IyG(this,this.Uo);this.Uo=null};
g.E.i9=function(){var e=this;if(!this.bv||!this.C){this.e1.stop();g.pIN(this.GQ);this.Cf.sort(g.RF);var a=this.Cf;if(this.HW){var r=g.QX(a,function(h){return this.HW.indexOf(h)===-1},this);
r.length&&(a=r)}a=g.Z(a);for(r=a.next();!r.done;r=a.next())zbb(this,r.value);a=g.Z(Object.entries(this.TQ));var P=a.next();for(r={};!P.done;r={nR:void 0,U9:void 0},P=a.next()){var B=g.Z(P.value);P=B.next().value;B=B.next().value;r.nR=P;r.U9=B;this.GQ[r.nR]?(r.U9.element.parentNode||(r.U9 instanceof qm||r.U9 instanceof z_||g.r6(this.TQ,function(h){return function(c,y){y!==h.nR&&c.T.params.LW===h.U9.T.params.LW&&c.T.params.fh===h.U9.T.params.fh&&c.T.params.O9===h.U9.T.params.O9&&(c.dispose(),delete e.TQ[y]);
return y===h.nR}}(r),this),this.D.element.appendChild(r.U9.element)),r.U9.hU(this.GQ[r.nR])):(r.U9.dispose(),delete this.TQ[r.nR])}}};
g.E.lwW=function(){$t8(this,{},!0);this.player.Og("captionssettingschanged")};
g.E.EP=function(){var e=Kq.BG;e={background:e.background,backgroundOpacity:e.backgroundOpacity,charEdgeStyle:e.charEdgeStyle,color:e.color,fontFamily:e.fontFamily,fontSizeIncrement:e.fontSizeIncrement,fontStyle:e.bold&&e.italic?3:e.bold?1:e.italic?2:0,textOpacity:e.textOpacity,windowColor:Kq.windowColor,windowOpacity:Kq.windowOpacity};var a=UBV()||{};a.background!=null&&(e.background=a.background);a.backgroundOverride!=null&&(e.backgroundOverride=a.backgroundOverride);a.backgroundOpacity!=null&&(e.backgroundOpacity=
a.backgroundOpacity);a.backgroundOpacityOverride!=null&&(e.backgroundOpacityOverride=a.backgroundOpacityOverride);a.charEdgeStyle!=null&&(e.charEdgeStyle=a.charEdgeStyle);a.charEdgeStyleOverride!=null&&(e.charEdgeStyleOverride=a.charEdgeStyleOverride);a.color!=null&&(e.color=a.color);a.colorOverride!=null&&(e.colorOverride=a.colorOverride);a.fontFamily!=null&&(e.fontFamily=a.fontFamily);a.fontFamilyOverride!=null&&(e.fontFamilyOverride=a.fontFamilyOverride);a.fontSizeIncrement!=null&&(e.fontSizeIncrement=
a.fontSizeIncrement);a.fontSizeIncrementOverride!=null&&(e.fontSizeIncrementOverride=a.fontSizeIncrementOverride);a.fontStyle!=null&&(e.fontStyle=a.fontStyle);a.fontStyleOverride!=null&&(e.fontStyleOverride=a.fontStyleOverride);a.textOpacity!=null&&(e.textOpacity=a.textOpacity);a.textOpacityOverride!=null&&(e.textOpacityOverride=a.textOpacityOverride);a.windowColor!=null&&(e.windowColor=a.windowColor);a.windowColorOverride!=null&&(e.windowColorOverride=a.windowColorOverride);a.windowOpacity!=null&&
(e.windowOpacity=a.windowOpacity);a.windowOpacityOverride!=null&&(e.windowOpacityOverride=a.windowOpacityOverride);return e};
g.E.pV=function(e,a){var r={};Object.assign(r,UBV());Object.assign(r,e);$t8(this,r,a);this.player.Og("captionssettingschanged")};
g.E.xN=function(){!this.C&&this.loaded&&(g.K5(this.TQ,function(e,a){e.dispose();delete this.TQ[a]},this),this.i9())};
g.E.i$=function(e,a){switch(e){case "fontSize":if(isNaN(a))break;e=g.ID(a,-2,4);this.pV({fontSizeIncrement:e});return e;case "reload":a&&!this.C&&u1(this,this.T,!0);break;case "stickyLoading":a!==void 0&&this.iW.Y&&(g.tZ(this.iW)?Mm(this,!!a):jd(this,!!a));break;case "track":return xt8(this,a);case "tracklist":return this.S?g.ba(g.o7(this.S.T,!(!a||!a.includeAsr)),function(r){return g.xG(r)}):[];
case "translationLanguages":return this.S?this.S.C.map(function(r){return Object.assign({},r)}):[];
case "sampleSubtitles":this.C||a===void 0||HaN(this,!!a);break;case "sampleSubtitlesCustomized":this.C||HaN(this,!!a,a);break;case "recommendedTranslationLanguages":return g.E$();case "defaultTranslationSourceTrackIndices":return this.S?this.S.Y:[]}};
g.E.getOptions=function(){var e="reload fontSize track tracklist translationLanguages sampleSubtitle".split(" ");this.iW.Y&&e.push("stickyLoading");return e};
g.E.Zh=function(){var e=this.T;if(this.W.Vn("captions")){if(this.iW.X("html5_modify_caption_vss_logging")){var a;return(e=(a=this.videoData.G7)!=null?a:null)?{cc:g.Zj4(e)}:{}}if(e)return a=e.vssId,e.translationLanguage&&a&&(a="t"+a+"."+g.Im(e)),{cc:a}}return{}};
g.E.fZQ=function(){this.isSubtitlesOn()?(g.tZ(this.iW)?Mm(this,!1):jd(this,!1),pa8(this),u1(this,null,!0)):this.jY()};
g.E.jY=function(){var e=FY(this)||!this.T?m8(this,!0):this.T;e&&this.yJ(e.vssId,"m");this.isSubtitlesOn()||u1(this,FY(this)||!this.T?m8(this,!0):this.T,!0)};
g.E.isSubtitlesOn=function(){return!!this.loaded&&!!this.T&&!FY(this)};
g.E.hdQ=function(){var e=FY(this);Sd(this,e)?u1(this,this.getAudioTrack().S,!1):this.videoData.captionTracks.length&&(this.loaded&&this.unload(),this.MP()&&(this.Y=!1,this.T=null,this.S&&(this.S.dispose(),this.S=null)),this.VB()&&(e?u1(this,m8(this,!1),!1):this.load()))};
g.E.hw=function(e){e&&(this.oH={top:e.top,right:e.right,bottom:e.bottom,left:e.left,width:1-e.left-e.right,height:1-e.top-e.bottom},this.D.element.style.top=this.oH.top*100+"%",this.D.element.style.left=this.oH.left*100+"%",this.D.element.style.width=this.oH.width*100+"%",this.D.element.style.height=this.oH.height*100+"%",this.D.element.style.position="absolute",e=N8B(this))&&(this.D.element.style.width=e.width+"px",this.D.element.style.height=e.height+"px")};
g.E.onVideoDataChange=function(e,a){e==="newdata"&&(this.videoData=a,this.loaded&&this.unload(),this.Y=!1,this.T=null,this.S&&(this.S.dispose(),this.S=null,this.player.Og("captionschanged",{})),this.VB()&&this.load())};
g.E.getAudioTrack=function(){return this.player.getAudioTrack()};
g.E.lDW=function(){var e=this.W.rg();e&&!e.wP()&&e.n9();this.W.isFullscreen()?(this.C=this.ym=!0,this.loaded&&this.hH()):(this.ym=this.iW.controlsType==="3",this.C=VgM(this));u1(this,this.T)};
g.E.u8=function(){var e,a,r,P=(e=this.videoData.getPlayerResponse())==null?void 0:(a=e.captions)==null?void 0:(r=a.playerCaptionsTracklistRenderer)==null?void 0:r.openTranscriptCommand;P&&this.player.qF("innertubeCommand",P)};
g.E.Gu=function(e,a,r){var P=/&|,|:|;|(\n)|(\s)|(\/)|(\\)/gm,B="";e&&(B=e.vssId,B=B.replace(P,""));var h="";e&&e.getId()&&(h=e.getId()||"");e&&e.getXtags()&&(e=e.getXtags(),e=e.replace(P,""),h=h.concat(";"+e));this.W.Gu(a?B:"",a?h:"",r)};
g.E.yJ=function(e,a){e=(e||"").replace(/&|,|:|;|(\n)|(\s)|(\/)|(\\)/gm,"");e.length>0&&this.W.yJ(e,a)};g.DD("captions",QBb);})(_yt_player);

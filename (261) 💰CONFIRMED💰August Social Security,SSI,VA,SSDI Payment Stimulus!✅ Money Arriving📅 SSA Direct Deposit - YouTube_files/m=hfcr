"use strict";this.default_IdentityRotateCookiesHttp=this.default_IdentityRotateCookiesHttp||{};(function(_){var window=this;
try{
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles_default_IdentityRotateCookiesHttp=a||[]};(0,_._F_toggles_initialize)([0xc000, ]);
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var aa=function(a){g.setTimeout(()=>{throw a;},0)},ba=function(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b},l=function(a){a=Error(a);ba(a,"warning");return a},m=function(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()},n=function(){return typeof BigInt==="function"},ca=function(a,b){return b===void 0?a.i!==q&&!!(2&(a.h[r]|0)):!!(2&b)&&a.i!==q},t=function(a){a.v=!0;return a},w=function(a){var b=
a;if(da(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(ea(b)&&!Number.isSafeInteger(b))throw Error(String(b));return v?BigInt(a):a=fa(a)?a?"1":"0":da(a)?a.trim()||"0":String(a)},ha=function(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(let c=0;c<a.length;c++){const d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}},ia=function(a){const b=a>>>0;A=b;B=(a-b)/**********>>>0},D=function(a){if(a<0){ia(-a);const [b,c]=C(A,B);A=b>>>0;B=c>>>0}else ia(a)},
F=function(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(***********b+a);else n()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+ja(c)+ja(a));return c},ja=function(a){a=String(a);return"0000000".slice(a.length)+a},ka=function(){var a=A,b=B;if(b&2147483648)if(n())a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0));else{const [c,d]=C(a,b);a="-"+F(c,d)}else a=F(a,
b);return a},C=function(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]},pa=function(a){switch(typeof a){case "bigint":return!0;case "number":return la(a);case "string":return ma.test(a);default:return!1}},qa=function(a){const b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337},ra=function(a){if(qa(a))return a;if(a.length<16)D(Number(a));else if(n())a=BigInt(a),A=Number(a&BigInt(4294967295))>>>0,B=Number(a>>BigInt(32)&BigInt(4294967295));
else{const b=+(a[0]==="-");B=A=0;const c=a.length;for(let d=b,e=(c-b)%6+b;e<=c;d=e,e+=6){const k=Number(a.slice(d,e));B*=1E6;A=A*1E6+k;A>=**********&&(B+=Math.trunc(A/**********),B>>>=0,A>>>=0)}if(b){const [d,e]=C(A,B);A=d;B=e}}return ka()},sa=function(a){a=G(a);if(!H(a)){D(a);var b=A,c=B;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);const d=c***********+(b>>>0);b=Number.isSafeInteger(d)?d:F(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a},ta=function(a){return a},I=function(a,b,c,
d){var e=d!==void 0;d=!!d;const k=[];var h=a.length;let f,u=4294967295,na=!1;const E=!!(b&64),x=E?b&128?0:-1:void 0;if(!(b&1||(f=h&&a[h-1],f!=null&&typeof f==="object"&&f.constructor===Object?(h--,u=h):f=void 0,!E||b&128||e))){na=!0;var p;u=((p=ua)!=null?p:ta)(u-x,x,a,f,void 0)+x}b=void 0;for(e=0;e<h;e++)if(p=a[e],p!=null&&(p=c(p,d))!=null)if(E&&e>=u){const y=e-x;let z;((z=b)!=null?z:b={})[y]=p}else k[e]=p;if(f)for(let y in f){a=f[y];if(a==null||(a=c(a,d))==null)continue;h=+y;let z;if(E&&!Number.isNaN(h)&&
(z=h+x)<u)k[z]=a;else{let oa;((oa=b)!=null?oa:b={})[y]=a}}b&&(na?k.push(b):k[u]=b);return k},wa=function(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return va(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[r]|0;return a.length===0&&b&1?void 0:I(a,b,wa)}if(a!=null&&a[J]===K)return L(a);return}return a},L=function(a){a=a.h;return I(a,a[r]|0,wa)},za=function(a,b,c){if(a==null){var d=32;c?(a=[c],d|=128):a=[];b&&(d=d&-8380417|
(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("a");d=a[r]|0;if(xa&&1&d)throw Error("b");2048&d&&!(2&d)&&ya();if(d&256)throw Error("c");if(d&64)return d&2048||(a[r]=d|2048),a;if(c&&(d|=128,c!==a[0]))throw Error("d");a:{c=a;d|=64;var e=c.length;if(e){var k=e-1;const f=c[k];if(f!=null&&typeof f==="object"&&f.constructor===Object){b=d&128?0:-1;k-=b;if(k>=1024)throw Error("f");for(var h in f)if(e=+h,e<k)c[e+b]=f[h],delete f[h];else break;d=d&-8380417|(k&1023)<<13;break a}}if(b){h=Math.max(b,e-(d&
128?0:-1));if(h>1024)throw Error("g");d=d&-8380417|(h&1023)<<13}}}a[r]=d|2112;return a},ya=function(){if(xa)throw Error("e");if(M!=null){var a;var b=(a=Aa)!=null?a:Aa={};a=b[M]||0;a>=5||(b[M]=a+1,b=Error(),ba(b,"incident"),aa(b))}},Ba=function(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[r]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=N(a,c,!1,b&&!(c&16)):(a[r]|=34,c&4&&Object.freeze(a)));return a}if(a!=null&&a[J]===K){c=a.h;const d=c[r]|0;ca(a,d)||(d&2?b=!0:d&32&&!(d&
4096)?(c[r]=d|2,a.i=q,b=!0):b=!1,b?(a=new a.constructor(c),a.u=q):a=N(c,d));return a}},N=function(a,b,c,d){d!=null||(d=!!(34&b));a=I(a,b,Ba,d);d=32;c&&(d|=2);b=b&8380609|d;a[r]=b;return a},Ca=function(a){return va(a)?Number(a):String(a)};var g=this||self,Da=g._F_toggles_default_IdentityRotateCookiesHttp||[];var Ea=!!(Da[0]&128);var O;if(Da[0]>>15&1)O=Ea;else{var P;a:{for(var Fa=["WIZ_global_data","oxN3nb"],Q=g,R=0;R<Fa.length;R++)if(Q=Q[Fa[R]],Q==null){P=null;break a}P=Q}var Ga=P&&P[748402147];O=Ga!=null?Ga:!1}var xa=O;var Aa=void 0;var M=m(),J=m("m_m",!0);var r=m("jas",!0);var K={},q={},Ha={};var ea=t(a=>typeof a==="number"),da=t(a=>typeof a==="string"),fa=t(a=>typeof a==="boolean");var v=typeof g.BigInt==="function"&&typeof g.BigInt(0)==="bigint";var va=t(a=>v?a>=Ia&&a<=Ja:a[0]==="-"?ha(a,Ka):ha(a,La)),Ka=Number.MIN_SAFE_INTEGER.toString(),Ia=v?BigInt(Number.MIN_SAFE_INTEGER):void 0,La=Number.MAX_SAFE_INTEGER.toString(),Ja=v?BigInt(Number.MAX_SAFE_INTEGER):void 0;var A=0,B=0;var S=typeof BigInt==="function"?BigInt.asIntN:void 0,H=Number.isSafeInteger,la=Number.isFinite,G=Math.trunc,ma=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;var ua;var Ma=w(0),Na=function(a,b,c){if(a.i===q){var d=a.h;d=N(d,d[r]|0);d[r]|=2048;a.h=d;a.i=void 0;a.u=void 0;d=!0}else d=!1;if(!d&&ca(a,a.h[r]|0))throw Error();d=a.h;a:{var e=d[r]|0;const k=b+-1,h=d.length-1;if(h>=0&&k>=h){const f=d[h];if(f!=null&&typeof f==="object"&&f.constructor===Object){f[b]=c;break a}}k<=h?d[k]=c:c!==void 0&&(e=(e!=null?e:d[r]|0)>>13&1023||536870912,b>=e?c!=null&&(d[e+-1]={[b]:c}):d[k]=c)}return a},Oa=function(a){var b=a.h;a=1+(Ha?0:-1);var c=b.length-1;c<1+(Ha?0:-1)?a=void 0:
a>=c?(b=b[c],b!=null&&typeof b==="object"&&b.constructor===Object?a=b[1]:a===c?a=b:a=void 0):a=b[a];a=a!==null?a:void 0;c=typeof a;a!=null&&(c==="bigint"?a=w(S(64,a)):pa(a)?c==="string"?(c=G(Number(a)),H(c)?a=w(c):(c=a.indexOf("."),c!==-1&&(a=a.substring(0,c)),a=n()?w(S(64,BigInt(a))):w(ra(a)))):H(a)?a=w(sa(a)):(a=G(a),H(a)?a=String(a):(c=String(a),qa(c)?a=c:(D(a),a=ka())),a=w(a)):a=void 0);return a!=null?a:Ma},T=function(a,b,c){if(c!=null){if(typeof c!=="number")throw l("int32");if(!la(c))throw l("int32");
c|=0}Na(a,b,c)};var U=class{constructor(a,b,c){this.h=za(a,b,c)}toJSON(){return L(this)}};U.prototype[J]=K;U.prototype.toString=function(){return this.h.toString()};var Pa=class extends U{constructor(a){super(a)}};var Qa=function(a){return b=>{if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("h");b[r]|=32;b=new a(b)}return b}}(class extends U{constructor(a){super(a,0,"identity.hfcr")}});var Ra=function(a){a={method:"POST",credentials:"same-origin",cache:"no-store",mode:"same-origin",headers:{"Content-Type":"application/json"},body:JSON.stringify(L(a))};if(typeof AbortController!=="undefined"){const b=new AbortController;setTimeout(()=>{b.abort()},3E4);a.signal=b.signal}return fetch(new Request("/RotateCookies",a)).then(b=>b.text()).then(b=>Qa(JSON.stringify(JSON.parse(b.substring(5))[0])))},Sa=function(){try{const a=window.localStorage;if(!a)return!1;a.setItem("cookieRotationStorageAccessTest",
"1");a.removeItem("cookieRotationStorageAccessTest");return!0}catch(a){return!1}},V=function(){try{const a=window.localStorage.getItem("nextRotationAttemptTs");if(!a)return null;const b=Math.floor(Number(a));return Number.isNaN(b)?null:b}catch(a){return null}},W=function(a){try{window.localStorage.setItem("nextRotationAttemptTs",a.toString())}catch(b){}},Ua=function(a){const b=V();if(!b||Date.now()>=b){const c=Math.floor(Math.random()*1E3);return new Promise(d=>{setTimeout(()=>{const e=V();!e||Date.now()>=
e?d(Ta(a)):d()},c)})}return Promise.resolve()},Wa=function(a){Va(a).then(()=>{const b=()=>{Va(a).then(()=>{setTimeout(b,a.g*1E3)})};setTimeout(()=>{b()},a.g*1E3)})},Va=function(a){const b=Xa(a);return Ra(b).then(c=>{c=Ya(Ca(Oa(c)));c!==a.g&&(a.g=c)}).catch(()=>{a.g*=2})},Xa=function(a){var b=new Pa;var c=a.o;if(c!=null)a:{if(!pa(c))throw l("int64");switch(typeof c){case "string":var d=G(Number(c));H(d)?c=String(d):(d=c.indexOf("."),d!==-1&&(c=c.substring(0,d)),c=ra(c));break a;case "bigint":c=w(S(64,
c));break a;default:c=sa(c)}}b=Na(b,2,c);a.l!==0&&T(b,1,a.l);a.m!==0&&T(b,3,a.m);a.j!==0&&T(b,4,a.j);return b},Ya=function(a){a<60&&(a=60);return a},Ta=function(a){W(Date.now()+a.g*1E3);const b=Xa(a);return Ra(b).then(c=>{c=Ya(Ca(Oa(c)));c!==a.g&&(W(Date.now()+c*1E3),a.g=c)}).catch(()=>{a.g*=2;W(Date.now()+a.g*1E3)})},Za=class{constructor(a,b,c,d,e){this.o=a;this.l=b;this.m=c;this.j=d;this.g=e}start(){if(typeof fetch!=="undefined")if(Sa()){var a=V(),b=Date.now();a&&a>b+this.g*1E3&&(a=Date.now()+this.g*
1E3,W(a));var c=()=>{Ua(this).then(()=>{setTimeout(c,this.g*1E3)})};setTimeout(()=>{c()},a&&a>b?a-b:0)}else Wa(this)}};for(var $a=function(a,b,c,d,e){(new Za(a,b,c,d,e)).start()},X=["init"],Y=g,Z;X.length&&(Z=X.shift());)X.length||$a===void 0?Y[Z]&&Y[Z]!==Object.prototype[Z]?Y=Y[Z]:Y=Y[Z]={}:Y[Z]=$a;
}catch(e){_._DumpException(e)}
}).call(this,this.default_IdentityRotateCookiesHttp);
// Google Inc.

(function(){'use strict';/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var e=this||self;function f(c,b){c=c.split(".");for(var a=e,d;c.length&&(d=c.shift());)c.length||b===void 0?a[d]&&a[d]!==Object.prototype[d]?a=a[d]:a=a[d]={}:a[d]=b}
;var g={YEAR_FULL:"y",YEAR_FULL_WITH_ERA:"y G",YEAR_MONTH_ABBR:"MMM y",YEAR_MONTH_FULL:"MMMM y",YEAR_MONTH_SHORT:"MM/y",MONTH_DAY_ABBR:"MMM d",MONTH_DAY_FULL:"MMMM dd",MONTH_DAY_SHORT:"M/d",MONTH_DAY_MEDIUM:"MMMM d",MONTH_DAY_YEAR_MEDIUM:"MMM d, y",WEEKDAY_MONTH_DAY_MEDIUM:"EEE, MMM d",WEEKDAY_MONTH_DAY_YEAR_MEDIUM:"EEE, MMM d, y",DAY_ABBR:"d",MONTH_DAY_TIME_ZONE_SHORT:"MMM d, h:mm\u202fa zzzz"},h=g;h=g;var k={ERAS:["BC","AD"],ERANAMES:["Before Christ","Anno Domini"],NARROWMONTHS:"JFMAMJJASOND".split(""),STANDALONENARROWMONTHS:"JFMAMJJASOND".split(""),MONTHS:"January February March April May June July August September October November December".split(" "),STANDALONEMONTHS:"January February March April May June July August September October November December".split(" "),SHORTMONTHS:"Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),STANDALONESHORTMONTHS:"Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),
WEEKDAYS:"Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),STANDALONEWEEKDAYS:"Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),SHORTWEEKDAYS:"Sun Mon Tue Wed Thu Fri Sat".split(" "),STANDALONESHORTWEEKDAYS:"Sun Mon Tue Wed Thu Fri Sat".split(" "),NARROWWEEKDAYS:"SMTWTFS".split(""),STANDALONENARROWWEEKDAYS:"SMTWTFS".split(""),SHORTQUARTERS:["Q1","Q2","Q3","Q4"],QUARTERS:["1st quarter","2nd quarter","3rd quarter","4th quarter"],AMPMS:["AM","PM"],DATEFORMATS:["EEEE, MMMM d, y",
"MMMM d, y","MMM d, y","M/d/yy"],TIMEFORMATS:["h:mm:ss\u202fa zzzz","h:mm:ss\u202fa z","h:mm:ss\u202fa","h:mm\u202fa"],DATETIMEFORMATS:["{1} 'at' {0}","{1} 'at' {0}","{1}, {0}","{1}, {0}"],FIRSTDAYOFWEEK:6,WEEKENDRANGE:[5,6],FIRSTWEEKCUTOFFDAY:5},l=k;l=k;function m(c,b){if(void 0===b){b=c+"";var a=b.indexOf(".");b=Math.min(a===-1?0:b.length-a-1,3)}a=Math.pow(10,b);b={g:b,f:(c*a|0)%a};return(c|0)==1&&b.g==0?"one":"other"}
var n=m;n=m;f("YT_I18N_FORMATTING_GOOG_LOCALE","en");f("YT_I18N_FORMATTING_DATE_TIME_PATTERNS",h);f("YT_I18N_FORMATTING_DATE_TIME_SYMBOLS",l);
f("YT_I18N_FORMATTING_RELATIVE_DATE_TIME_SYMBOLS",{DAY:{LONG:{R:{"-1":"yesterday",0:"today",1:"tomorrow"},P:"one{# day ago}other{# days ago}",F:"one{in # day}other{in # days}"},NARROW:{R:{"-1":"yesterday",0:"today",1:"tomorrow"},P:"one{#d ago}other{#d ago}",F:"one{in #d}other{in #d}"}},HOUR:{LONG:{R:{0:"this hour"},P:"one{# hour ago}other{# hours ago}",F:"one{in # hour}other{in # hours}"},SHORT:{R:{0:"this hour"},P:"one{# hr. ago}other{# hr. ago}",F:"one{in # hr.}other{in # hr.}"},NARROW:{R:{0:"this hour"},
P:"one{#h ago}other{#h ago}",F:"one{in #h}other{in #h}"}},MINUTE:{LONG:{R:{0:"this minute"},P:"one{# minute ago}other{# minutes ago}",F:"one{in # minute}other{in # minutes}"},SHORT:{R:{0:"this minute"},P:"one{# min. ago}other{# min. ago}",F:"one{in # min.}other{in # min.}"},NARROW:{R:{0:"this minute"},P:"one{#m ago}other{#m ago}",F:"one{in #m}other{in #m}"}},MONTH:{LONG:{R:{"-1":"last month",0:"this month",1:"next month"},P:"one{# month ago}other{# months ago}",F:"one{in # month}other{in # months}"},
SHORT:{R:{"-1":"last mo.",0:"this mo.",1:"next mo."},P:"one{# mo. ago}other{# mo. ago}",F:"one{in # mo.}other{in # mo.}"},NARROW:{R:{"-1":"last mo.",0:"this mo.",1:"next mo."},P:"one{#mo ago}other{#mo ago}",F:"one{in #mo}other{in #mo}"}},QUARTER:{LONG:{R:{"-1":"last quarter",0:"this quarter",1:"next quarter"},P:"one{# quarter ago}other{# quarters ago}",F:"one{in # quarter}other{in # quarters}"},SHORT:{R:{"-1":"last qtr.",0:"this qtr.",1:"next qtr."},P:"one{# qtr. ago}other{# qtrs. ago}",F:"one{in # qtr.}other{in # qtrs.}"},
NARROW:{R:{"-1":"last qtr.",0:"this qtr.",1:"next qtr."},P:"one{#q ago}other{#q ago}",F:"one{in #q}other{in #q}"}},SECOND:{LONG:{R:{0:"now"},P:"one{# second ago}other{# seconds ago}",F:"one{in # second}other{in # seconds}"},SHORT:{R:{0:"now"},P:"one{# sec. ago}other{# sec. ago}",F:"one{in # sec.}other{in # sec.}"},NARROW:{R:{0:"now"},P:"one{#s ago}other{#s ago}",F:"one{in #s}other{in #s}"}},WEEK:{LONG:{R:{"-1":"last week",0:"this week",1:"next week"},P:"one{# week ago}other{# weeks ago}",F:"one{in # week}other{in # weeks}"},
SHORT:{R:{"-1":"last wk.",0:"this wk.",1:"next wk."},P:"one{# wk. ago}other{# wk. ago}",F:"one{in # wk.}other{in # wk.}"},NARROW:{R:{"-1":"last wk.",0:"this wk.",1:"next wk."},P:"one{#w ago}other{#w ago}",F:"one{in #w}other{in #w}"}},YEAR:{LONG:{R:{"-1":"last year",0:"this year",1:"next year"},P:"one{# year ago}other{# years ago}",F:"one{in # year}other{in # years}"},SHORT:{R:{"-1":"last yr.",0:"this yr.",1:"next yr."},P:"one{# yr. ago}other{# yr. ago}",F:"one{in # yr.}other{in # yr.}"},NARROW:{R:{"-1":"last yr.",
0:"this yr.",1:"next yr."},P:"one{#y ago}other{#y ago}",F:"one{in #y}other{in #y}"}}});f("YT_I18N_FORMATTING_PLURAL_RULES_SELECT",n);
f("YT_I18N_FORMATTING_DURATION_TIME_SYMBOLS",{DAY:{LONG:"one{# day}other{# days}",SHORT:"one{# day}other{# days}",NARROW:"one{#d}other{#d}"},HOUR:{LONG:"one{# hour}other{# hours}",SHORT:"one{# hr}other{# hr}",NARROW:"one{#h}other{#h}"},MINUTE:{LONG:"one{# minute}other{# minutes}",SHORT:"one{# min}other{# min}",NARROW:"one{#m}other{#m}"},MONTH:{LONG:"one{# month}other{# months}",SHORT:"one{# mth}other{# mths}",NARROW:"one{#m}other{#m}"},SECOND:{LONG:"one{# second}other{# seconds}",SHORT:"one{# sec}other{# sec}",
NARROW:"one{#s}other{#s}"},WEEK:{LONG:"one{# week}other{# weeks}",SHORT:"one{# wk}other{# wks}",NARROW:"one{#w}other{#w}"},YEAR:{LONG:"one{# year}other{# years}",SHORT:"one{# yr}other{# yrs}",NARROW:"one{#y}other{#y}"}});}).call(this);

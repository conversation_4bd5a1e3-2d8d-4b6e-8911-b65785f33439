// ==UserScript==
// @name         YT CHAT1
// @namespace    https://greasyfork.org/en/users/160457-stan60250
// @version      1.4.0
// @description  CHATTER - Optimized with proper timing intervals
// <AUTHOR>
// @match        *://www.youtube.com/live*
// @match        *://www.youtube.com/live_chat?is_popout=1&v=*
// @icon         https://www.google.com/s2/favicons?domain=youtube.com
// @grant        none
// @license      MIT
// @run-at       document-start
// ==/UserScript==

// Configuration - 30 minutes to 1 hour intervals
var INTERVAL_SECONDS_MIN = 1800; // 30 minutes in seconds
var INTERVAL_SECONDS_MAX = 3600; // 1 hour in seconds
var STARTUP_DELAY_SECONDS = 5; // Initial delay before first execution

var UNICODE_SUN = '\u2600\ufe0f';
var UNICODE_MOON = '\ud83c\udf19';

(function() {
    'use strict';

    // Global variables for timing control
    var nextExecutionTimeout = null;
    var isScriptActive = true;
    var lastExecutionTime = 0;

    // Enhanced timer function with better error handling
    function executeTask() {
        try {
            var url = window.location.href;
            var currentTime = Date.now();

            console.log(`[YT CHAT] Executing task at ${new Date().toLocaleTimeString()}`);

            if (url) {
                if (url.indexOf('/live_chat?') > -1) {
                    sendChat(document);
                } else if (url.indexOf('/watch?') > -1) {
                    var iframe = document.getElementById('chatframe');
                    var chatFrame = iframe ? (iframe.contentDocument || iframe.contentWindow.document) : null;
                    sendChat(chatFrame);
                }
            }

            lastExecutionTime = currentTime;
            scheduleNextExecution();

        } catch (error) {
            console.error('[YT CHAT] Error in executeTask:', error);
            scheduleNextExecution(); // Continue even if there's an error
        }
    }

    // Improved random interval generator
    function getRandomInt(min, max) {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // Schedule next execution with proper timing
    function scheduleNextExecution() {
        // Clear any existing timeout
        if (nextExecutionTimeout) {
            clearTimeout(nextExecutionTimeout);
        }

        if (!isScriptActive) {
            console.log('[YT CHAT] Script is inactive, not scheduling next execution');
            return;
        }

        var delay = getRandomInt(INTERVAL_SECONDS_MIN, INTERVAL_SECONDS_MAX) * 1000;
        var nextExecutionTime = new Date(Date.now() + delay);

        console.log(`[YT CHAT] Next execution scheduled in ${delay / 1000 / 60} minutes (at ${nextExecutionTime.toLocaleTimeString()})`);

        nextExecutionTimeout = setTimeout(executeTask, delay);
    }

    // Enhanced visibility change handler for background operation
    function handleVisibilityChange() {
        if (document.hidden) {
            console.log('[YT CHAT] Page hidden - continuing background operation');
        } else {
            console.log('[YT CHAT] Page visible - resuming normal operation');
        }
        // Script continues running regardless of visibility
    }

    // Enhanced chat sending function with better error handling
    function sendChat(chatFrame) {
        try {
            if (!chatFrame) {
                console.error('[YT CHAT] Unable to find chat iframe');
                return false;
            }

            var chat_input = chatFrame.querySelector('div#input');
            var chat_submit = chatFrame.querySelector('.yt-spec-button-shape-next.yt-spec-button-shape-next--text.yt-spec-button-shape-next--mono.yt-spec-button-shape-next--size-m.yt-spec-button-shape-next--icon-button');

            if (!chat_input || !chat_submit) {
                console.error('[YT CHAT] Unable to find chat components');
                return false;
            }

            var storedComments = JSON.parse(localStorage.getItem('comments')) || [];
            if (storedComments.length === 0) {
                console.error('[YT CHAT] No comments found in localStorage.');
                return false;
            }

            var msg = storedComments[Math.floor(Math.random() * storedComments.length)];
            console.log(`[YT CHAT] Sending message: ${msg}`);

            // Focus and set message
            chat_input.focus();
            chat_input.textContent = msg;

            // Trigger input event
            chat_input.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));

            // Small delay before clicking submit to ensure the input is processed
            setTimeout(() => {
                chat_submit.click();
                console.log('[YT CHAT] Message sent successfully');
            }, 100);

            return true;

        } catch (error) {
            console.error('[YT CHAT] Error in sendChat:', error);
            return false;
        }
    }

    // Initialize script with proper timing
    function initializeScript() {
        console.log(`[YT CHAT] Script initialized - intervals set to ${INTERVAL_SECONDS_MIN/60}-${INTERVAL_SECONDS_MAX/60} minutes`);

        // Add visibility change listener for background operation
        document.addEventListener('visibilitychange', handleVisibilityChange);

        // Add beforeunload listener to cleanup
        window.addEventListener('beforeunload', function() {
            isScriptActive = false;
            if (nextExecutionTimeout) {
                clearTimeout(nextExecutionTimeout);
            }
        });

        // Start the execution with a startup delay
        setTimeout(() => {
            console.log('[YT CHAT] Starting first execution after startup delay');
            executeTask();
        }, STARTUP_DELAY_SECONDS * 1000);
    }

    // Wait for DOM to be ready before initializing
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeScript);
    } else {
        initializeScript();
    }

})();

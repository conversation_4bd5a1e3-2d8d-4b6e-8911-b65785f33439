// ==UserScript==
// @name         YT CHAT1
// @namespace    https://greasyfork.org/en/users/160457-stan60250
// @version      1.3.5
// @description  CHATTER
// <AUTHOR>
// @match        *://www.youtube.com/live*
// @match        *://www.youtube.com/live_chat?is_popout=1&v=*
// @icon         https://www.google.com/s2/favicons?domain=youtube.com
// @grant        none
// @license      MIT
// ==/UserScript==

var INTERVAL_SECONDS_MIN = 30; // Customize minimum interval in seconds
var INTERVAL_SECONDS_MAX = 80; // Customize maximum interval in seconds
var STARTUP_DELAY_SECONDS = 3;

var UNICODE_SUN = '\u2600\ufe0f';
var UNICODE_MOON = '\ud83c\udf19';

(function() {
    'use strict';

    // Timer function
    function executeTask() {
        var url = window.location.href;
        if (url) {
            if (url.indexOf('/live_chat?') > -1) {
                sendChat(document);
            } else if (url.indexOf('/watch?') > -1) {
                var iframe = document.getElementById('chatframe');
                var chatFrame = iframe ? (iframe.contentDocument || iframe.contentWindow.document) : null;
                sendChat(chatFrame);
            }
        }
        console.log('Executed task at interval.');
    }

    function getRandomInt(min, max) {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    function sendChat(chatFrame) {
        if (chatFrame) {
            var chat_input = chatFrame.querySelector('div#input');
            var chat_submit = chatFrame.querySelector('.yt-spec-button-shape-next.yt-spec-button-shape-next--text.yt-spec-button-shape-next--mono.yt-spec-button-shape-next--size-m.yt-spec-button-shape-next--icon-button');
            if (chat_input && chat_submit) {
                var storedComments = JSON.parse(localStorage.getItem('comments')) || [];
                if (storedComments.length > 0) {
                    var msg = storedComments[Math.floor(Math.random() * storedComments.length)];
                    console.log('msg:' + msg);
                    chat_input.focus();
                    chat_input.textContent = msg;
                    chat_input.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
                    chat_submit.click();
                } else {
                    console.error('No comments found in localStorage.');
                }
            } else {
                console.error('Unable to find chat component');
            }
        } else {
            console.error('Unable to find chat iframe');
        }
    }

    // Start the execution with a startup delay
    setTimeout(() => {
        executeTask();
        setInterval(() => {
            let delay = getRandomInt(INTERVAL_SECONDS_MIN, INTERVAL_SECONDS_MAX) * 1000;
            console.log('Next task in ' + delay / 1000 + ' seconds.');
            setTimeout(executeTask, delay);
        }, getRandomInt(INTERVAL_SECONDS_MIN, INTERVAL_SECONDS_MAX) * 1000);
    }, STARTUP_DELAY_SECONDS * 1000);

})();

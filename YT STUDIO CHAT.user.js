// ==UserScript==
// @name         YT STUDIO CHAT
// @namespace    https://greasyfork.org/en/users/160457-stan60250
// @version      1.0.0
// @description  CHATTER for YouTube Studio Live Streaming pages
// <AUTHOR>
// @match        *://studio.youtube.com/video/*/livestreaming*
// @match        *://studio.youtube.com/channel/*/livestreaming*
// @icon         https://www.google.com/s2/favicons?domain=studio.youtube.com
// @grant        none
// @license      MIT
// @run-at       document-start
// ==/UserScript==

// Configuration - 30 minutes to 1 hour intervals
var INTERVAL_SECONDS_MIN = 10; // 30 minutes in seconds (set to 10 for testing)
var INTERVAL_SECONDS_MAX = 20; // 1 hour in seconds (set to 20 for testing)
var STARTUP_DELAY_SECONDS = 5;

(function() {
    'use strict';

    // Global variables for countdown
    var nextExecutionTime = 0;
    var countdownInterval = null;
    var originalTitle = document.title;
    var currentTimeout = null;

    // Timer function for Studio pages
    function executeTask() {
        var url = window.location.href;
        if (url && url.includes('studio.youtube.com') && url.includes('livestreaming')) {
            sendStudioChat();
        }
        console.log('[YT STUDIO] ✅ Executed task at ' + new Date().toLocaleTimeString());
        
        // Schedule next execution
        scheduleNextExecution();
    }

    function getRandomInt(min, max) {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // Studio-specific chat sending function
    function sendStudioChat() {
        console.log('[YT STUDIO] 🔍 Looking for Studio chat elements...');

        // Wait a bit more for Studio interface to load
        if (document.readyState !== 'complete') {
            console.log('[YT STUDIO] ⏳ Document not ready, waiting...');
            setTimeout(sendStudioChat, 2000);
            return;
        }
        
        // Try multiple selectors for Studio chat input
        var chatInputSelectors = [
            'div[contenteditable="true"]',
            'textarea[placeholder*="chat"]',
            'textarea[placeholder*="message"]',
            'input[placeholder*="chat"]',
            'input[placeholder*="message"]',
            '[data-testid*="chat"] input',
            '[data-testid*="chat"] textarea',
            '[data-testid*="message"] input',
            '[data-testid*="message"] textarea',
            '.chat-input',
            '.message-input',
            '#chat-input',
            '#message-input'
        ];

        var chatInput = null;
        for (var i = 0; i < chatInputSelectors.length; i++) {
            chatInput = document.querySelector(chatInputSelectors[i]);
            if (chatInput) {
                console.log('[YT STUDIO] 📝 Found chat input with selector: ' + chatInputSelectors[i]);
                break;
            }
        }

        if (chatInput) {
            var storedComments = JSON.parse(localStorage.getItem('comments')) || [];
            if (storedComments.length > 0) {
                var msg = storedComments[Math.floor(Math.random() * storedComments.length)];
                console.log('[YT STUDIO] 💬 Preparing to send: ' + msg);
                
                // Focus and set the message
                chatInput.focus();
                
                // Handle different input types
                if (chatInput.contentEditable === 'true') {
                    chatInput.textContent = msg;
                    chatInput.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
                } else {
                    chatInput.value = msg;
                    chatInput.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
                    chatInput.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));
                }
                
                console.log('[YT STUDIO] 📝 Message entered, waiting for submit button...');
                
                // Look for submit button
                var attempts = 0;
                var maxAttempts = 10;
                
                function trySubmit() {
                    attempts++;
                    
                    // Try multiple selectors for submit button
                    var submitSelectors = [
                        'button[type="submit"]',
                        'button[aria-label*="send"]',
                        'button[aria-label*="Send"]',
                        'button[title*="send"]',
                        'button[title*="Send"]',
                        '[data-testid*="send"] button',
                        '[data-testid*="submit"] button',
                        '.send-button',
                        '.submit-button',
                        'button:contains("Send")',
                        'button:contains("send")'
                    ];
                    
                    var submitButton = null;
                    for (var j = 0; j < submitSelectors.length; j++) {
                        submitButton = document.querySelector(submitSelectors[j]);
                        if (submitButton && !submitButton.disabled && submitButton.offsetParent !== null) {
                            console.log('[YT STUDIO] 🚀 Found submit button with selector: ' + submitSelectors[j]);
                            break;
                        }
                    }
                    
                    // Also try looking for buttons near the input
                    if (!submitButton && chatInput.parentElement) {
                        var nearbyButtons = chatInput.parentElement.querySelectorAll('button');
                        for (var k = 0; k < nearbyButtons.length; k++) {
                            if (!nearbyButtons[k].disabled && nearbyButtons[k].offsetParent !== null) {
                                submitButton = nearbyButtons[k];
                                console.log('[YT STUDIO] 🚀 Found nearby submit button');
                                break;
                            }
                        }
                    }
                    
                    if (submitButton && !submitButton.disabled && submitButton.offsetParent !== null) {
                        console.log('[YT STUDIO] 🚀 Submit button ready, clicking...');
                        submitButton.click();
                        console.log('[YT STUDIO] ✅ Message sent successfully!');
                    } else if (attempts < maxAttempts) {
                        console.log(`[YT STUDIO] ⏳ Submit button not ready, attempt ${attempts}/${maxAttempts}`);
                        setTimeout(trySubmit, 200);
                    } else {
                        console.error('[YT STUDIO] ❌ Submit button never became available');
                        
                        // Try Enter key as fallback
                        console.log('[YT STUDIO] 🔄 Trying Enter key as fallback...');
                        chatInput.focus();
                        var enterEvent = new KeyboardEvent('keydown', {
                            key: 'Enter',
                            code: 'Enter',
                            keyCode: 13,
                            which: 13,
                            bubbles: true,
                            cancelable: true
                        });
                        chatInput.dispatchEvent(enterEvent);
                    }
                }
                
                // Start trying to submit after a short delay
                setTimeout(trySubmit, 300);
                
            } else {
                console.error('[YT STUDIO] ❌ No comments found in localStorage.');
            }
        } else {
            console.error('[YT STUDIO] ❌ Unable to find chat input in Studio interface');
            console.log('[YT STUDIO] 🔍 Debugging - looking for chat elements...');

            // Check for iframes (Studio chat might be in an iframe)
            var iframes = document.querySelectorAll('iframe');
            console.log('[YT STUDIO] 📺 Found', iframes.length, 'iframes');
            for (var n = 0; n < iframes.length; n++) {
                console.log('[YT STUDIO] - Iframe', n, ':', iframes[n].src || iframes[n].id || 'no src/id');

                // Try to access iframe content if same-origin
                try {
                    var iframeDoc = iframes[n].contentDocument || iframes[n].contentWindow.document;
                    if (iframeDoc) {
                        var iframeInputs = iframeDoc.querySelectorAll('input, textarea, [contenteditable="true"]');
                        console.log('[YT STUDIO] - Iframe', n, 'has', iframeInputs.length, 'input elements');

                        // Try to find chat input in iframe
                        for (var o = 0; o < iframeInputs.length; o++) {
                            var elem = iframeInputs[o];
                            var placeholder = elem.placeholder || elem.getAttribute('aria-label') || '';
                            if (placeholder.toLowerCase().includes('chat') ||
                                placeholder.toLowerCase().includes('message') ||
                                elem.id.toLowerCase().includes('chat') ||
                                elem.className.toLowerCase().includes('chat')) {
                                console.log('[YT STUDIO] 🎯 Found potential chat input in iframe:', elem.tagName, elem.className, placeholder);

                                // Try to use this input
                                var storedComments = JSON.parse(localStorage.getItem('comments')) || [];
                                if (storedComments.length > 0) {
                                    var msg = storedComments[Math.floor(Math.random() * storedComments.length)];
                                    console.log('[YT STUDIO] 💬 Trying to send via iframe input: ' + msg);

                                    elem.focus();
                                    if (elem.contentEditable === 'true') {
                                        elem.textContent = msg;
                                        elem.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
                                    } else {
                                        elem.value = msg;
                                        elem.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
                                    }

                                    // Look for submit button in iframe
                                    setTimeout(() => {
                                        var iframeButtons = iframeDoc.querySelectorAll('button');
                                        for (var p = 0; p < iframeButtons.length; p++) {
                                            if (!iframeButtons[p].disabled && iframeButtons[p].offsetParent !== null) {
                                                console.log('[YT STUDIO] 🚀 Clicking iframe button');
                                                iframeButtons[p].click();
                                                return;
                                            }
                                        }

                                        // Try Enter key in iframe
                                        console.log('[YT STUDIO] 🔄 Trying Enter key in iframe');
                                        var enterEvent = new KeyboardEvent('keydown', {
                                            key: 'Enter',
                                            code: 'Enter',
                                            keyCode: 13,
                                            which: 13,
                                            bubbles: true,
                                            cancelable: true
                                        });
                                        elem.dispatchEvent(enterEvent);
                                    }, 500);

                                    return; // Exit if we found and tried an input
                                }
                            }
                        }
                    }
                } catch (e) {
                    console.log('[YT STUDIO] - Iframe', n, 'access blocked (cross-origin)');
                }
            }

            // Show available input elements in main document
            console.log('[YT STUDIO] 🔍 Available input elements in main document:');
            var allInputs = document.querySelectorAll('input, textarea, [contenteditable="true"]');
            for (var m = 0; m < Math.min(allInputs.length, 10); m++) {
                var elem = allInputs[m];
                console.log('[YT STUDIO] - Element:', elem.tagName,
                           'class:', elem.className,
                           'placeholder:', elem.placeholder || 'none',
                           'aria-label:', elem.getAttribute('aria-label') || 'none',
                           'id:', elem.id || 'none');
            }

            // Look for any element with "chat" in its attributes
            console.log('[YT STUDIO] 🔍 Looking for elements with "chat" in attributes...');
            var allElements = document.querySelectorAll('*');
            var chatElements = [];
            for (var q = 0; q < Math.min(allElements.length, 1000); q++) { // Limit to first 1000 elements
                var el = allElements[q];
                try {
                    var hasChat = false;

                    // Check ID
                    if (el.id && typeof el.id === 'string' && el.id.toLowerCase().includes('chat')) {
                        hasChat = true;
                    }

                    // Check className (handle both string and DOMTokenList)
                    if (el.className) {
                        var classStr = '';
                        if (typeof el.className === 'string') {
                            classStr = el.className;
                        } else if (el.className.toString) {
                            classStr = el.className.toString();
                        }
                        if (classStr.toLowerCase().includes('chat')) {
                            hasChat = true;
                        }
                    }

                    // Check data-testid
                    var testId = el.getAttribute('data-testid');
                    if (testId && testId.toLowerCase().includes('chat')) {
                        hasChat = true;
                    }

                    if (hasChat) {
                        chatElements.push(el);
                    }
                } catch (e) {
                    // Skip elements that cause errors
                    continue;
                }
            }
            console.log('[YT STUDIO] 💬 Found', chatElements.length, 'elements with "chat" in attributes');
            for (var r = 0; r < Math.min(chatElements.length, 5); r++) {
                var elem = chatElements[r];
                var classStr = '';
                if (elem.className) {
                    if (typeof elem.className === 'string') {
                        classStr = elem.className;
                    } else if (elem.className.toString) {
                        classStr = elem.className.toString();
                    }
                }
                console.log('[YT STUDIO] - Chat element:', elem.tagName, elem.id || 'no-id', classStr);
            }
        }
    }

    // Fixed timing system
    function scheduleNextExecution() {
        if (currentTimeout) {
            clearTimeout(currentTimeout);
        }
        
        var delay = getRandomInt(INTERVAL_SECONDS_MIN, INTERVAL_SECONDS_MAX) * 1000;
        nextExecutionTime = Date.now() + delay;
        
        console.log('[YT STUDIO] ⏰ Next execution in ' + Math.round(delay / 1000 / 60) + ' minutes at ' + new Date(nextExecutionTime).toLocaleTimeString());
        
        startCountdown();
        currentTimeout = setTimeout(executeTask, delay);
    }

    // Countdown in tab title
    function updateTabTitle() {
        if (!nextExecutionTime) return;
        
        var now = Date.now();
        var timeLeft = nextExecutionTime - now;
        
        if (timeLeft <= 0) {
            document.title = originalTitle;
            return;
        }
        
        var minutes = Math.floor(timeLeft / 60000);
        var seconds = Math.floor((timeLeft % 60000) / 1000);
        
        document.title = `⏰ ${minutes}m ${seconds}s - ${originalTitle}`;
    }

    function startCountdown() {
        if (countdownInterval) {
            clearInterval(countdownInterval);
        }
        
        updateTabTitle();
        countdownInterval = setInterval(updateTabTitle, 1000);
    }

    function stopCountdown() {
        if (countdownInterval) {
            clearInterval(countdownInterval);
            countdownInterval = null;
        }
        document.title = originalTitle;
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.shiftKey && e.key === 'E') {
            e.preventDefault();
            console.log('[YT STUDIO] 🔧 Manual execution triggered');
            stopCountdown();
            if (currentTimeout) {
                clearTimeout(currentTimeout);
            }
            executeTask();
        }
        else if (e.ctrlKey && e.shiftKey && e.key === 'S') {
            e.preventDefault();
            var timeLeft = nextExecutionTime ? Math.round((nextExecutionTime - Date.now()) / 60000) : 0;
            var nextTime = nextExecutionTime ? new Date(nextExecutionTime).toLocaleTimeString() : 'Not scheduled';
            alert(`YT Studio Chat Status:\n\nNext execution: ${nextTime}\nTime remaining: ${timeLeft} minutes\n\nShortcuts:\nCtrl+Shift+E - Execute now\nCtrl+Shift+S - Show status\nCtrl+Shift+D - Debug mode`);
        }
        // Ctrl+Shift+D for debug mode
        else if (e.ctrlKey && e.shiftKey && e.key === 'D') {
            e.preventDefault();
            console.log('[YT STUDIO] 🔧 Debug mode triggered');

            // Show current URL
            console.log('[YT STUDIO] 📍 Current URL:', window.location.href);

            // Show document ready state
            console.log('[YT STUDIO] 📄 Document ready state:', document.readyState);

            // Force run sendStudioChat with extra debugging
            console.log('[YT STUDIO] 🔍 Running debug chat detection...');
            sendStudioChat();
        }
    });

    // Initialize
    console.log(`[YT STUDIO] 🚀 Studio script starting - intervals: ${INTERVAL_SECONDS_MIN/60}-${INTERVAL_SECONDS_MAX/60} minutes`);
    console.log('[YT STUDIO] 🎮 Shortcuts: Ctrl+Shift+E (execute now), Ctrl+Shift+S (status)');
    
    setTimeout(() => {
        console.log('[YT STUDIO] 🎯 First execution starting...');
        executeTask();
    }, STARTUP_DELAY_SECONDS * 1000);

    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        stopCountdown();
        if (currentTimeout) {
            clearTimeout(currentTimeout);
        }
    });

})();

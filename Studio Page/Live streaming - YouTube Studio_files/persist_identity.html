<!DOCTYPE html>
<!-- saved from url=(0040)https://www.youtube.com/persist_identity -->
<html data-kantu="1"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>YouTube Frame</title><script nonce="">if ('undefined' == typeof Symbol || 'undefined' == typeof Symbol.iterator) {delete Array.prototype.entries;}</script></head><body><script nonce="">'use strict';/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var COMPILED=false;var goog=goog||{};goog.global=this||self;goog.global.CLOSURE_UNCOMPILED_DEFINES;goog.global.CLOSURE_DEFINES;
goog.exportPath_=function(name,object,overwriteImplicit,objectToExportTo){var parts=name.split(".");var cur=objectToExportTo||goog.global;for(var part;parts.length&&(part=parts.shift());)if(!parts.length&&object!==undefined)if(!overwriteImplicit&&goog.isObject(object)&&goog.isObject(cur[part]))for(var prop in object){if(object.hasOwnProperty(prop))cur[part][prop]=object[prop]}else cur[part]=object;else if(cur[part]&&cur[part]!==Object.prototype[part])cur=cur[part];else cur=cur[part]={}};
goog.CLOSURE_DEFINES=typeof CLOSURE_DEFINES!=="undefined"?CLOSURE_DEFINES:goog.global.CLOSURE_DEFINES;goog.CLOSURE_UNCOMPILED_DEFINES=typeof CLOSURE_UNCOMPILED_DEFINES!=="undefined"?CLOSURE_UNCOMPILED_DEFINES:goog.global.CLOSURE_UNCOMPILED_DEFINES;
goog.define=function(name,defaultValue){var value=defaultValue;if(!COMPILED){var uncompiledDefines=goog.CLOSURE_UNCOMPILED_DEFINES?goog.CLOSURE_UNCOMPILED_DEFINES:goog.global.CLOSURE_UNCOMPILED_DEFINES;var defines=goog.CLOSURE_DEFINES?goog.CLOSURE_DEFINES:goog.global.CLOSURE_DEFINES;if(uncompiledDefines&&uncompiledDefines.nodeType===undefined&&Object.prototype.hasOwnProperty.call(uncompiledDefines,name))value=uncompiledDefines[name];else if(defines&&defines.nodeType===undefined&&Object.prototype.hasOwnProperty.call(defines,
name))value=defines[name]}return value};goog.FEATURESET_YEAR=goog.define("goog.FEATURESET_YEAR",2012);goog.DEBUG=goog.define("goog.DEBUG",true);goog.LOCALE=goog.define("goog.LOCALE","en");goog.TRUSTED_SITE=goog.define("goog.TRUSTED_SITE",true);goog.DISALLOW_TEST_ONLY_CODE=goog.define("goog.DISALLOW_TEST_ONLY_CODE",COMPILED&&!goog.DEBUG);goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING=goog.define("goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING",false);
goog.readFlagInternalDoNotUseOrElse=function(googFlagId,defaultValue){var obj=goog.getObjectByName(goog.FLAGS_OBJECT_);var val=obj&&obj[googFlagId];return val!=null?val:defaultValue};goog.FLAGS_OBJECT_=goog.define("goog.FLAGS_OBJECT","CLOSURE_FLAGS");goog.FLAGS_STAGING_DEFAULT=goog.define("goog.FLAGS_STAGING_DEFAULT",true);goog.CLOSURE_TOGGLE_ORDINALS=typeof CLOSURE_TOGGLE_ORDINALS==="object"?CLOSURE_TOGGLE_ORDINALS:goog.global.CLOSURE_TOGGLE_ORDINALS;
goog.readToggleInternalDoNotCallDirectly=function(name){var ordinals=goog.CLOSURE_TOGGLE_ORDINALS;var ordinal=ordinals&&ordinals[name];if(typeof ordinal!=="number")return Boolean(ordinal);return Boolean(goog.TOGGLES_[Math.floor(ordinal/30)]&1<<ordinal%30)};goog.global.CLOSURE_TOGGLE_ORDINALS;goog.TOGGLE_VAR_=goog.define("goog.TOGGLE_VAR","_F_toggles");goog.TOGGLES_=goog.global[goog.TOGGLE_VAR_]||[];goog.GENDERED_MESSAGES_ENABLED=goog.define("goog.GENDERED_MESSAGES_ENABLED",true);
goog.GrammaticalGender_={OTHER:0,MASCULINE:1,FEMININE:2,NEUTER:3};goog.GRAMMATICAL_GENDER_MAP_={"FEMININE":goog.GrammaticalGender_.FEMININE,"MASCULINE":goog.GrammaticalGender_.MASCULINE,"NEUTER":goog.GrammaticalGender_.NEUTER};goog.viewerGrammaticalGender_=goog.GRAMMATICAL_GENDER_MAP_[goog.GENDERED_MESSAGES_ENABLED&&goog.global["_F_VIEWER_GRAMMATICAL_GENDER"]]||goog.GrammaticalGender_.OTHER;
goog.msgKind={MASCULINE:goog.viewerGrammaticalGender_===goog.GrammaticalGender_.MASCULINE,FEMININE:goog.viewerGrammaticalGender_===goog.GrammaticalGender_.FEMININE,NEUTER:goog.viewerGrammaticalGender_===goog.GrammaticalGender_.NEUTER};goog.global.CLOSURE_UNCOMPILED_LEGACY_NAMESPACE_OBJECT;goog.LEGACY_NAMESPACE_OBJECT_=!COMPILED&&typeof CLOSURE_UNCOMPILED_LEGACY_NAMESPACE_OBJECT!=="undefined"?CLOSURE_UNCOMPILED_LEGACY_NAMESPACE_OBJECT:goog.global;
goog.provide=function(name){if(goog.isInModuleLoader_())throw new Error("goog.provide cannot be used within a module.");if(!COMPILED)if(goog.isProvided_(name))throw new Error('Namespace "'+name+'" already declared.');goog.constructNamespace_(name)};
goog.constructNamespace_=function(name,object,overwriteImplicit){if(!COMPILED){delete goog.implicitNamespaces_[name];var namespace=name;while(namespace=namespace.substring(0,namespace.lastIndexOf("."))){if(goog.getObjectByName(namespace,goog.LEGACY_NAMESPACE_OBJECT_))break;goog.implicitNamespaces_[namespace]=true}}goog.exportPath_(name,object,overwriteImplicit,goog.LEGACY_NAMESPACE_OBJECT_)};goog.NONCE_PATTERN_=/^[\w+/_-]+[=]{0,2}$/;
goog.getScriptNonce_=function(opt_window){var doc=(opt_window||goog.global).document;var script=doc.querySelector&&doc.querySelector("script[nonce]");if(script){var nonce=script["nonce"]||script.getAttribute("nonce");if(nonce&&goog.NONCE_PATTERN_.test(nonce))return nonce}return""};goog.VALID_MODULE_RE_=/^[a-zA-Z_$][a-zA-Z0-9._$]*$/;
goog.module=function(name){if(COMPILED)return;if(typeof name!=="string"||!name||name.search(goog.VALID_MODULE_RE_)==-1)throw new Error("Invalid module identifier");if(!goog.isInGoogModuleLoader_())throw new Error("Module "+name+" has been loaded incorrectly. Note, "+"modules cannot be loaded as normal scripts. They require some kind of "+"pre-processing step. You're likely trying to load a module via a "+"script tag or as a part of a concatenated bundle without rewriting the "+"module. For more info see: "+
"https://github.com/google/closure-library/wiki/goog.module:-an-ES6-module-like-alternative-to-goog.provide.");if(goog.moduleLoaderState_.moduleName)throw new Error("goog.module may only be called once per module.");goog.moduleLoaderState_.moduleName=name;if(goog.isProvided_(name))throw new Error('Namespace "'+name+'" already declared.');delete goog.implicitNamespaces_[name]};goog.module.get=function(name){return COMPILED?null:goog.module.getInternal_(name)};
goog.module.getInternal_=function(name){if(!COMPILED)if(name in goog.loadedModules_)return goog.loadedModules_[name].exports;else if(!goog.implicitNamespaces_[name]){var ns=goog.getObjectByName(name,goog.LEGACY_NAMESPACE_OBJECT_);return ns!=null?ns:null}return null};
goog.requireDynamic=function(name){if(!COMPILED){if(!goog.importHandler_||!goog.uncompiledChunkIdHandler_)throw new Error("Need to setup import handler and chunk id handler.");return goog.importHandler_(goog.uncompiledChunkIdHandler_(name)).then(function(){var module=goog.module.getInternal_(name);if(module==null)throw new Error("Module "+name+" is not loaded.");return module})}return null};goog.importHandler_=null;goog.uncompiledChunkIdHandler_=null;
goog.setImportHandlerInternalDoNotCallOrElse=function(fn){goog.importHandler_=fn};goog.setUncompiledChunkIdHandlerInternalDoNotCallOrElse=function(fn){goog.uncompiledChunkIdHandler_=fn};goog.maybeRequireFrameworkInternalOnlyDoNotCallOrElse=function(namespace){};goog.ModuleType={ES6:"es6",GOOG:"goog"};goog.moduleLoaderState_=null;goog.isInModuleLoader_=function(){return goog.isInGoogModuleLoader_()||goog.isInEs6ModuleLoader_()};
goog.isInGoogModuleLoader_=function(){return!!goog.moduleLoaderState_&&goog.moduleLoaderState_.type==goog.ModuleType.GOOG};goog.isInEs6ModuleLoader_=function(){var inLoader=!!goog.moduleLoaderState_&&goog.moduleLoaderState_.type==goog.ModuleType.ES6;if(inLoader)return true;var jscomp=goog.LEGACY_NAMESPACE_OBJECT_["$jscomp"];if(jscomp){if(typeof jscomp.getCurrentModulePath!="function")return false;return!!jscomp.getCurrentModulePath()}return false};
goog.module.declareLegacyNamespace=function(){if(!COMPILED&&!goog.isInGoogModuleLoader_())throw new Error("goog.module.declareLegacyNamespace must be called from "+"within a goog.module");if(!COMPILED&&!goog.moduleLoaderState_.moduleName)throw new Error("goog.module must be called prior to "+"goog.module.declareLegacyNamespace.");goog.moduleLoaderState_.declareLegacyNamespace=true};
goog.module.preventModuleExportSealing=function(){if(!COMPILED&&!goog.isInGoogModuleLoader_())throw new Error("goog.module.preventModuleExportSealing must be called from "+"within a goog.module");if(!COMPILED&&!goog.moduleLoaderState_.moduleName)throw new Error("goog.module must be called prior to "+"goog.module.preventModuleExportSealing.");goog.moduleLoaderState_.preventModuleExportSealing=true};
goog.declareModuleId=function(namespace){if(!COMPILED){if(!goog.isInEs6ModuleLoader_())throw new Error("goog.declareModuleId may only be called from "+"within an ES6 module");if(goog.moduleLoaderState_&&goog.moduleLoaderState_.moduleName)throw new Error("goog.declareModuleId may only be called once per module.");if(namespace in goog.loadedModules_)throw new Error('Module with namespace "'+namespace+'" already exists.');}if(goog.moduleLoaderState_)goog.moduleLoaderState_.moduleName=namespace;else{var jscomp=
goog.LEGACY_NAMESPACE_OBJECT_["$jscomp"];if(!jscomp||typeof jscomp.getCurrentModulePath!="function")throw new Error('Module with namespace "'+namespace+'" has been loaded incorrectly.');var exports=jscomp.require(jscomp.getCurrentModulePath());goog.loadedModules_[namespace]={exports:exports,type:goog.ModuleType.ES6,moduleId:namespace}}};
goog.setTestOnly=function(opt_message){if(goog.DISALLOW_TEST_ONLY_CODE){opt_message=opt_message||"";throw new Error("Importing test-only code into non-debug environment"+(opt_message?": "+opt_message:"."));}};goog.forwardDeclare=function(name){};if(!COMPILED){goog.isProvided_=function(name){return name in goog.loadedModules_||!goog.implicitNamespaces_[name]&&goog.getObjectByName(name,goog.LEGACY_NAMESPACE_OBJECT_)!=null};goog.implicitNamespaces_={"goog.module":true}}
goog.getObjectByName=function(name,opt_obj){var parts=name.split(".");var cur=opt_obj||goog.global;for(var i=0;i<parts.length;i++){cur=cur[parts[i]];if(cur==null)return null}return cur};goog.addDependency=function(relPath,provides,requires,opt_loadFlags){if(!COMPILED&&goog.DEPENDENCIES_ENABLED)goog.debugLoader_.addDependency(relPath,provides,requires,opt_loadFlags)};goog.ENABLE_DEBUG_LOADER=goog.define("goog.ENABLE_DEBUG_LOADER",false);goog.logToConsole_=function(msg){if(goog.global.console)goog.global.console["error"](msg)};
goog.require=function(namespace){if(!COMPILED){if(goog.ENABLE_DEBUG_LOADER)goog.debugLoader_.requested(namespace);if(goog.isProvided_(namespace)){if(goog.isInModuleLoader_())return goog.module.getInternal_(namespace)}else if(goog.ENABLE_DEBUG_LOADER){var moduleLoaderState=goog.moduleLoaderState_;goog.moduleLoaderState_=null;try{goog.debugLoader_.load_(namespace)}finally{goog.moduleLoaderState_=moduleLoaderState}}return null}};goog.requireType=function(namespace){return{}};goog.basePath="";goog.global.CLOSURE_BASE_PATH;
goog.global.CLOSURE_IMPORT_SCRIPT;goog.abstractMethod=function(){throw new Error("unimplemented abstract method");};goog.addSingletonGetter=function(ctor){ctor.instance_=undefined;ctor.getInstance=function(){if(ctor.instance_)return ctor.instance_;if(goog.DEBUG)goog.instantiatedSingletons_[goog.instantiatedSingletons_.length]=ctor;return ctor.instance_=new ctor}};goog.instantiatedSingletons_=[];goog.LOAD_MODULE_USING_EVAL=goog.define("goog.LOAD_MODULE_USING_EVAL",true);
goog.SEAL_MODULE_EXPORTS=goog.define("goog.SEAL_MODULE_EXPORTS",goog.DEBUG);goog.loadedModules_={};goog.DEPENDENCIES_ENABLED=!COMPILED&&goog.ENABLE_DEBUG_LOADER;goog.ASSUME_ES_MODULES_TRANSPILED=goog.define("goog.ASSUME_ES_MODULES_TRANSPILED",false);goog.TRUSTED_TYPES_POLICY_NAME=goog.define("goog.TRUSTED_TYPES_POLICY_NAME","goog");
goog.loadModule=function(moduleDef){var previousState=goog.moduleLoaderState_;try{goog.moduleLoaderState_={moduleName:"",declareLegacyNamespace:false,preventModuleExportSealing:false,type:goog.ModuleType.GOOG};var origExports={};var exports=origExports;if(typeof moduleDef==="function")exports=moduleDef.call(undefined,exports);else if(typeof moduleDef==="string")exports=goog.loadModuleFromSource_.call(undefined,exports,moduleDef);else throw new Error("Invalid module definition");var moduleName=goog.moduleLoaderState_.moduleName;
if(typeof moduleName==="string"&&moduleName){if(goog.moduleLoaderState_.declareLegacyNamespace){var isDefaultExport=origExports!==exports;goog.constructNamespace_(moduleName,exports,isDefaultExport)}else if(goog.SEAL_MODULE_EXPORTS&&Object.seal&&typeof exports=="object"&&exports!=null&&!goog.moduleLoaderState_.preventModuleExportSealing)Object.seal(exports);var data={exports:exports,type:goog.ModuleType.GOOG,moduleId:goog.moduleLoaderState_.moduleName};goog.loadedModules_[moduleName]=data}else throw new Error('Invalid module name "'+
moduleName+'"');}finally{goog.moduleLoaderState_=previousState}};goog.loadModuleFromSource_=function(exports){eval(goog.CLOSURE_EVAL_PREFILTER_.createScript(arguments[1]));return exports};goog.normalizePath_=function(path){var components=path.split("/");var i=0;while(i<components.length)if(components[i]==".")components.splice(i,1);else if(i&&components[i]==".."&&components[i-1]&&components[i-1]!="..")components.splice(--i,2);else i++;return components.join("/")};goog.global.CLOSURE_LOAD_FILE_SYNC;
goog.loadFileSync_=function(src){if(goog.global.CLOSURE_LOAD_FILE_SYNC)return goog.global.CLOSURE_LOAD_FILE_SYNC(src);else try{var xhr=new goog.global["XMLHttpRequest"];xhr.open("get",src,false);xhr.send();return xhr.status==0||xhr.status==200?xhr.responseText:null}catch(err){return null}};goog.typeOf=function(value){var s=typeof value;if(s!="object")return s;if(!value)return"null";if(Array.isArray(value))return"array";return s};
goog.isArrayLike=function(val){var type=goog.typeOf(val);return type=="array"||type=="object"&&typeof val.length=="number"};goog.isDateLike=function(val){return goog.isObject(val)&&typeof val.getFullYear=="function"};goog.isObject=function(val){var type=typeof val;return type=="object"&&val!=null||type=="function"};goog.getUid=function(obj){return Object.prototype.hasOwnProperty.call(obj,goog.UID_PROPERTY_)&&obj[goog.UID_PROPERTY_]||(obj[goog.UID_PROPERTY_]=++goog.uidCounter_)};goog.hasUid=function(obj){return!!obj[goog.UID_PROPERTY_]};
goog.removeUid=function(obj){if(obj!==null&&"removeAttribute"in obj)obj.removeAttribute(goog.UID_PROPERTY_);try{delete obj[goog.UID_PROPERTY_]}catch(ex){}};goog.UID_PROPERTY_="closure_uid_"+(Math.random()*1E9>>>0);goog.uidCounter_=0;
goog.cloneObject=function(obj){var type=goog.typeOf(obj);if(type=="object"||type=="array"){if(typeof obj.clone==="function")return obj.clone();if(typeof Map!=="undefined"&&obj instanceof Map)return new Map(obj);else if(typeof Set!=="undefined"&&obj instanceof Set)return new Set(obj);var clone=type=="array"?[]:{};for(var key in obj)clone[key]=goog.cloneObject(obj[key]);return clone}return obj};goog.bindNative_=function(fn,selfObj,var_args){return fn.call.apply(fn.bind,arguments)};
goog.bindJs_=function(fn,selfObj,var_args){if(!fn)throw new Error;if(arguments.length>2){var boundArgs=Array.prototype.slice.call(arguments,2);return function(){var newArgs=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(newArgs,boundArgs);return fn.apply(selfObj,newArgs)}}else return function(){return fn.apply(selfObj,arguments)}};
goog.bind=function(fn,selfObj,var_args){if(goog.TRUSTED_SITE&&goog.FEATURESET_YEAR>2012||Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1)goog.bind=goog.bindNative_;else goog.bind=goog.bindJs_;return goog.bind.apply(null,arguments)};goog.partial=function(fn,var_args){var args=Array.prototype.slice.call(arguments,1);return function(){var newArgs=args.slice();newArgs.push.apply(newArgs,arguments);return fn.apply(this,newArgs)}};goog.now=function(){return Date.now()};
goog.globalEval=function(script){(0,eval)(script)};goog.cssNameMapping_;goog.cssNameMappingStyle_;goog.global.CLOSURE_CSS_NAME_MAP_FN;
goog.getCssName=function(className,opt_modifier){if(String(className).charAt(0)==".")throw new Error('className passed in goog.getCssName must not start with ".".'+" You passed: "+className);var getMapping=function(cssName){return goog.cssNameMapping_[cssName]||cssName};var renameByParts=function(cssName){var parts=cssName.split("-");var mapped=[];for(var i=0;i<parts.length;i++)mapped.push(getMapping(parts[i]));return mapped.join("-")};var rename;if(goog.cssNameMapping_)rename=goog.cssNameMappingStyle_==
"BY_WHOLE"?getMapping:renameByParts;else rename=function(a){return a};var result=opt_modifier?className+"-"+rename(opt_modifier):rename(className);if(goog.global.CLOSURE_CSS_NAME_MAP_FN)return goog.global.CLOSURE_CSS_NAME_MAP_FN(result);return result};goog.setCssNameMapping=function(mapping,opt_style){goog.cssNameMapping_=mapping;goog.cssNameMappingStyle_=opt_style};goog.global.CLOSURE_CSS_NAME_MAPPING;if(!COMPILED&&goog.global.CLOSURE_CSS_NAME_MAPPING)goog.cssNameMapping_=goog.global.CLOSURE_CSS_NAME_MAPPING;
goog.GetMsgOptions=function(){};goog.GetMsgOptions.prototype.html;goog.GetMsgOptions.prototype.unescapeHtmlEntities;goog.GetMsgOptions.prototype.original_code;goog.GetMsgOptions.prototype.example;
goog.getMsg=function(str,opt_values,opt_options){if(opt_options&&opt_options.html)str=str.replace(/</g,"&lt;");if(opt_options&&opt_options.unescapeHtmlEntities)str=str.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&apos;/g,"'").replace(/&quot;/g,'"').replace(/&amp;/g,"&");if(opt_values)str=str.replace(/\{\$([^}]+)}/g,function(match,key){return opt_values!=null&&key in opt_values?opt_values[key]:match});return str};goog.getMsgWithFallback=function(a,b){return a};
goog.exportSymbol=function(publicPath,object,objectToExportTo){goog.exportPath_(publicPath,object,true,objectToExportTo)};goog.exportProperty=function(object,publicName,symbol){object[publicName]=symbol};goog.weakUsage=function(name){return name};
goog.inherits=function(childCtor,parentCtor){function tempCtor(){}tempCtor.prototype=parentCtor.prototype;childCtor.superClass_=parentCtor.prototype;childCtor.prototype=new tempCtor;childCtor.prototype.constructor=childCtor;childCtor.base=function(me,methodName,var_args){var args=new Array(arguments.length-2);for(var i=2;i<arguments.length;i++)args[i-2]=arguments[i];return parentCtor.prototype[methodName].apply(me,args)}};
goog.scope=function(fn){if(goog.isInModuleLoader_())throw new Error("goog.scope is not supported within a module.");fn.call(goog.global)};if(!COMPILED)goog.LEGACY_NAMESPACE_OBJECT_["COMPILED"]=COMPILED;goog.identity_=function(s){return s};
goog.createTrustedTypesPolicy=function(name){var policy=null;var policyFactory=goog.global.trustedTypes;if(!policyFactory||!policyFactory.createPolicy)return policy;try{policy=policyFactory.createPolicy(name,{createHTML:goog.identity_,createScript:goog.identity_,createScriptURL:goog.identity_})}catch(e){goog.logToConsole_(e.message)}return policy};
if(!COMPILED&&goog.DEPENDENCIES_ENABLED){goog.dependencies_={loadFlags:{},nameToPath:{},requires:{},visited:{},written:{},deferred:{}};goog.getLoader_=function(){return{dependencies_:goog.dependencies_,writeScriptTag_:goog.writeScriptTag_}};goog.writeScriptTag_=function(src,opt_sourceText){if(goog.inHtmlDocument_()){var doc=goog.global.document;if(!goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING&&doc.readyState=="complete"){var isDeps=/\bdeps.js$/.test(src);if(isDeps)return false;else throw Error('Cannot write "'+
src+'" after document load');}var nonceAttr="";var nonce=goog.getScriptNonce_();if(nonce)nonceAttr=' nonce="'+nonce+'"';if(opt_sourceText===undefined){var script='<script src="'+src+'"'+nonceAttr+"></"+"script>";doc.write(goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createHTML(script):script)}else{var script="<script"+nonceAttr+">"+goog.protectScriptTag_(opt_sourceText)+"</"+"script>";doc.write(goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createHTML(script):script)}return true}else return false};
goog.inHtmlDocument_=function(){var doc=goog.global.document;return doc!=null&&"write"in doc};goog.isDocumentLoading_=function(){var doc=goog.global.document;return doc.attachEvent?doc.readyState!="complete":doc.readyState=="loading"};goog.findBasePath_=function(){if(goog.global.CLOSURE_BASE_PATH!=undefined&&typeof goog.global.CLOSURE_BASE_PATH==="string"){goog.basePath=goog.global.CLOSURE_BASE_PATH;return}else if(!goog.inHtmlDocument_())return;var doc=goog.global.document;var currentScript=doc.currentScript;
if(currentScript)var scripts=[currentScript];else var scripts=doc.getElementsByTagName("SCRIPT");for(var i=scripts.length-1;i>=0;--i){var script=scripts[i];var src=script.src;var qmark=src.lastIndexOf("?");var l=qmark==-1?src.length:qmark;if(src.slice(l-7,l)=="base.js"){goog.basePath=src.slice(0,l-7);return}}};goog.findBasePath_();goog.protectScriptTag_=function(str){return str.replace(/<\/(SCRIPT)/ig,"\\x3c/$1")};goog.DebugLoader_=function(){this.dependencies_={};this.idToPath_={};this.written_=
{};this.loadingDeps_=[];this.depsToLoad_=[];this.paused_=false;this.factory_=new goog.DependencyFactory;this.deferredCallbacks_={};this.deferredQueue_=[]};goog.DebugLoader_.prototype.loadClosureDeps=function(){var relPath="deps.js";this.depsToLoad_.push(this.factory_.createDependency(goog.normalizePath_(goog.basePath+relPath),relPath,[],[],{}));this.loadDeps_()};goog.DebugLoader_.prototype.requested=function(absPathOrId,opt_force){var path=this.getPathFromDeps_(absPathOrId);if(path&&(opt_force||this.areDepsLoaded_(this.dependencies_[path].requires))){var callback=
this.deferredCallbacks_[path];if(callback){delete this.deferredCallbacks_[path];callback()}}};goog.DebugLoader_.prototype.setDependencyFactory=function(factory){this.factory_=factory};goog.DebugLoader_.prototype.load_=function(namespace){if(!this.getPathFromDeps_(namespace)){var errorMessage="goog.require could not find: "+namespace;goog.logToConsole_(errorMessage)}else{var loader=this;var deps=[];var visit=function(namespace){var path=loader.getPathFromDeps_(namespace);if(!path)throw new Error("Bad dependency path or symbol: "+
namespace);if(loader.written_[path])return;loader.written_[path]=true;var dep=loader.dependencies_[path];if(goog.dependencies_.written[dep.relativePath])return;for(var i=0;i<dep.requires.length;i++)if(!goog.isProvided_(dep.requires[i]))visit(dep.requires[i]);deps.push(dep)};visit(namespace);var wasLoading=!!this.depsToLoad_.length;this.depsToLoad_=this.depsToLoad_.concat(deps);if(!this.paused_&&!wasLoading)this.loadDeps_()}};goog.DebugLoader_.prototype.loadDeps_=function(){var loader=this;var paused=
this.paused_;while(this.depsToLoad_.length&&!paused)(function(){var loadCallDone=false;var dep=loader.depsToLoad_.shift();var loaded=false;loader.loading_(dep);var controller={pause:function(){if(loadCallDone)throw new Error("Cannot call pause after the call to load.");else paused=true},resume:function(){if(loadCallDone)loader.resume_();else paused=false},loaded:function(){if(loaded)throw new Error("Double call to loaded.");loaded=true;loader.loaded_(dep)},pending:function(){var pending=[];for(var i=
0;i<loader.loadingDeps_.length;i++)pending.push(loader.loadingDeps_[i]);return pending},setModuleState:function(type){goog.moduleLoaderState_={type:type,moduleName:"",declareLegacyNamespace:false,preventModuleExportSealing:false}},registerEs6ModuleExports:function(path,exports,opt_closureNamespace){if(opt_closureNamespace)goog.loadedModules_[opt_closureNamespace]={exports:exports,type:goog.ModuleType.ES6,moduleId:opt_closureNamespace||""}},registerGoogModuleExports:function(moduleId,exports){goog.loadedModules_[moduleId]=
{exports:exports,type:goog.ModuleType.GOOG,moduleId:moduleId}},clearModuleState:function(){goog.moduleLoaderState_=null},defer:function(callback){if(loadCallDone)throw new Error("Cannot register with defer after the call to load.");loader.defer_(dep,callback)},areDepsLoaded:function(){return loader.areDepsLoaded_(dep.requires)}};try{dep.load(controller)}finally{loadCallDone=true}})();if(paused)this.pause_()};goog.DebugLoader_.prototype.pause_=function(){this.paused_=true};goog.DebugLoader_.prototype.resume_=
function(){if(this.paused_){this.paused_=false;this.loadDeps_()}};goog.DebugLoader_.prototype.loading_=function(dep){this.loadingDeps_.push(dep)};goog.DebugLoader_.prototype.loaded_=function(dep){for(var i=0;i<this.loadingDeps_.length;i++)if(this.loadingDeps_[i]==dep){this.loadingDeps_.splice(i,1);break}for(var i=0;i<this.deferredQueue_.length;i++)if(this.deferredQueue_[i]==dep.path){this.deferredQueue_.splice(i,1);break}if(this.loadingDeps_.length==this.deferredQueue_.length&&!this.depsToLoad_.length)while(this.deferredQueue_.length)this.requested(this.deferredQueue_.shift(),
true);dep.loaded()};goog.DebugLoader_.prototype.areDepsLoaded_=function(pathsOrIds){for(var i=0;i<pathsOrIds.length;i++){var path=this.getPathFromDeps_(pathsOrIds[i]);if(!path||!(path in this.deferredCallbacks_)&&!goog.isProvided_(pathsOrIds[i]))return false}return true};goog.DebugLoader_.prototype.getPathFromDeps_=function(absPathOrId){if(absPathOrId in this.idToPath_)return this.idToPath_[absPathOrId];else if(absPathOrId in this.dependencies_)return absPathOrId;else return null};goog.DebugLoader_.prototype.defer_=
function(dependency,callback){this.deferredCallbacks_[dependency.path]=callback;this.deferredQueue_.push(dependency.path)};goog.LoadController=function(){};goog.LoadController.prototype.pause=function(){};goog.LoadController.prototype.resume=function(){};goog.LoadController.prototype.loaded=function(){};goog.LoadController.prototype.pending=function(){};goog.LoadController.prototype.registerEs6ModuleExports=function(path,exports,opt_closureNamespace){};goog.LoadController.prototype.setModuleState=
function(type){};goog.LoadController.prototype.clearModuleState=function(){};goog.LoadController.prototype.defer=function(callback){};goog.LoadController.prototype.areDepsLoaded=function(){};goog.Dependency=function(path,relativePath,provides,requires,loadFlags){this.path=path;this.relativePath=relativePath;this.provides=provides;this.requires=requires;this.loadFlags=loadFlags;this.loaded_=false;this.loadCallbacks_=[]};goog.Dependency.prototype.getPathName=function(){var pathName=this.path;var protocolIndex=
pathName.indexOf("://");if(protocolIndex>=0){pathName=pathName.substring(protocolIndex+3);var slashIndex=pathName.indexOf("/");if(slashIndex>=0)pathName=pathName.substring(slashIndex+1)}return pathName};goog.Dependency.prototype.onLoad=function(callback){if(this.loaded_)callback();else this.loadCallbacks_.push(callback)};goog.Dependency.prototype.loaded=function(){this.loaded_=true;var callbacks=this.loadCallbacks_;this.loadCallbacks_=[];for(var i=0;i<callbacks.length;i++)callbacks[i]()};goog.Dependency.defer_=
false;goog.Dependency.callbackMap_={};goog.Dependency.registerCallback_=function(callback){var key=Math.random().toString(32);goog.Dependency.callbackMap_[key]=callback;return key};goog.Dependency.unregisterCallback_=function(key){delete goog.Dependency.callbackMap_[key]};goog.Dependency.callback_=function(key,var_args){if(key in goog.Dependency.callbackMap_){var callback=goog.Dependency.callbackMap_[key];var args=[];for(var i=1;i<arguments.length;i++)args.push(arguments[i]);callback.apply(undefined,
args)}else{var errorMessage="Callback key "+key+" does not exist (was base.js loaded more than once?).";goog.logToConsole_(errorMessage)}};goog.Dependency.prototype.load=function(controller){if(goog.global.CLOSURE_IMPORT_SCRIPT){if(goog.global.CLOSURE_IMPORT_SCRIPT(this.path))controller.loaded();else controller.pause();return}if(!goog.inHtmlDocument_()){goog.logToConsole_("Cannot use default debug loader outside of HTML documents.");if(this.relativePath=="deps.js"){goog.logToConsole_("Consider setting CLOSURE_IMPORT_SCRIPT before loading base.js.");
controller.loaded()}else controller.pause();return}var doc=goog.global.document;if(doc.readyState=="complete"&&!goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING){var isDeps=/\bdeps.js$/.test(this.path);if(isDeps){controller.loaded();return}else throw Error('Cannot write "'+this.path+'" after document load');}var nonce=goog.getScriptNonce_();if(!goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING&&goog.isDocumentLoading_()){var key;var callback=function(script){if(script.readyState&&script.readyState!="complete"){script.onload=
callback;return}goog.Dependency.unregisterCallback_(key);controller.loaded()};key=goog.Dependency.registerCallback_(callback);var defer=goog.Dependency.defer_?" defer":"";var nonceAttr=nonce?' nonce="'+nonce+'"':"";var script='<script src="'+this.path+'"'+nonceAttr+defer+' id="script-'+key+'">\x3c/script>';script+="<script"+nonceAttr+">";if(goog.Dependency.defer_)script+="document.getElementById('script-"+key+"').onload = function() {\n"+"  goog.Dependency.callback_('"+key+"', this);\n"+"};\n";else script+=
"goog.Dependency.callback_('"+key+"', document.getElementById('script-"+key+"'));";script+="\x3c/script>";doc.write(goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createHTML(script):script)}else{var scriptEl=doc.createElement("script");scriptEl.defer=goog.Dependency.defer_;scriptEl.async=false;if(nonce)scriptEl.nonce=nonce;scriptEl.onload=function(){scriptEl.onload=null;controller.loaded()};scriptEl.src=goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createScriptURL(this.path):this.path;
doc.head.appendChild(scriptEl)}};goog.Es6ModuleDependency=function(path,relativePath,provides,requires,loadFlags){goog.Dependency.call(this,path,relativePath,provides,requires,loadFlags)};goog.inherits(goog.Es6ModuleDependency,goog.Dependency);goog.Es6ModuleDependency.prototype.load=function(controller){if(goog.global.CLOSURE_IMPORT_SCRIPT){if(goog.global.CLOSURE_IMPORT_SCRIPT(this.path))controller.loaded();else controller.pause();return}if(!goog.inHtmlDocument_()){goog.logToConsole_("Cannot use default debug loader outside of HTML documents.");
controller.pause();return}var doc=goog.global.document;var dep=this;function write(src,contents){var nonceAttr="";var nonce=goog.getScriptNonce_();if(nonce)nonceAttr=' nonce="'+nonce+'"';if(contents){var script='<script type="module" crossorigin'+nonceAttr+">"+contents+"</"+"script>";doc.write(goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createHTML(script):script)}else{var script='<script type="module" crossorigin src="'+src+'"'+nonceAttr+"></"+"script>";doc.write(goog.TRUSTED_TYPES_POLICY_?
goog.TRUSTED_TYPES_POLICY_.createHTML(script):script)}}function append(src,contents){var scriptEl=doc.createElement("script");scriptEl.defer=true;scriptEl.async=false;scriptEl.type="module";scriptEl.setAttribute("crossorigin",true);var nonce=goog.getScriptNonce_();if(nonce)scriptEl.nonce=nonce;if(contents)scriptEl.text=goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createScript(contents):contents;else scriptEl.src=goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createScriptURL(src):src;
doc.head.appendChild(scriptEl)}var create;if(goog.isDocumentLoading_()){create=write;goog.Dependency.defer_=true}else create=append;var beforeKey=goog.Dependency.registerCallback_(function(){goog.Dependency.unregisterCallback_(beforeKey);controller.setModuleState(goog.ModuleType.ES6)});create(undefined,'goog.Dependency.callback_("'+beforeKey+'")');create(this.path,undefined);var registerKey=goog.Dependency.registerCallback_(function(exports){goog.Dependency.unregisterCallback_(registerKey);controller.registerEs6ModuleExports(dep.path,
exports,goog.moduleLoaderState_.moduleName)});create(undefined,'import * as m from "'+this.path+'"; goog.Dependency.callback_("'+registerKey+'", m)');var afterKey=goog.Dependency.registerCallback_(function(){goog.Dependency.unregisterCallback_(afterKey);controller.clearModuleState();controller.loaded()});create(undefined,'goog.Dependency.callback_("'+afterKey+'")')};goog.TransformedDependency=function(path,relativePath,provides,requires,loadFlags){goog.Dependency.call(this,path,relativePath,provides,
requires,loadFlags);this.contents_=null;this.lazyFetch_=!goog.inHtmlDocument_()||!("noModule"in goog.global.document.createElement("script"))};goog.inherits(goog.TransformedDependency,goog.Dependency);goog.TransformedDependency.prototype.load=function(controller){var dep=this;function fetch(){dep.contents_=goog.loadFileSync_(dep.path);if(dep.contents_){dep.contents_=dep.transform(dep.contents_);if(dep.contents_)dep.contents_+="\n//# sourceURL="+dep.path}}if(goog.global.CLOSURE_IMPORT_SCRIPT){fetch();
if(this.contents_&&goog.global.CLOSURE_IMPORT_SCRIPT("",this.contents_)){this.contents_=null;controller.loaded()}else controller.pause();return}var isEs6=this.loadFlags["module"]==goog.ModuleType.ES6;if(!this.lazyFetch_)fetch();function load(){if(dep.lazyFetch_)fetch();if(!dep.contents_)return;if(isEs6)controller.setModuleState(goog.ModuleType.ES6);var namespace;try{var contents=dep.contents_;dep.contents_=null;goog.globalEval(goog.CLOSURE_EVAL_PREFILTER_.createScript(contents));if(isEs6)namespace=
goog.moduleLoaderState_.moduleName}finally{if(isEs6)controller.clearModuleState()}if(isEs6)goog.LEGACY_NAMESPACE_OBJECT_["$jscomp"]["require"]["ensure"]([dep.getPathName()],function(){controller.registerEs6ModuleExports(dep.path,goog.LEGACY_NAMESPACE_OBJECT_["$jscomp"]["require"](dep.getPathName()),namespace)});controller.loaded()}function fetchInOwnScriptThenLoad(){var doc=goog.global.document;var key=goog.Dependency.registerCallback_(function(){goog.Dependency.unregisterCallback_(key);load()});
var nonce=goog.getScriptNonce_();var nonceAttr=nonce?' nonce="'+nonce+'"':"";var script="<script"+nonceAttr+">"+goog.protectScriptTag_('goog.Dependency.callback_("'+key+'");')+"</"+"script>";doc.write(goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createHTML(script):script)}var anythingElsePending=controller.pending().length>1;var needsAsyncLoading=goog.Dependency.defer_&&(anythingElsePending||goog.isDocumentLoading_());if(needsAsyncLoading){controller.defer(function(){load()});return}var doc=
goog.global.document;if(isEs6&&goog.inHtmlDocument_()&&goog.isDocumentLoading_()){goog.Dependency.defer_=true;controller.pause();var oldCallback=doc.onreadystatechange;doc.onreadystatechange=function(){if(doc.readyState=="interactive"){doc.onreadystatechange=oldCallback;load();controller.resume()}if(typeof oldCallback==="function")oldCallback.apply(undefined,arguments)}}else if(!goog.inHtmlDocument_()||!goog.isDocumentLoading_())load();else fetchInOwnScriptThenLoad()};goog.TransformedDependency.prototype.transform=
function(contents){};goog.PreTranspiledEs6ModuleDependency=function(path,relativePath,provides,requires,loadFlags){goog.TransformedDependency.call(this,path,relativePath,provides,requires,loadFlags)};goog.inherits(goog.PreTranspiledEs6ModuleDependency,goog.TransformedDependency);goog.PreTranspiledEs6ModuleDependency.prototype.transform=function(contents){return contents};goog.GoogModuleDependency=function(path,relativePath,provides,requires,loadFlags){goog.TransformedDependency.call(this,path,relativePath,
provides,requires,loadFlags)};goog.inherits(goog.GoogModuleDependency,goog.TransformedDependency);goog.GoogModuleDependency.prototype.transform=function(contents){if(!goog.LOAD_MODULE_USING_EVAL||goog.global.JSON===undefined)return""+"goog.loadModule(function(exports) {"+'"use strict";'+contents+"\n"+";return exports"+"});"+"\n//# sourceURL="+this.path+"\n";else return""+"goog.loadModule("+goog.global.JSON.stringify(contents+"\n//# sourceURL="+this.path+"\n")+");"};goog.DebugLoader_.prototype.addDependency=
function(relPath,provides,requires,opt_loadFlags){provides=provides||[];relPath=relPath.replace(/\\/g,"/");var path=goog.normalizePath_(goog.basePath+relPath);if(!opt_loadFlags||typeof opt_loadFlags==="boolean")opt_loadFlags=opt_loadFlags?{"module":goog.ModuleType.GOOG}:{};var dep=this.factory_.createDependency(path,relPath,provides,requires,opt_loadFlags);this.dependencies_[path]=dep;for(var i=0;i<provides.length;i++)this.idToPath_[provides[i]]=path;this.idToPath_[relPath]=path};goog.DependencyFactory=
function(){};goog.DependencyFactory.prototype.createDependency=function(path,relativePath,provides,requires,loadFlags){var provide,require;for(var i=0;provide=provides[i];i++){goog.dependencies_.nameToPath[provide]=relativePath;goog.dependencies_.loadFlags[relativePath]=loadFlags}for(var j=0;require=requires[j];j++){if(!(relativePath in goog.dependencies_.requires))goog.dependencies_.requires[relativePath]={};goog.dependencies_.requires[relativePath][require]=true}if(loadFlags["module"]==goog.ModuleType.GOOG)return new goog.GoogModuleDependency(path,
relativePath,provides,requires,loadFlags);else if(loadFlags["module"]==goog.ModuleType.ES6)if(goog.ASSUME_ES_MODULES_TRANSPILED)return new goog.PreTranspiledEs6ModuleDependency(path,relativePath,provides,requires,loadFlags);else return new goog.Es6ModuleDependency(path,relativePath,provides,requires,loadFlags);else return new goog.Dependency(path,relativePath,provides,requires,loadFlags)};goog.debugLoader_=new goog.DebugLoader_;goog.loadClosureDeps=function(){goog.debugLoader_.loadClosureDeps()};
goog.setDependencyFactory=function(factory){goog.debugLoader_.setDependencyFactory(factory)};goog.TRUSTED_TYPES_POLICY_=goog.TRUSTED_TYPES_POLICY_NAME?goog.createTrustedTypesPolicy(goog.TRUSTED_TYPES_POLICY_NAME+"#base"):null}if(!COMPILED){var isChrome87=false;try{isChrome87=eval(goog.global.trustedTypes.emptyScript)!==goog.global.trustedTypes.emptyScript}catch(err){}goog.CLOSURE_EVAL_PREFILTER_=goog.global.trustedTypes&&isChrome87&&goog.createTrustedTypesPolicy("goog#base#devonly#eval")||{createScript:goog.identity_}}
goog.CodeLocation={DO_NOT_USE:"",DO_NOT_USE_ME_EITHER:"."};goog.callerLocation=function(){if(!COMPILED){var err=new Error;var stack=err.stack;if(stack)stack=stack.split(/\n/g)[3];if(stack)stack=stack.trim();return stack||""}return""};goog.callerLocationIdInternalDoNotCallOrElse=function(id){return id};var module$exports$google3$third_party$javascript$tslib$tslib={};var module$contents$google3$third_party$javascript$tslib$tslib_extendStatics=Object.setPrototypeOf||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};
module$exports$google3$third_party$javascript$tslib$tslib.__extends=function(d,b){if(typeof b!=="function"&&b!==null&&(b===undefined||b!==goog.global["Event"]))throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");module$contents$google3$third_party$javascript$tslib$tslib_extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)};
module$exports$google3$third_party$javascript$tslib$tslib.__assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)if(Object.prototype.hasOwnProperty.call(s,p))t[p]=s[p]}return t};
module$exports$google3$third_party$javascript$tslib$tslib.__rest=function(s,e){var t={};for(var p in s)if(Object.prototype.hasOwnProperty.call(s,p)&&e.indexOf(p)<0)t[p]=s[p];if(s!=null&&typeof Object.getOwnPropertySymbols==="function")for(var i=0,p=Object.getOwnPropertySymbols(s);i<p.length;i++)if(e.indexOf(p[i])<0&&Object.prototype.propertyIsEnumerable.call(s,p[i]))t[p[i]]=s[p[i]];return t};
module$exports$google3$third_party$javascript$tslib$tslib.__decorate=function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(Reflect&&typeof Reflect==="object"&&typeof Reflect["decorate"]==="function")r=Reflect["decorate"](decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),
r};module$exports$google3$third_party$javascript$tslib$tslib.__param=function(paramIndex,decorator){return function(target,key){decorator(target,key,paramIndex)}};module$exports$google3$third_party$javascript$tslib$tslib.__setFunctionName=function(f,name,prefix){if(typeof name==="symbol")name=name.description?"[".concat(name.description,"]"):"";return Object.defineProperty(f,"name",{configurable:true,value:prefix?"".concat(prefix," ",name):name})};
module$exports$google3$third_party$javascript$tslib$tslib.__metadata=function(metadataKey,metadataValue){if(Reflect&&typeof Reflect==="object"&&typeof Reflect["metadata"]==="function")return Reflect["metadata"](metadataKey,metadataValue)};
module$exports$google3$third_party$javascript$tslib$tslib.__awaiter=function(thisArg,_arguments,P,generator){function adopt(value){return value instanceof P?value:new P(function(resolve){resolve(value)})}return new (P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value))}catch(e){reject(e)}}function rejected(value){try{step(generator["throw"](value))}catch(e){reject(e)}}function step(result){result.done?resolve(result.value):adopt(result.value).then(fulfilled,
rejected)}step((generator=generator.apply(thisArg,_arguments||[])).next())})};
module$exports$google3$third_party$javascript$tslib$tslib.__generator=function(thisArg,body){var _={label:0,sent:function(){if(t[0]&1)throw t[1];return t[1]},trys:[],ops:[]},f,y,t,g=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return g["next"]=verb(0),g["throw"]=verb(1),g["return"]=verb(2),typeof Symbol==="function"&&(g[Symbol.iterator]=function(){return this}),g;function verb(n){return function(v){return step([n,v])}}function step(op){if(f)throw new TypeError("Generator is already executing.");
while(g&&(g=0,op[0]&&(_=0)),_)try{if(f=1,y&&(t=op[0]&2?y["return"]:op[0]?y["throw"]||((t=y["return"])&&t.call(y),0):y.next)&&!(t=t.call(y,op[1])).done)return t;if(y=0,t)op=[op[0]&2,t.value];switch(op[0]){case 0:case 1:t=op;break;case 4:_.label++;return{value:op[1],done:false};case 5:_.label++;y=op[1];op=[0];continue;case 7:op=_.ops.pop();_.trys.pop();continue;default:if(!(t=_.trys,t=t.length>0&&t[t.length-1])&&(op[0]===6||op[0]===2)){_=0;continue}if(op[0]===3&&(!t||op[1]>t[0]&&op[1]<t[3])){_.label=
op[1];break}if(op[0]===6&&_.label<t[1]){_.label=t[1];t=op;break}if(t&&_.label<t[2]){_.label=t[2];_.ops.push(op);break}if(t[2])_.ops.pop();_.trys.pop();continue}op=body.call(thisArg,_)}catch(e){op=[6,e];y=0}finally{f=t=0}if(op[0]&5)throw op[1];return{value:op[0]?op[1]:void 0,done:true}}};module$exports$google3$third_party$javascript$tslib$tslib.__exportStar=function(m,o){for(var p in m)if(p!=="default"&&!Object.prototype.hasOwnProperty.call(o,p))o[p]=m[p]};
module$exports$google3$third_party$javascript$tslib$tslib.__values=function(o){var s=typeof Symbol==="function"&&Symbol.iterator,m=s&&o[s],i=0;if(m)return m.call(o);if(o&&typeof o.length==="number")return{next:function(){if(o&&i>=o.length)o=void 0;return{value:o&&o[i++],done:!o}}};throw new TypeError(s?"Object is not iterable.":"Symbol.iterator is not defined.");};
module$exports$google3$third_party$javascript$tslib$tslib.__read=function(o,n){var m=typeof Symbol==="function"&&o[Symbol.iterator];if(!m)return o;var i=m.call(o),r,ar=[],e;try{while((n===void 0||n-- >0)&&!(r=i.next()).done)ar.push(r.value)}catch(error){e={error:error}}finally{try{if(r&&!r.done&&(m=i["return"]))m.call(i)}finally{if(e)throw e.error;}}return ar};
module$exports$google3$third_party$javascript$tslib$tslib.__spread=function(){for(var ar=[],i=0;i<arguments.length;i++)ar=ar.concat(module$exports$google3$third_party$javascript$tslib$tslib.__read(arguments[i]));return ar};module$exports$google3$third_party$javascript$tslib$tslib.__spreadArrays=function(){for(var s=0,i=0,il=arguments.length;i<il;i++)s+=arguments[i].length;for(var r=Array(s),k=0,i=0;i<il;i++)for(var a=arguments[i],j=0,jl=a.length;j<jl;j++,k++)r[k]=a[j];return r};
module$exports$google3$third_party$javascript$tslib$tslib.__spreadArray=function(to,from,pack){if(pack||arguments.length===2)for(var i=0,l=from.length,ar;i<l;i++)if(ar||!(i in from)){if(!ar)ar=Array.prototype.slice.call(from,0,i);ar[i]=from[i]}return to.concat(ar||Array.prototype.slice.call(from))};module$exports$google3$third_party$javascript$tslib$tslib.__await=function(v){return this instanceof module$exports$google3$third_party$javascript$tslib$tslib.__await?(this.v=v,this):new module$exports$google3$third_party$javascript$tslib$tslib.__await(v)};
module$exports$google3$third_party$javascript$tslib$tslib.__asyncGenerator=function(thisArg,_arguments,generator){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var g=generator.apply(thisArg,_arguments||[]),i,q=[];return i=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),verb("next"),verb("throw"),verb("return",awaitReturn),i[Symbol.asyncIterator]=function(){return this},i;function awaitReturn(f){return function(v){return Promise.resolve(v).then(f,
reject)}}function verb(n,f){if(g[n]){i[n]=function(v){return new Promise(function(a,b){q.push([n,v,a,b])>1||resume(n,v)})};if(f)i[n]=f(i[n])}}function resume(n,v){try{step(g[n](v))}catch(e){settle(q[0][3],e)}}function step(r){r.value instanceof module$exports$google3$third_party$javascript$tslib$tslib.__await?Promise.resolve(r.value.v).then(fulfill,reject):settle(q[0][2],r)}function fulfill(value){resume("next",value)}function reject(value){resume("throw",value)}function settle(f,v){if(f(v),q.shift(),
q.length)resume(q[0][0],q[0][1])}};module$exports$google3$third_party$javascript$tslib$tslib.__asyncDelegator=function(o){var i,p;return i={},verb("next"),verb("throw",function(e){throw e;}),verb("return"),i[Symbol.iterator]=function(){return this},i;function verb(n,f){i[n]=o[n]?function(v){return(p=!p)?{value:new module$exports$google3$third_party$javascript$tslib$tslib.__await(o[n](v)),done:false}:f?f(v):v}:f}};
module$exports$google3$third_party$javascript$tslib$tslib.__asyncValues=function(o){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var m=o[Symbol.asyncIterator],i;return m?m.call(o):(o=typeof __values==="function"?__values(o):o[Symbol.iterator](),i={},verb("next"),verb("throw"),verb("return"),i[Symbol.asyncIterator]=function(){return this},i);function verb(n){i[n]=o[n]&&function(v){return new Promise(function(resolve,reject){v=o[n](v),settle(resolve,reject,v.done,
v.value)})}}function settle(resolve,reject,d,v){Promise.resolve(v).then(function(v){resolve({value:v,done:d})},reject)}};module$exports$google3$third_party$javascript$tslib$tslib.__makeTemplateObject=function(cooked,raw){if(Object.defineProperty)Object.defineProperty(cooked,"raw",{value:raw});else cooked.raw=raw;return cooked};
module$exports$google3$third_party$javascript$tslib$tslib.__classPrivateFieldGet=function(receiver,state,kind,f){if(kind==="a"&&!f)throw new TypeError("Private accessor was defined without a getter");if(typeof state==="function"?receiver!==state||!f:!state.has(receiver))throw new TypeError("Cannot read private member from an object whose class did not declare it");return kind==="m"?f:kind==="a"?f.call(receiver):f?f.value:state.get(receiver)};
module$exports$google3$third_party$javascript$tslib$tslib.__classPrivateFieldSet=function(receiver,state,value,kind,f){if(kind==="m")throw new TypeError("Private method is not writable");if(kind==="a"&&!f)throw new TypeError("Private accessor was defined without a setter");if(typeof state==="function"?receiver!==state||!f:!state.has(receiver))throw new TypeError("Cannot write private member to an object whose class did not declare it");return kind==="a"?f.call(receiver,value):f?f.value=value:state.set(receiver,
value),value};module$exports$google3$third_party$javascript$tslib$tslib.__classPrivateFieldIn=function(state,receiver){if(receiver===null||typeof receiver!=="object"&&typeof receiver!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof state==="function"?receiver===state:state.has(receiver)};
module$exports$google3$third_party$javascript$tslib$tslib.__addDisposableResource=function(env,value,async){if(value!==null&&value!==void 0){if(typeof value!=="object"&&typeof value!=="function")throw new TypeError("Object expected.");var dispose,inner;var valueObject=value;if(async){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");dispose=valueObject[Symbol.asyncDispose]}if(dispose===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");
dispose=valueObject[Symbol.dispose];if(async)inner=dispose}if(typeof dispose!=="function")throw new TypeError("Object not disposable.");if(inner)dispose=function(){try{inner.call(this)}catch(e){return Promise.reject(e)}};env.stack.push({value:value,dispose:dispose,async:async})}else if(async)env.stack.push({async:true});return value};
module$exports$google3$third_party$javascript$tslib$tslib.__disposeResources=function(env){function fail(e){env.error=env.hasError?new SuppressedError(e,env.error,"An error was suppressed during disposal."):e;env.hasError=true}var r,s=0;function next(){while(r=env.stack.pop())try{if(!r.async&&s===1)return s=0,env.stack.push(r),Promise.resolve().then(next);if(r.dispose){var result=r.dispose.call(r.value);if(r.async)return s|=2,Promise.resolve(result).then(next,function(e){fail(e);return next()})}else s|=
1}catch(e){fail(e)}if(s===1)return env.hasError?Promise.reject(env.error):Promise.resolve();if(env.hasError)throw env.error;}return next()};var module$exports$google3$video$youtube$web$frontend$bootstrap$identity$stickiness$stickiness={};var module$contents$google3$video$youtube$web$frontend$bootstrap$identity$stickiness$stickiness_module=module$contents$google3$video$youtube$web$frontend$bootstrap$identity$stickiness$stickiness_module||{id:"video/youtube/web/frontend/bootstrap/identity/stickiness/stickiness.closure.js"};
function module$contents$google3$video$youtube$web$frontend$bootstrap$identity$stickiness$stickiness_handlePostMessage(event){const src=new URL(event.origin);if(!src.host.endsWith(".youtube.com"))return;try{const data=event.data;switch(data.action){case "clear":window.sessionStorage.removeItem("stickiness_reload");window.sessionStorage.removeItem("session_logininfo");return;case "save_pom_role":if(data.datasyncId&&data.roleText)window.localStorage.setItem(`${data.datasyncId}::pom::role_text`,JSON.stringify({"data":data.roleText,
"creation":Date.now()}));return;default:const loginInfo=data.loginInfo;if(!loginInfo)return;window.sessionStorage.removeItem("stickiness_reload");window.sessionStorage.setItem("session_logininfo",loginInfo)}}catch{}}module$exports$google3$video$youtube$web$frontend$bootstrap$identity$stickiness$stickiness.handlePostMessage=module$contents$google3$video$youtube$web$frontend$bootstrap$identity$stickiness$stickiness_handlePostMessage;window.addEventListener("message",module$contents$google3$video$youtube$web$frontend$bootstrap$identity$stickiness$stickiness_handlePostMessage);
</script></body></html>
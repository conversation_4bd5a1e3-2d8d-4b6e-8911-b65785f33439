gapi.loaded_0(function(_){var window=this;
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles=a||[]};(0,_._F_toggles_initialize)([]);
var da,ha,la,pa,ta,va,Da,Ea;da=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};ha=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
la=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};_.na=la(this);pa=function(a,b){if(b)a:{var c=_.na;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ha(c,a,{configurable:!0,writable:!0,value:b})}};
pa("Symbol",function(a){if(a)return a;var b=function(f,h){this.l2=f;ha(this,"description",{configurable:!0,writable:!0,value:h})};b.prototype.toString=function(){return this.l2};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
pa("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=_.na[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ha(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ta(da(this))}})}return a});ta=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
_.ua=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b};if(typeof Object.setPrototypeOf=="function")va=Object.setPrototypeOf;else{var wa;a:{var xa={a:!0},ya={};try{ya.__proto__=xa;wa=ya.a;break a}catch(a){}wa=!1}va=wa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}_.za=va;
_.Aa=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:da(a)};throw Error("b`"+String(a));};Da=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};Ea=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Da(d,e)&&(a[e]=d[e])}return a};pa("Object.assign",function(a){return a||Ea});
pa("globalThis",function(a){return a||_.na});pa("Reflect.setPrototypeOf",function(a){return a?a:_.za?function(b,c){try{return(0,_.za)(b,c),!0}catch(d){return!1}}:null});
pa("Promise",function(a){function b(){this.Af=null}function c(h){return h instanceof e?h:new e(function(k){k(h)})}if(a)return a;b.prototype.zP=function(h){if(this.Af==null){this.Af=[];var k=this;this.AP(function(){k.V8()})}this.Af.push(h)};var d=_.na.setTimeout;b.prototype.AP=function(h){d(h,0)};b.prototype.V8=function(){for(;this.Af&&this.Af.length;){var h=this.Af;this.Af=[];for(var k=0;k<h.length;++k){var l=h[k];h[k]=null;try{l()}catch(m){this.Yp(m)}}}this.Af=null};b.prototype.Yp=function(h){this.AP(function(){throw h;
})};var e=function(h){this.Ca=0;this.qf=void 0;this.Fr=[];this.aW=!1;var k=this.BF();try{h(k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.BF=function(){function h(m){return function(n){l||(l=!0,m.call(k,n))}}var k=this,l=!1;return{resolve:h(this.dfa),reject:h(this.qK)}};e.prototype.dfa=function(h){if(h===this)this.qK(new TypeError("A Promise cannot resolve to itself"));else if(h instanceof e)this.Iga(h);else{a:switch(typeof h){case "object":var k=h!=null;break a;case "function":k=!0;break a;
default:k=!1}k?this.cfa(h):this.XS(h)}};e.prototype.cfa=function(h){var k=void 0;try{k=h.then}catch(l){this.qK(l);return}typeof k=="function"?this.Jga(k,h):this.XS(h)};e.prototype.qK=function(h){this.Y_(2,h)};e.prototype.XS=function(h){this.Y_(1,h)};e.prototype.Y_=function(h,k){if(this.Ca!=0)throw Error("c`"+h+"`"+k+"`"+this.Ca);this.Ca=h;this.qf=k;this.Ca===2&&this.sfa();this.W8()};e.prototype.sfa=function(){var h=this;d(function(){if(h.pda()){var k=_.na.console;typeof k!=="undefined"&&k.error(h.qf)}},
1)};e.prototype.pda=function(){if(this.aW)return!1;var h=_.na.CustomEvent,k=_.na.Event,l=_.na.dispatchEvent;if(typeof l==="undefined")return!0;typeof h==="function"?h=new h("unhandledrejection",{cancelable:!0}):typeof k==="function"?h=new k("unhandledrejection",{cancelable:!0}):(h=_.na.document.createEvent("CustomEvent"),h.initCustomEvent("unhandledrejection",!1,!0,h));h.promise=this;h.reason=this.qf;return l(h)};e.prototype.W8=function(){if(this.Fr!=null){for(var h=0;h<this.Fr.length;++h)f.zP(this.Fr[h]);
this.Fr=null}};var f=new b;e.prototype.Iga=function(h){var k=this.BF();h.wy(k.resolve,k.reject)};e.prototype.Jga=function(h,k){var l=this.BF();try{h.call(k,l.resolve,l.reject)}catch(m){l.reject(m)}};e.prototype.then=function(h,k){function l(q,r){return typeof q=="function"?function(w){try{m(q(w))}catch(u){n(u)}}:r}var m,n,p=new e(function(q,r){m=q;n=r});this.wy(l(h,m),l(k,n));return p};e.prototype.catch=function(h){return this.then(void 0,h)};e.prototype.wy=function(h,k){function l(){switch(m.Ca){case 1:h(m.qf);
break;case 2:k(m.qf);break;default:throw Error("d`"+m.Ca);}}var m=this;this.Fr==null?f.zP(l):this.Fr.push(l);this.aW=!0};e.resolve=c;e.reject=function(h){return new e(function(k,l){l(h)})};e.race=function(h){return new e(function(k,l){for(var m=_.Aa(h),n=m.next();!n.done;n=m.next())c(n.value).wy(k,l)})};e.all=function(h){var k=_.Aa(h),l=k.next();return l.done?c([]):new e(function(m,n){function p(w){return function(u){q[w]=u;r--;r==0&&m(q)}}var q=[],r=0;do q.push(void 0),r++,c(l.value).wy(p(q.length-
1),n),l=k.next();while(!l.done)})};return e});var Ia=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
pa("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Ia(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var h=0;h<f&&c<e;)if(d[c++]!=b[h++])return!1;return h>=f}});pa("Object.setPrototypeOf",function(a){return a||_.za});pa("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});
pa("WeakMap",function(a){function b(){}function c(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function d(l){if(!Da(l,f)){var m=new b;ha(l,f,{value:m})}}function e(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),n=new a([[l,2],[m,3]]);if(n.get(l)!=2||n.get(m)!=3)return!1;n.delete(l);n.set(m,4);return!n.has(l)&&n.get(m)==4}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var h=0,k=function(l){this.Da=(h+=Math.random()+1).toString();if(l){l=_.Aa(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};k.prototype.set=function(l,m){if(!c(l))throw Error("e");d(l);if(!Da(l,f))throw Error("f`"+l);l[f][this.Da]=m;return this};k.prototype.get=function(l){return c(l)&&Da(l,f)?l[f][this.Da]:void 0};k.prototype.has=function(l){return c(l)&&Da(l,f)&&Da(l[f],this.Da)};k.prototype.delete=
function(l){return c(l)&&Da(l,f)&&Da(l[f],this.Da)?delete l[f][this.Da]:!1};return k});
pa("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var k=Object.seal({x:4}),l=new a(_.Aa([[k,"s"]]));if(l.get(k)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=k||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(k){this[0]={};this[1]=
f();this.size=0;if(k){k=_.Aa(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(k,l){k=k===0?0:k;var m=d(this,k);m.list||(m.list=this[0][m.id]=[]);m.Ve?m.Ve.value=l:(m.Ve={next:this[1],Lk:this[1].Lk,head:this[1],key:k,value:l},m.list.push(m.Ve),this[1].Lk.next=m.Ve,this[1].Lk=m.Ve,this.size++);return this};c.prototype.delete=function(k){k=d(this,k);return k.Ve&&k.list?(k.list.splice(k.index,1),k.list.length||delete this[0][k.id],k.Ve.Lk.next=k.Ve.next,k.Ve.next.Lk=
k.Ve.Lk,k.Ve.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].Lk=f();this.size=0};c.prototype.has=function(k){return!!d(this,k).Ve};c.prototype.get=function(k){return(k=d(this,k).Ve)&&k.value};c.prototype.entries=function(){return e(this,function(k){return[k.key,k.value]})};c.prototype.keys=function(){return e(this,function(k){return k.key})};c.prototype.values=function(){return e(this,function(k){return k.value})};c.prototype.forEach=function(k,l){for(var m=this.entries(),
n;!(n=m.next()).done;)n=n.value,k.call(l,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(k,l){var m=l&&typeof l;m=="object"||m=="function"?b.has(l)?m=b.get(l):(m=""+ ++h,b.set(l,m)):m="p_"+l;var n=k[0][m];if(n&&Da(k[0],m))for(k=0;k<n.length;k++){var p=n[k];if(l!==l&&p.key!==p.key||l===p.key)return{id:m,list:n,index:k,Ve:p}}return{id:m,list:n,index:-1,Ve:void 0}},e=function(k,l){var m=k[1];return ta(function(){if(m){for(;m.head!=k[1];)m=m.Lk;for(;m.next!=m.head;)return m=
m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})},f=function(){var k={};return k.Lk=k.next=k.head=k},h=0;return c});
pa("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(_.Aa([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(h){return!1}}())return a;var b=function(c){this.Sa=new Map;if(c){c=
_.Aa(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.Sa.size};b.prototype.add=function(c){c=c===0?0:c;this.Sa.set(c,c);this.size=this.Sa.size;return this};b.prototype.delete=function(c){c=this.Sa.delete(c);this.size=this.Sa.size;return c};b.prototype.clear=function(){this.Sa.clear();this.size=0};b.prototype.has=function(c){return this.Sa.has(c)};b.prototype.entries=function(){return this.Sa.entries()};b.prototype.values=function(){return this.Sa.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.Sa.forEach(function(f){return c.call(d,f,f,e)})};return b});var Ka=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};pa("Array.prototype.entries",function(a){return a?a:function(){return Ka(this,function(b,c){return[b,c]})}});
pa("Array.prototype.keys",function(a){return a?a:function(){return Ka(this,function(b){return b})}});pa("String.prototype.codePointAt",function(a){return a?a:function(b){var c=Ia(this,null,"codePointAt"),d=c.length;b=Number(b)||0;if(b>=0&&b<d){b|=0;var e=c.charCodeAt(b);if(e<55296||e>56319||b+1===d)return e;b=c.charCodeAt(b+1);return b<56320||b>57343?e:(e-55296)*1024+b+9216}}});
pa("String.fromCodePoint",function(a){return a?a:function(b){for(var c="",d=0;d<arguments.length;d++){var e=Number(arguments[d]);if(e<0||e>1114111||e!==Math.floor(e))throw new RangeError("invalid_code_point "+e);e<=65535?c+=String.fromCharCode(e):(e-=65536,c+=String.fromCharCode(e>>>10&1023|55296),c+=String.fromCharCode(e&1023|56320))}return c}});pa("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)Da(b,d)&&c.push([d,b[d]]);return c}});
pa("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=Ia(this,b,"endsWith");c===void 0&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;e>0&&c>0;)if(d[--c]!=b[--e])return!1;return e<=0}});pa("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});
var Ma=function(a,b,c){a instanceof String&&(a=String(a));for(var d=a.length,e=0;e<d;e++){var f=a[e];if(b.call(c,f,e,a))return{aV:e,SD:f}}return{aV:-1,SD:void 0}};pa("Array.prototype.find",function(a){return a?a:function(b,c){return Ma(this,b,c).SD}});pa("Array.prototype.values",function(a){return a?a:function(){return Ka(this,function(b,c){return c})}});
pa("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});pa("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
pa("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});pa("String.prototype.includes",function(a){return a?a:function(b,c){return Ia(this,b,"includes").indexOf(b,c||0)!==-1}});
pa("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(k){return k};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var h=0;!(f=b.next()).done;)e.push(c.call(d,f.value,h++))}else for(f=b.length,h=0;h<f;h++)e.push(c.call(d,b[h],h));return e}});pa("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)Da(b,d)&&c.push(b[d]);return c}});
pa("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});pa("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});pa("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});pa("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
pa("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});pa("Array.prototype.flatMap",function(a){return a?a:function(b,c){var d=[];Array.prototype.forEach.call(this,function(e,f){e=b.call(c,e,f,this);Array.isArray(e)?d.push.apply(d,e):d.push(e)});return d}});pa("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});
pa("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});pa("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});
pa("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});var Na=function(a){a=Math.trunc(a)||0;a<0&&(a+=this.length);if(!(a<0||a>=this.length))return this[a]};
pa("Array.prototype.at",function(a){return a?a:Na});var Pa=function(a){return a?a:Na};pa("Int8Array.prototype.at",Pa);pa("Uint8Array.prototype.at",Pa);pa("Uint8ClampedArray.prototype.at",Pa);pa("Int16Array.prototype.at",Pa);pa("Uint16Array.prototype.at",Pa);pa("Int32Array.prototype.at",Pa);pa("Uint32Array.prototype.at",Pa);pa("Float32Array.prototype.at",Pa);pa("Float64Array.prototype.at",Pa);pa("String.prototype.at",function(a){return a?a:Na});
pa("Array.prototype.findIndex",function(a){return a?a:function(b,c){return Ma(this,b,c).aV}});_.Ta={};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Va=_.Va||{};_.Xa=this||self;_.$a=_.Xa._F_toggles||[];_.ab="closure_uid_"+(Math.random()*1E9>>>0);_.bb=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.t=function(a,b){a=a.split(".");for(var c=_.Xa,d;a.length&&(d=a.shift());)a.length||b===void 0?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b};
_.eb=function(a,b){function c(){}c.prototype=b.prototype;a.N=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.mt=function(d,e,f){for(var h=Array(arguments.length-2),k=2;k<arguments.length;k++)h[k-2]=arguments[k];return b.prototype[e].apply(d,h)}};_.gb=window.osapi=window.osapi||{};
window.___jsl=window.___jsl||{};
(window.___jsl.cd=window.___jsl.cd||[]).push({gwidget:{parsetags:"explicit"},appsapi:{plus_one_service:"/plus/v1"},csi:{rate:.01},poshare:{hangoutContactPickerServer:"https://plus.google.com"},gappsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},appsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},
"oauth-flow":{authUrl:"https://accounts.google.com/o/oauth2/auth",proxyUrl:"https://accounts.google.com/o/oauth2/postmessageRelay",redirectUri:"postmessage"},iframes:{sharebox:{params:{json:"&"},url:":socialhost:/:session_prefix:_/sharebox/dialog"},plus:{url:":socialhost:/:session_prefix:_/widget/render/badge?usegapi=1"},":socialhost:":"https://apis.google.com",":im_socialhost:":"https://plus.googleapis.com",domains_suggest:{url:"https://domains.google.com/suggest/flow"},card:{params:{s:"#",userid:"&"},
url:":socialhost:/:session_prefix:_/hovercard/internalcard"},":signuphost:":"https://plus.google.com",":gplus_url:":"https://plus.google.com",plusone:{url:":socialhost:/:session_prefix:_/+1/fastbutton?usegapi=1"},plus_share:{url:":socialhost:/:session_prefix:_/+1/sharebutton?plusShare=true&usegapi=1"},plus_circle:{url:":socialhost:/:session_prefix:_/widget/plus/circle?usegapi=1"},plus_followers:{url:":socialhost:/_/im/_/widget/render/plus/followers?usegapi=1"},configurator:{url:":socialhost:/:session_prefix:_/plusbuttonconfigurator?usegapi=1"},
appcirclepicker:{url:":socialhost:/:session_prefix:_/widget/render/appcirclepicker"},page:{url:":socialhost:/:session_prefix:_/widget/render/page?usegapi=1"},person:{url:":socialhost:/:session_prefix:_/widget/render/person?usegapi=1"},community:{url:":ctx_socialhost:/:session_prefix::im_prefix:_/widget/render/community?usegapi=1"},follow:{url:":socialhost:/:session_prefix:_/widget/render/follow?usegapi=1"},commentcount:{url:":socialhost:/:session_prefix:_/widget/render/commentcount?usegapi=1"},comments:{url:":socialhost:/:session_prefix:_/widget/render/comments?usegapi=1"},
blogger:{url:":socialhost:/:session_prefix:_/widget/render/blogger?usegapi=1"},youtube:{url:":socialhost:/:session_prefix:_/widget/render/youtube?usegapi=1"},reportabuse:{url:":socialhost:/:session_prefix:_/widget/render/reportabuse?usegapi=1"},additnow:{url:":socialhost:/additnow/additnow.html"},appfinder:{url:"https://workspace.google.com/:session_prefix:marketplace/appfinder?usegapi=1"},":source:":"1p"},poclient:{update_session:"google.updateSessionCallback"},"googleapis.config":{rpc:"/rpc",root:"https://content.googleapis.com",
"root-1p":"https://clients6.google.com",useGapiForXd3:!0,xd3:"/static/proxy.html",auth:{useInterimAuth:!1}},report:{apis:["iframes\\..*","gadgets\\..*","gapi\\.appcirclepicker\\..*","gapi\\.client\\..*"],rate:1E-4},client:{perApiBatch:!0},gen204logger:{interval:3E4,rate:.001,batch:!1}});
var ob;_.jb=function(a){return function(){return _.hb[a].apply(this,arguments)}};_.lb=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.lb);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.rZ=!0};ob=function(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");_.lb.call(this,c+a[d])};_.hb=[];_.eb(_.lb,Error);_.lb.prototype.name="CustomError";_.eb(ob,_.lb);ob.prototype.name="AssertionError";
var xb,yb,zb;_.pb=function(a,b){return _.hb[a]=b};_.rb=function(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};_.tb=function(a,b){return(0,_.sb)(a,b)>=0};_.ub=function(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};_.vb=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"};
_.y=function(a,b){a.prototype=(0,_.ua)(b.prototype);a.prototype.constructor=a;if(_.za)(0,_.za)(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.N=b.prototype};_.wb=function(a,b){a=a.split(".");b=b||_.Xa;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b};xb=function(a){var b=_.wb("WIZ_global_data.oxN3nb");a=b&&b[a];return a!=null?a:!1};
yb=function(a,b,c){return a.call.apply(a.bind,arguments)};zb=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};_.z=function(a,b,c){_.z=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?yb:zb;return _.z.apply(null,arguments)};_.sb=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};
_.Ab=Array.prototype.lastIndexOf?function(a,b){return Array.prototype.lastIndexOf.call(a,b,a.length-1)}:function(a,b){var c=a.length-1;c<0&&(c=Math.max(0,a.length+c));if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.lastIndexOf(b,c);for(;c>=0;c--)if(c in a&&a[c]===b)return c;return-1};_.Bb=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
_.Gb=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f=typeof a==="string"?a.split(""):a,h=0;h<c;h++)if(h in f){var k=f[h];b.call(void 0,k,h,a)&&(d[e++]=k)}return d};_.Ib=Array.prototype.map?function(a,b,c){return Array.prototype.map.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=Array(d),f=typeof a==="string"?a.split(""):a,h=0;h<d;h++)h in f&&(e[h]=b.call(c,f[h],h,a));return e};
_.Jb=Array.prototype.some?function(a,b,c){return Array.prototype.some.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return!0;return!1};_.Nb=Array.prototype.every?function(a,b,c){return Array.prototype.every.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&!b.call(c,e[f],f,a))return!1;return!0};var Ob=!!(_.$a[0]&4096),Pb=!!(_.$a[0]&8192),Qb=!!(_.$a[0]&16),Rb=!!(_.$a[0]>>15&1);_.Sb=Ob?Pb:xb(610401301);_.Ub=Ob?Qb:xb(**********);_.Vb=Ob?Rb:xb(651175828);_.Wb=function(a){_.Wb[" "](a);return a};_.Wb[" "]=function(){};
/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var ac,cc,pc,Ac,Lc,Zc,id;_.Yb=function(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};_.Zb=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};ac=function(){var a=null;if(!$b)return a;try{var b=function(c){return c};a=$b.createPolicy("gapi#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};cc=function(){bc===void 0&&(bc=ac());return bc};_.ec=function(a){var b=cc();a=b?b.createHTML(a):a;return new _.dc(a)};
_.fc=function(a){if(a instanceof _.dc)return a.NY;throw Error("j");};_.hc=function(a){return new _.gc(a)};_.jc=function(a){var b=cc();a=b?b.createScriptURL(a):a;return new _.ic(a)};_.kc=function(a){if(a instanceof _.ic)return a.OY;throw Error("j");};_.mc=function(a){return a instanceof _.lc};_.nc=function(a){if(_.mc(a))return a.QY;throw Error("j");};pc=function(a){return new _.oc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})};_.rc=function(a){if(qc.test(a))return a};
_.sc=function(a){return a instanceof _.lc?_.nc(a):_.rc(a)};_.tc=function(a,b){if(a instanceof _.dc)return a;a=String(a).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;");if(b==null?0:b.msa)a=a.replace(/(^|[\r\n\t ]) /g,"$1&#160;");if(b==null?0:b.rea)a=a.replace(/(\r\n|\n|\r)/g,"<br>");if(b==null?0:b.nsa)a=a.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>');return _.ec(a)};
_.vc=function(a){var b=_.uc.apply(1,arguments);if(b.length===0)return _.jc(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return _.jc(c)};_.wc=function(a,b){return a.lastIndexOf(b,0)==0};_.xc=function(a){return/^[\s\xa0]*$/.test(a)};_.yc=function(a,b){return a.indexOf(b)!=-1};
_.Dc=function(a,b){var c=0;a=(0,_.zc)(String(a)).split(".");b=(0,_.zc)(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",h=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(f[0].length==0&&h[0].length==0)break;c=Ac(f[1].length==0?0:parseInt(f[1],10),h[1].length==0?0:parseInt(h[1],10))||Ac(f[2].length==0,h[2].length==0)||Ac(f[2],h[2]);f=f[3];h=h[3]}while(c==0)}return c};
Ac=function(a,b){return a<b?-1:a>b?1:0};_.Ec=function(a,b){b=_.sc(b);b!==void 0&&(a.href=b)};_.Fc=function(a,b,c,d){b=_.sc(b);return b!==void 0?a.open(b,c,d):null};_.Gc=function(a,b){b=b===void 0?document:b;var c,d;b=(d=(c=b).querySelector)==null?void 0:d.call(c,a+"[nonce]");return b==null?"":b.nonce||b.getAttribute("nonce")||""};_.Hc=function(a,b){if(a.nodeType===1&&/^(script|style)$/i.test(a.tagName))throw Error("j");a.innerHTML=_.fc(b)};
_.Jc=function(){var a=_.Xa.navigator;return a&&(a=a.userAgent)?a:""};Lc=function(a){if(!_.Sb||!_.Kc)return!1;for(var b=0;b<_.Kc.brands.length;b++){var c=_.Kc.brands[b].brand;if(c&&_.yc(c,a))return!0}return!1};_.Mc=function(a){return _.yc(_.Jc(),a)};_.Nc=function(a){for(var b=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g"),c=[],d;d=b.exec(a);)c.push([d[1],d[2],d[3]||void 0]);return c};_.Oc=function(){return _.Sb?!!_.Kc&&_.Kc.brands.length>0:!1};_.Pc=function(){return _.Oc()?!1:_.Mc("Opera")};
_.Qc=function(){return _.Oc()?!1:_.Mc("Trident")||_.Mc("MSIE")};_.Sc=function(){return _.Oc()?!1:_.Mc("Edge")};_.Tc=function(){return _.Oc()?Lc("Microsoft Edge"):_.Mc("Edg/")};_.Uc=function(){return _.Oc()?Lc("Opera"):_.Mc("OPR")};_.Vc=function(){return _.Mc("Firefox")||_.Mc("FxiOS")};_.Wc=function(){return _.Oc()?Lc("Chromium"):(_.Mc("Chrome")||_.Mc("CriOS"))&&!_.Sc()||_.Mc("Silk")};
_.Xc=function(a){var b={};a.forEach(function(c){b[c[0]]=c[1]});return function(c){return b[c.find(function(d){return d in b})]||""}};_.Yc=function(a){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])return b[1];b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];return b};
Zc=function(){return _.Sb?!!_.Kc&&!!_.Kc.platform:!1};_.$c=function(){return Zc()?_.Kc.platform==="Android":_.Mc("Android")};_.ad=function(){return _.Mc("iPhone")&&!_.Mc("iPod")&&!_.Mc("iPad")};_.bd=function(){return _.ad()||_.Mc("iPad")||_.Mc("iPod")};_.cd=function(){return Zc()?_.Kc.platform==="macOS":_.Mc("Macintosh")};_.dd=function(){return Zc()?_.Kc.platform==="Windows":_.Mc("Windows")};_.ed=function(){return Zc()?_.Kc.platform==="Chrome OS":_.Mc("CrOS")};
_.fd=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a};_.gd=function(a){return _.fd(a,a)};_.uc=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};_.jd=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"};_.kd=function(a){var b=_.jd(a);return b=="array"||b=="object"&&typeof a.length=="number"};_.ld=function(){return Date.now()};var md=globalThis.trustedTypes,$b=md,bc;_.dc=function(a){this.NY=a};_.dc.prototype.toString=function(){return this.NY+""};_.nd=function(){return new _.dc(md?md.emptyHTML:"")}();_.gc=function(a){this.PY=a};_.gc.prototype.toString=function(){return this.PY};_.ic=function(a){this.OY=a};_.ic.prototype.toString=function(){return this.OY+""};_.lc=function(a){this.QY=a};_.lc.prototype.toString=function(){return this.QY};_.od=new _.lc("about:invalid#zClosurez");var qc;_.oc=function(a){this.xj=a};_.pd=[pc("data"),pc("http"),pc("https"),pc("mailto"),pc("ftp"),new _.oc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];_.qd=function(){return typeof URL==="function"}();qc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;_.rd=function(a,b){this.width=a;this.height=b};_.sd=function(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1};_.g=_.rd.prototype;_.g.clone=function(){return new _.rd(this.width,this.height)};_.g.by=function(){return this.width*this.height};_.g.aspectRatio=function(){return this.width/this.height};_.g.isEmpty=function(){return!this.by()};_.g.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};
_.g.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};_.g.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};_.zc=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};_.td=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};_.ud=Math.random()*2147483648|0;var vd;vd=_.Xa.navigator;_.Kc=vd?vd.userAgentData||null:null;var Nd,Od,Wd;_.xd=_.Pc();_.yd=_.Qc();_.zd=_.Mc("Edge");_.Ad=_.zd||_.yd;_.Bd=_.Mc("Gecko")&&!(_.yc(_.Jc().toLowerCase(),"webkit")&&!_.Mc("Edge"))&&!(_.Mc("Trident")||_.Mc("MSIE"))&&!_.Mc("Edge");_.Cd=_.yc(_.Jc().toLowerCase(),"webkit")&&!_.Mc("Edge");_.Dd=_.Cd&&_.Mc("Mobile");_.Ed=_.cd();_.Fd=_.dd();_.Gd=(Zc()?_.Kc.platform==="Linux":_.Mc("Linux"))||_.ed();_.Id=_.$c();_.Jd=_.ad();_.Kd=_.Mc("iPad");_.Ld=_.Mc("iPod");_.Md=_.bd();Nd=function(){var a=_.Xa.document;return a?a.documentMode:void 0};
a:{var Pd="",Td=function(){var a=_.Jc();if(_.Bd)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.zd)return/Edge\/([\d\.]+)/.exec(a);if(_.yd)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.Cd)return/WebKit\/(\S+)/.exec(a);if(_.xd)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Td&&(Pd=Td?Td[1]:"");if(_.yd){var Ud=Nd();if(Ud!=null&&Ud>parseFloat(Pd)){Od=String(Ud);break a}}Od=Pd}_.Vd=Od;if(_.Xa.document&&_.yd){var Xd=Nd();Wd=Xd?Xd:parseInt(_.Vd,10)||void 0}else Wd=void 0;_.Yd=Wd;var de,ke,je;_.ae=function(a){return a?new _.Zd(_.$d(a)):id||(id=new _.Zd)};_.be=function(a,b){return typeof b==="string"?a.getElementById(b):b};_.ce=function(a,b,c,d){a=d||a;return(b=b&&b!="*"?String(b).toUpperCase():"")||c?a.querySelectorAll(b+(c?"."+c:"")):a.getElementsByTagName("*")};
_.ee=function(a,b){_.Zb(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:de.hasOwnProperty(d)?a.setAttribute(de[d],c):_.wc(d,"aria-")||_.wc(d,"data-")?a.setAttribute(d,c):a[d]=c})};de={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};_.ge=function(a){return _.fe(a||window)};
_.fe=function(a){a=a.document;a=_.he(a)?a.documentElement:a.body;return new _.rd(a.clientWidth,a.clientHeight)};_.ie=function(a){return a?a.defaultView:window};_.le=function(a,b){var c=b[1],d=je(a,String(b[0]));c&&(typeof c==="string"?d.className=c:Array.isArray(c)?d.className=c.join(" "):_.ee(d,c));b.length>2&&ke(a,d,b,2);return d};
ke=function(a,b,c,d){function e(k){k&&b.appendChild(typeof k==="string"?a.createTextNode(k):k)}for(;d<c.length;d++){var f=c[d];if(!_.kd(f)||_.vb(f)&&f.nodeType>0)e(f);else{a:{if(f&&typeof f.length=="number"){if(_.vb(f)){var h=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){h=typeof f.item=="function";break a}}h=!1}_.Bb(h?_.Yb(f):f,e)}}};_.me=function(a){return je(document,a)};
je=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};_.he=function(a){return a.compatMode=="CSS1Compat"};_.ne=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.oe=function(a,b){ke(_.$d(a),a,arguments,1)};_.pe=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.qe=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b)};_.re=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.se=function(a){return a.children!=void 0?a.children:Array.prototype.filter.call(a.childNodes,function(b){return b.nodeType==1})};_.te=function(a){return _.vb(a)&&a.nodeType==1};
_.ue=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.$d=function(a){return a.nodeType==9?a:a.ownerDocument||a.document};
_.ve=function(a,b){if("textContent"in a)a.textContent=b;else if(a.nodeType==3)a.data=String(b);else if(a.firstChild&&a.firstChild.nodeType==3){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.pe(a),a.appendChild(_.$d(a).createTextNode(String(b)))};_.Zd=function(a){this.Bc=a||_.Xa.document||document};_.g=_.Zd.prototype;_.g.Ha=_.ae;_.g.uL=_.jb(0);_.g.ub=function(){return this.Bc};_.g.O=_.jb(1);_.g.getElementsByTagName=function(a,b){return(b||this.Bc).getElementsByTagName(String(a))};
_.g.uH=_.jb(2);_.g.wa=function(a,b,c){return _.le(this.Bc,arguments)};_.g.createElement=function(a){return je(this.Bc,a)};_.g.createTextNode=function(a){return this.Bc.createTextNode(String(a))};_.g.getWindow=function(){return this.Bc.defaultView};_.g.appendChild=function(a,b){a.appendChild(b)};_.g.append=_.oe;_.g.canHaveChildren=_.ne;_.g.ne=_.pe;_.g.xV=_.qe;_.g.removeNode=_.re;_.g.EG=_.se;_.g.isElement=_.te;_.g.contains=_.ue;_.g.XG=_.$d;_.g.vj=_.jb(3);
/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
_.we=function(a){return a===null?"null":a===void 0?"undefined":a};_.xe=window;_.ye=document;_.ze=_.xe.location;_.Ae=/\[native code\]/;_.Be=function(a,b,c){return a[b]=a[b]||c};_.Ce=function(){var a;if((a=Object.create)&&_.Ae.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a};_.De=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};_.Ee=function(a,b){a=a||{};for(var c in a)_.De(a,c)&&(b[c]=a[c])};_.Fe=_.Be(_.xe,"gapi",{});_.Ge=function(a,b,c){var d=new RegExp("([#].*&|[#])"+b+"=([^&#]*)","g");b=new RegExp("([?#].*&|[?#])"+b+"=([^&#]*)","g");if(a=a&&(d.exec(a)||b.exec(a)))try{c=decodeURIComponent(a[2])}catch(e){}return c};_.He=new RegExp(/^/.source+/([a-zA-Z][-+.a-zA-Z0-9]*:)?/.source+/(\/\/[^\/?#]*)?/.source+/([^?#]*)?/.source+/(\?([^#]*))?/.source+/(#((#|[^#])*))?/.source+/$/.source);_.Ie=new RegExp(/(%([^0-9a-fA-F%]|[0-9a-fA-F]([^0-9a-fA-F%])?)?)*/.source+/%($|[^0-9a-fA-F]|[0-9a-fA-F]($|[^0-9a-fA-F]))/.source,"g");
_.Je=new RegExp(/\/?\??#?/.source+"("+/[\/?#]/i.source+"|"+/[\uD800-\uDBFF]/i.source+"|"+/%[c-f][0-9a-f](%[89ab][0-9a-f]){0,2}(%[89ab]?)?/i.source+"|"+/%[0-9a-f]?/i.source+")$","i");_.Le=function(a,b,c){_.Ke(a,b,c,"add","at")};_.Ke=function(a,b,c,d,e){if(a[d+"EventListener"])a[d+"EventListener"](b,c,!1);else if(a[e+"tachEvent"])a[e+"tachEvent"]("on"+b,c)};_.Me={};_.Me=_.Be(_.xe,"___jsl",_.Ce());_.Be(_.Me,"I",0);_.Be(_.Me,"hel",10);var Ne,Pe,Qe,Re,Ue,Se,Te,Ve,We;Ne=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Pe=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};Qe=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Re=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Qe(a[d])&&!Qe(b[d])?Re(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Qe(b[d])?[]:{},Re(a[d],b[d])):a[d]=b[d])};
Ue=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Ne("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Se())if(e=Te(c),d.push(25),typeof e===
"object")return e;return{}}};Se=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Te=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};Ve=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Re(c,b);a.push(c)};
We=function(a){Pe(!0);var b=window.___gcfg,c=Ne("cu"),d=window.___gu;b&&b!==d&&(Ve(c,b),window.___gu=b);b=Ne("cu");var e=document.getElementsByTagName("script")||[];d=[];var f=[];f.push.apply(f,Ne("us"));for(var h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&d.push(k);d.length==0&&e.length>0&&e[e.length-1].src&&d.push(e[e.length-1]);for(e=0;e<d.length;++e)d[e].getAttribute("gapi_processed")||(d[e].setAttribute("gapi_processed",!0),(f=d[e])?(h=f.nodeType,f=h==3||
h==4?f.nodeValue:f.textContent||""):f=void 0,h=d[e].nonce||d[e].getAttribute("nonce"),(f=Ue(f,h))&&b.push(f));a&&Ve(c,a);d=Ne("cd");a=0;for(b=d.length;a<b;++a)Re(Pe(),d[a],!0);d=Ne("ci");a=0;for(b=d.length;a<b;++a)Re(Pe(),d[a],!0);a=0;for(b=c.length;a<b;++a)Re(Pe(),c[a],!0)};_.Xe=function(a,b){var c=Pe();if(!a)return c;a=a.split("/");for(var d=0,e=a.length;c&&typeof c==="object"&&d<e;++d)c=c[a[d]];return d===a.length&&c!==void 0?c:b};
_.Ye=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;We(c)};var Ze=function(){var a=window.__GOOGLEAPIS;a&&(a.googleapis&&!a["googleapis.config"]&&(a["googleapis.config"]=a.googleapis),_.Be(_.Me,"ci",[]).push(a),window.__GOOGLEAPIS=void 0)};Ze&&Ze();We();_.t("gapi.config.get",_.Xe);_.t("gapi.config.update",_.Ye);
_.$e=function(a){a=_.we(a);return _.ec(a)};
_.Fg=(window.gapi||{}).load;
_.lo=_.Be(_.Me,"rw",_.Ce());
var mo=function(a,b){(a=_.lo[a])&&a.state<b&&(a.state=b)};var no=function(a){a=(a=_.lo[a])?a.oid:void 0;if(a){var b=_.ye.getElementById(a);b&&b.parentNode.removeChild(b);delete _.lo[a];no(a)}};_.oo=function(a){a=a.container;typeof a==="string"&&(a=document.getElementById(a));return a};_.po=function(a){var b=a.clientWidth;return"position:absolute;top:-10000px;width:"+(b?b+"px":a.style.width||"300px")+";margin:0px;border-style:none;"};
_.qo=function(a,b){var c={},d=a.wc(),e=b&&b.width,f=b&&b.height,h=b&&b.verticalAlign;h&&(c.verticalAlign=h);e||(e=d.width||a.width);f||(f=d.height||a.height);d.width=c.width=e;d.height=c.height=f;d=a.getIframeEl();e=a.getId();mo(e,2);a:{e=a.getSiteEl();c=c||{};var k;if(_.Me.oa&&(k=d.id)){f=(f=_.lo[k])?f.state:void 0;if(f===1||f===4)break a;no(k)}(f=e.nextSibling)&&f.dataset&&f.dataset.gapistub&&(e.parentNode.removeChild(f),e.style.cssText="");f=c.width;h=c.height;var l=e.style;l.textIndent="0";l.margin=
"0";l.padding="0";l.background="transparent";l.borderStyle="none";l.cssFloat="none";l.styleFloat="none";l.lineHeight="normal";l.fontSize="1px";l.verticalAlign="baseline";e=e.style;e.display="inline-block";d=d.style;d.position="static";d.left="0";d.top="0";d.visibility="visible";f&&(e.width=d.width=f+"px");h&&(e.height=d.height=h+"px");c.verticalAlign&&(e.verticalAlign=c.verticalAlign);k&&mo(k,3)}(k=b?b.title:null)&&a.getIframeEl().setAttribute("title",k);(b=b?b.ariaLabel:null)&&a.getIframeEl().setAttribute("aria-label",
b)};_.ro=function(a){var b=a.getSiteEl();b&&b.removeChild(a.getIframeEl())};_.so=function(a){a.where=_.oo(a);var b=a.messageHandlers=a.messageHandlers||{},c=function(e){_.qo(this,e)};b._ready=c;b._renderstart=c;var d=a.onClose;a.onClose=function(e){d&&d.call(this,e);_.ro(this)};a.onCreate=function(e){e=e.getIframeEl();e.style.cssText=_.po(e)}};
_.ef=function(){var a=window.gadgets&&window.gadgets.config&&window.gadgets.config.get;a&&_.Ye(a());return{register:function(b,c,d){d&&d(_.Xe())},get:function(b){return _.Xe(b)},update:function(b,c){if(c)throw"Config replacement is not supported";_.Ye(b)},init:function(){}}}();_.t("gadgets.config.register",_.ef.register);_.t("gadgets.config.get",_.ef.get);_.t("gadgets.config.init",_.ef.init);_.t("gadgets.config.update",_.ef.update);
var ff,gf,hf,jf,lf,nf,of,pf,qf,rf,sf,tf,uf,vf,wf,xf,yf,zf,Af,Bf,Df,Gf,Hf,If,Jf,Kf,Lf,Mf,Nf,Of,Pf,Sf,Tf;hf=void 0;jf=function(a){try{return _.Xa.JSON.parse.call(_.Xa.JSON,a)}catch(b){return!1}};lf=function(a){return Object.prototype.toString.call(a)};nf=lf(0);of=lf(new Date(0));pf=lf(!0);qf=lf("");rf=lf({});sf=lf([]);
tf=function(a,b){if(b)for(var c=0,d=b.length;c<d;++c)if(a===b[c])throw new TypeError("Converting circular structure to JSON");d=typeof a;if(d!=="undefined"){c=Array.prototype.slice.call(b||[],0);c[c.length]=a;b=[];var e=lf(a);if(a!=null&&typeof a.toJSON==="function"&&(Object.prototype.hasOwnProperty.call(a,"toJSON")||(e!==sf||a.constructor!==Array&&a.constructor!==Object)&&(e!==rf||a.constructor!==Array&&a.constructor!==Object)&&e!==qf&&e!==nf&&e!==pf&&e!==of))return tf(a.toJSON.call(a),c);if(a==
null)b[b.length]="null";else if(e===nf)a=Number(a),isNaN(a)||isNaN(a-a)?a="null":a===-0&&1/a<0&&(a="-0"),b[b.length]=String(a);else if(e===pf)b[b.length]=String(!!Number(a));else{if(e===of)return tf(a.toISOString.call(a),c);if(e===sf&&lf(a.length)===nf){b[b.length]="[";var f=0;for(d=Number(a.length)>>0;f<d;++f)f&&(b[b.length]=","),b[b.length]=tf(a[f],c)||"null";b[b.length]="]"}else if(e==qf&&lf(a.length)===nf){b[b.length]='"';f=0;for(c=Number(a.length)>>0;f<c;++f)d=String.prototype.charAt.call(a,
f),e=String.prototype.charCodeAt.call(a,f),b[b.length]=d==="\b"?"\\b":d==="\f"?"\\f":d==="\n"?"\\n":d==="\r"?"\\r":d==="\t"?"\\t":d==="\\"||d==='"'?"\\"+d:e<=31?"\\u"+(e+65536).toString(16).substr(1):e>=32&&e<=65535?d:"\ufffd";b[b.length]='"'}else if(d==="object"){b[b.length]="{";d=0;for(f in a)Object.prototype.hasOwnProperty.call(a,f)&&(e=tf(a[f],c),e!==void 0&&(d++&&(b[b.length]=","),b[b.length]=tf(f),b[b.length]=":",b[b.length]=e));b[b.length]="}"}else return}return b.join("")}};uf=/[\0-\x07\x0b\x0e-\x1f]/;
vf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*[\0-\x1f]/;wf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\[^\\\/"bfnrtu]/;xf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\u([0-9a-fA-F]{0,3}[^0-9a-fA-F])/;yf=/"([^\0-\x1f\\"]|\\[\\\/"bfnrt]|\\u[0-9a-fA-F]{4})*"/g;zf=/-?(0|[1-9][0-9]*)(\.[0-9]+)?([eE][-+]?[0-9]+)?/g;Af=/[ \t\n\r]+/g;Bf=/[^"]:/;Df=/""/g;Gf=/true|false|null/g;Hf=/00/;If=/[\{]([^0\}]|0[^:])/;Jf=/(^|\[)[,:]|[,:](\]|\}|[,:]|$)/;Kf=/[^\[,:][\[\{]/;Lf=/^(\{|\}|\[|\]|,|:|0)+/;Mf=/\u2028/g;
Nf=/\u2029/g;
Of=function(a){a=String(a);if(uf.test(a)||vf.test(a)||wf.test(a)||xf.test(a))return!1;var b=a.replace(yf,'""');b=b.replace(zf,"0");b=b.replace(Af,"");if(Bf.test(b))return!1;b=b.replace(Df,"0");b=b.replace(Gf,"0");if(Hf.test(b)||If.test(b)||Jf.test(b)||Kf.test(b)||!b||(b=b.replace(Lf,"")))return!1;a=a.replace(Mf,"\\u2028").replace(Nf,"\\u2029");b=void 0;try{b=hf?[jf(a)]:eval("(function (var_args) {\n  return Array.prototype.slice.call(arguments, 0);\n})(\n"+a+"\n)")}catch(c){return!1}return b&&b.length===
1?b[0]:!1};Pf=function(){var a=((_.Xa.document||{}).scripts||[]).length;if((ff===void 0||hf===void 0||gf!==a)&&gf!==-1){ff=hf=!1;gf=-1;try{try{hf=!!_.Xa.JSON&&_.Xa.JSON.stringify.call(_.Xa.JSON,{a:[3,!0,new Date(0)],c:function(){}})==='{"a":[3,true,"1970-01-01T00:00:00.000Z"]}'&&jf("true")===!0&&jf('[{"a":3}]')[0].a===3}catch(b){}ff=hf&&!jf("[00]")&&!jf('"\u0007"')&&!jf('"\\0"')&&!jf('"\\v"')}finally{gf=a}}};_.Qf=function(a){if(gf===-1)return!1;Pf();return(ff?jf:Of)(a)};
_.Rf=function(a){if(gf!==-1)return Pf(),hf?_.Xa.JSON.stringify.call(_.Xa.JSON,a):tf(a)};Sf=!Date.prototype.toISOString||typeof Date.prototype.toISOString!=="function"||(new Date(0)).toISOString()!=="1970-01-01T00:00:00.000Z";
Tf=function(){var a=Date.prototype.getUTCFullYear.call(this);return[a<0?"-"+String(1E6-a).substr(1):a<=9999?String(1E4+a).substr(1):"+"+String(1E6+a).substr(1),"-",String(101+Date.prototype.getUTCMonth.call(this)).substr(1),"-",String(100+Date.prototype.getUTCDate.call(this)).substr(1),"T",String(100+Date.prototype.getUTCHours.call(this)).substr(1),":",String(100+Date.prototype.getUTCMinutes.call(this)).substr(1),":",String(100+Date.prototype.getUTCSeconds.call(this)).substr(1),".",String(1E3+Date.prototype.getUTCMilliseconds.call(this)).substr(1),
"Z"].join("")};Date.prototype.toISOString=Sf?Tf:Date.prototype.toISOString;
_.t("gadgets.json.stringify",_.Rf);_.t("gadgets.json.parse",_.Qf);
(function(){function a(e,f){if(!(e<c)&&d)if(e===2&&d.warn)d.warn(f);else if(e===3&&d.error)try{d.error(f)}catch(h){}else d.log&&d.log(f)}var b=function(e){a(1,e)};_.bf=function(e){a(2,e)};_.cf=function(e){a(3,e)};_.df=function(){};b.INFO=1;b.WARNING=2;b.NONE=4;var c=1,d=window.console?window.console:window.opera?window.opera.postError:void 0;return b})();
_.af=_.af||{};
_.af=_.af||{};(function(){var a=[];_.af.rsa=function(b){a.push(b)};_.af.Fsa=function(){for(var b=0,c=a.length;b<c;++b)a[b]()}})();
_.af=_.af||{};
(function(){function a(c){var d=typeof c==="undefined";if(b!==null&&d)return b;var e={};c=c||window.location.href;var f=c.indexOf("?"),h=c.indexOf("#");c=(h===-1?c.substr(f+1):[c.substr(f+1,h-f-1),"&",c.substr(h+1)].join("")).split("&");f=window.decodeURIComponent?decodeURIComponent:unescape;h=0;for(var k=c.length;h<k;++h){var l=c[h].indexOf("=");if(l!==-1){var m=c[h].substring(0,l);l=c[h].substring(l+1);l=l.replace(/\+/g," ");try{e[m]=f(l)}catch(n){}}}d&&(b=e);return e}var b=null;_.af.Rg=a;a()})();_.t("gadgets.util.getUrlParameters",_.af.Rg);
var Uf=function(){this.Eg=window.console};Uf.prototype.log=function(a){this.Eg&&this.Eg.log&&this.Eg.log(a)};Uf.prototype.error=function(a){this.Eg&&(this.Eg.error?this.Eg.error(a):this.Eg.log&&this.Eg.log(a))};Uf.prototype.warn=function(a){this.Eg&&(this.Eg.warn?this.Eg.warn(a):this.Eg.log&&this.Eg.log(a))};Uf.prototype.debug=function(){};_.Vf=new Uf;
_.Wf=function(){var a=_.ye.readyState;return a==="complete"||a==="interactive"&&navigator.userAgent.indexOf("MSIE")==-1};_.Xf=function(a){if(_.Wf())a();else{var b=!1,c=function(){if(!b)return b=!0,a.apply(this,arguments)};_.xe.addEventListener?(_.xe.addEventListener("load",c,!1),_.xe.addEventListener("DOMContentLoaded",c,!1)):_.xe.attachEvent&&(_.xe.attachEvent("onreadystatechange",function(){_.Wf()&&c.apply(this,arguments)}),_.xe.attachEvent("onload",c))}};
_.Yf=function(a,b){var c=_.Be(_.Me,"watt",_.Ce());_.Be(c,a,b)};_.Ge(_.xe.location.href,"rpctoken")&&_.Le(_.ye,"unload",function(){});var Zf=Zf||{};Zf.HZ=null;Zf.rX=null;Zf.FA=null;Zf.frameElement=null;Zf=Zf||{};
Zf.TN||(Zf.TN=function(){function a(f,h,k){typeof window.addEventListener!="undefined"?window.addEventListener(f,h,k):typeof window.attachEvent!="undefined"&&window.attachEvent("on"+f,h);f==="message"&&(window.___jsl=window.___jsl||{},f=window.___jsl,f.RPMQ=f.RPMQ||[],f.RPMQ.push(h))}function b(f){var h=_.Qf(f.data);if(h&&h.f){_.df();var k=_.$f.co(h.f);e&&(typeof f.origin!=="undefined"?f.origin!==k:f.domain!==/^.+:\/\/([^:]+).*/.exec(k)[1])?_.cf("Invalid rpc message origin. "+k+" vs "+(f.origin||"")):
c(h,f.origin)}}var c,d,e=!0;return{nT:function(){return"wpm"},nca:function(){return!0},init:function(f,h){_.ef.register("rpc",null,function(k){String((k&&k.rpc||{}).disableForceSecure)==="true"&&(e=!1)});c=f;d=h;a("message",b,!1);d("..",!0);return!0},Ib:function(f){d(f,!0);return!0},call:function(f,h,k){var l=_.$f.co(f),m=_.$f.MO(f);l?window.setTimeout(function(){var n=_.Rf(k);_.df();m&&"postMessage"in m&&m.postMessage(n,l)},0):f!=".."&&_.cf("No relay set (used as window.postMessage targetOrigin), cannot send cross-domain message");
return!0}}}());if(window.gadgets&&window.gadgets.rpc)typeof _.$f!="undefined"&&_.$f||(_.$f=window.gadgets.rpc,_.$f.config=_.$f.config,_.$f.register=_.$f.register,_.$f.unregister=_.$f.unregister,_.$f.jZ=_.$f.registerDefault,_.$f.A1=_.$f.unregisterDefault,_.$f.SS=_.$f.forceParentVerifiable,_.$f.call=_.$f.call,_.$f.Gu=_.$f.getRelayUrl,_.$f.Oj=_.$f.setRelayUrl,_.$f.OC=_.$f.setAuthToken,_.$f.Iw=_.$f.setupReceiver,_.$f.Pn=_.$f.getAuthToken,_.$f.vK=_.$f.removeReceiver,_.$f.NT=_.$f.getRelayChannel,_.$f.eZ=_.$f.receive,
_.$f.fZ=_.$f.receiveSameDomain,_.$f.getOrigin=_.$f.getOrigin,_.$f.co=_.$f.getTargetOrigin,_.$f.MO=_.$f._getTargetWin,_.$f.N6=_.$f._parseSiblingId);else{_.$f=function(){function a(I,ka){if(!T[I]){var ma=cb;ka||(ma=Oa);T[I]=ma;ka=K[I]||[];for(var Fa=0;Fa<ka.length;++Fa){var U=ka[Fa];U.t=E[I];ma.call(I,U.f,U)}K[I]=[]}}function b(){function I(){Mb=!0}Hb||(typeof window.addEventListener!="undefined"?window.addEventListener("unload",I,!1):typeof window.attachEvent!="undefined"&&window.attachEvent("onunload",
I),Hb=!0)}function c(I,ka,ma,Fa,U){E[ka]&&E[ka]===ma||(_.cf("Invalid gadgets.rpc token. "+E[ka]+" vs "+ma),qb(ka,2));U.onunload=function(){R[ka]&&!Mb&&(qb(ka,1),_.$f.vK(ka))};b();Fa=_.Qf(decodeURIComponent(Fa))}function d(I,ka){if(I&&typeof I.s==="string"&&typeof I.f==="string"&&I.a instanceof Array)if(E[I.f]&&E[I.f]!==I.t&&(_.cf("Invalid gadgets.rpc token. "+E[I.f]+" vs "+I.t),qb(I.f,2)),I.s==="__ack")window.setTimeout(function(){a(I.f,!0)},0);else{I.c&&(I.callback=function(Ga){_.$f.call(I.f,(I.g?
"legacy__":"")+"__cb",null,I.c,Ga)});if(ka){var ma=e(ka);I.origin=ka;var Fa=I.r;try{var U=e(Fa)}catch(Ga){}Fa&&U==ma||(Fa=ka);I.referer=Fa}ka=(x[I.s]||x[""]).apply(I,I.a);I.c&&typeof ka!=="undefined"&&_.$f.call(I.f,"__cb",null,I.c,ka)}}function e(I){if(!I)return"";I=I.split("#")[0].split("?")[0];I=I.toLowerCase();I.indexOf("//")==0&&(I=window.location.protocol+I);I.indexOf("://")==-1&&(I=window.location.protocol+"//"+I);var ka=I.substring(I.indexOf("://")+3),ma=ka.indexOf("/");ma!=-1&&(ka=ka.substring(0,
ma));I=I.substring(0,I.indexOf("://"));if(I!=="http"&&I!=="https"&&I!=="chrome-extension"&&I!=="file"&&I!=="android-app"&&I!=="chrome-search"&&I!=="chrome-untrusted"&&I!=="chrome"&&I!=="devtools")throw Error("l");ma="";var Fa=ka.indexOf(":");if(Fa!=-1){var U=ka.substring(Fa+1);ka=ka.substring(0,Fa);if(I==="http"&&U!=="80"||I==="https"&&U!=="443")ma=":"+U}return I+"://"+ka+ma}function f(I){if(I.charAt(0)=="/"){var ka=I.indexOf("|"),ma=ka>0?I.substring(1,ka):I.substring(1);I=ka>0?I.substring(ka+1):
null;return{id:ma,origin:I}}return null}function h(I){if(typeof I==="undefined"||I==="..")return window.parent;var ka=f(I);if(ka)return k(window.top.frames[ka.id]);I=String(I);return(ka=window.frames[I])?k(ka):(ka=document.getElementById(I))&&ka.contentWindow?ka.contentWindow:null}function k(I){return I?"postMessage"in I?I:I instanceof HTMLIFrameElement&&"contentWindow"in I&&I.contentWindow!=null&&"postMessage"in I.contentWindow?I.contentWindow:null:null}function l(I,ka){if(R[I]!==!0){typeof R[I]===
"undefined"&&(R[I]=0);var ma=h(I);I!==".."&&ma==null||cb.Ib(I,ka)!==!0?R[I]!==!0&&R[I]++<10?window.setTimeout(function(){l(I,ka)},500):(T[I]=Oa,R[I]=!0):R[I]=!0}}function m(I){(I=A[I])&&I.substring(0,1)==="/"&&(I=I.substring(1,2)==="/"?document.location.protocol+I:document.location.protocol+"//"+document.location.host+I);return I}function n(I,ka,ma){ka&&!/http(s)?:\/\/.+/.test(ka)&&(ka.indexOf("//")==0?ka=window.location.protocol+ka:ka.charAt(0)=="/"?ka=window.location.protocol+"//"+window.location.host+
ka:ka.indexOf("://")==-1&&(ka=window.location.protocol+"//"+ka));A[I]=ka;typeof ma!=="undefined"&&(D[I]=!!ma)}function p(I,ka){ka=ka||"";E[I]=String(ka);l(I,ka)}function q(I){I=(I.passReferrer||"").split(":",2);O=I[0]||"none";Y=I[1]||"origin"}function r(I){String(I.useLegacyProtocol)==="true"&&(cb=Zf.FA||Oa,cb.init(d,a))}function w(I,ka){function ma(Fa){Fa=Fa&&Fa.rpc||{};q(Fa);var U=Fa.parentRelayUrl||"";U=e(aa.parent||ka)+U;n("..",U,String(Fa.useLegacyProtocol)==="true");r(Fa);p("..",I)}!aa.parent&&
ka?ma({}):_.ef.register("rpc",null,ma)}function u(I,ka,ma){if(I==="..")w(ma||aa.rpctoken||aa.ifpctok||"",ka);else a:{var Fa=null;if(I.charAt(0)!="/"){if(!_.af)break a;Fa=document.getElementById(I);if(!Fa)throw Error("m`"+I);}Fa=Fa&&Fa.src;ka=ka||e(Fa);n(I,ka);ka=_.af.Rg(Fa);p(I,ma||ka.rpctoken)}}var x={},A={},D={},E={},N=0,H={},R={},aa={},T={},K={},O=null,Y=null,oa=window.top!==window.self,La=window.name,qb=function(){},fb=window.console,Cb=fb&&fb.log&&function(I){fb.log(I)}||function(){},Oa=function(){function I(ka){return function(){Cb(ka+
": call ignored")}}return{nT:function(){return"noop"},nca:function(){return!0},init:I("init"),Ib:I("setup"),call:I("call")}}();_.af&&(aa=_.af.Rg());var Mb=!1,Hb=!1,cb=function(){if(aa.rpctx=="rmr")return Zf.HZ;var I=typeof window.postMessage==="function"?Zf.TN:typeof window.postMessage==="object"?Zf.TN:window.ActiveXObject?Zf.rX?Zf.rX:Zf.FA:navigator.userAgent.indexOf("WebKit")>0?Zf.HZ:navigator.product==="Gecko"?Zf.frameElement:Zf.FA;I||(I=Oa);return I}();x[""]=function(){Cb("Unknown RPC service: "+
this.s)};x.__cb=function(I,ka){var ma=H[I];ma&&(delete H[I],ma.call(this,ka))};return{config:function(I){typeof I.VZ==="function"&&(qb=I.VZ)},register:function(I,ka){if(I==="__cb"||I==="__ack")throw Error("n");if(I==="")throw Error("o");x[I]=ka},unregister:function(I){if(I==="__cb"||I==="__ack")throw Error("p");if(I==="")throw Error("q");delete x[I]},jZ:function(I){x[""]=I},A1:function(){delete x[""]},SS:function(){},call:function(I,ka,ma,Fa){I=I||"..";var U="..";I===".."?U=La:I.charAt(0)=="/"&&(U=
e(window.location.href),U="/"+La+(U?"|"+U:""));++N;ma&&(H[N]=ma);var Ga={s:ka,f:U,c:ma?N:0,a:Array.prototype.slice.call(arguments,3),t:E[I],l:!!D[I]};a:if(O==="bidir"||O==="c2p"&&I===".."||O==="p2c"&&I!==".."){var Ha=window.location.href;var fa="?";if(Y==="query")fa="#";else if(Y==="hash")break a;fa=Ha.lastIndexOf(fa);fa=fa===-1?Ha.length:fa;Ha=Ha.substring(0,fa)}else Ha=null;Ha&&(Ga.r=Ha);if(I===".."||f(I)!=null||document.getElementById(I))(Ha=T[I])||f(I)===null||(Ha=cb),ka.indexOf("legacy__")===
0&&(Ha=cb,Ga.s=ka.substring(8),Ga.c=Ga.c?Ga.c:N),Ga.g=!0,Ga.r=U,Ha?(D[I]&&(Ha=Zf.FA),Ha.call(I,U,Ga)===!1&&(T[I]=Oa,cb.call(I,U,Ga))):K[I]?K[I].push(Ga):K[I]=[Ga]},Gu:m,Oj:n,OC:p,Iw:u,Pn:function(I){return E[I]},vK:function(I){delete A[I];delete D[I];delete E[I];delete R[I];delete T[I]},NT:function(){return cb.nT()},eZ:function(I,ka){I.length>4?cb.Qpa(I,d):c.apply(null,I.concat(ka))},fZ:function(I){I.a=Array.prototype.slice.call(I.a);window.setTimeout(function(){d(I)},0)},getOrigin:e,co:function(I){var ka=
null,ma=m(I);ma?ka=ma:(ma=f(I))?ka=ma.origin:I==".."?ka=aa.parent:(I=document.getElementById(I))&&I.tagName.toLowerCase()==="iframe"&&(ka=I.src);return e(ka)},init:function(){cb.init(d,a)===!1&&(cb=Oa);oa?u(".."):_.ef.register("rpc",null,function(I){I=I.rpc||{};q(I);r(I)})},MO:h,N6:f,Wha:"__ack",Wma:La||"..",gna:0,fna:1,ena:2}}();_.$f.init()};_.$f.config({VZ:function(a){throw Error("r`"+a);}});_.t("gadgets.rpc.config",_.$f.config);_.t("gadgets.rpc.register",_.$f.register);_.t("gadgets.rpc.unregister",_.$f.unregister);_.t("gadgets.rpc.registerDefault",_.$f.jZ);_.t("gadgets.rpc.unregisterDefault",_.$f.A1);_.t("gadgets.rpc.forceParentVerifiable",_.$f.SS);_.t("gadgets.rpc.call",_.$f.call);_.t("gadgets.rpc.getRelayUrl",_.$f.Gu);_.t("gadgets.rpc.setRelayUrl",_.$f.Oj);_.t("gadgets.rpc.setAuthToken",_.$f.OC);_.t("gadgets.rpc.setupReceiver",_.$f.Iw);_.t("gadgets.rpc.getAuthToken",_.$f.Pn);
_.t("gadgets.rpc.removeReceiver",_.$f.vK);_.t("gadgets.rpc.getRelayChannel",_.$f.NT);_.t("gadgets.rpc.receive",_.$f.eZ);_.t("gadgets.rpc.receiveSameDomain",_.$f.fZ);_.t("gadgets.rpc.getOrigin",_.$f.getOrigin);_.t("gadgets.rpc.getTargetOrigin",_.$f.co);
_.Ig=function(a){if(!a)return"";if(/^about:(?:blank|srcdoc)$/.test(a))return window.origin||"";a.indexOf("blob:")===0&&(a=a.substring(5));a=a.split("#")[0].split("?")[0];a=a.toLowerCase();a.indexOf("//")==0&&(a=window.location.protocol+a);/^[\w\-]*:\/\//.test(a)||(a=window.location.href);var b=a.substring(a.indexOf("://")+3),c=b.indexOf("/");c!=-1&&(b=b.substring(0,c));c=a.substring(0,a.indexOf("://"));if(!c)throw Error("s`"+a);if(c!=="http"&&c!=="https"&&c!=="chrome-extension"&&c!=="moz-extension"&&
c!=="file"&&c!=="android-app"&&c!=="chrome-search"&&c!=="chrome-untrusted"&&c!=="chrome"&&c!=="app"&&c!=="devtools")throw Error("t`"+c);a="";var d=b.indexOf(":");if(d!=-1){var e=b.substring(d+1);b=b.substring(0,d);if(c==="http"&&e!=="80"||c==="https"&&e!=="443")a=":"+e}return c+"://"+b+a};
var Mg=function(){this.blockSize=-1},Ng=function(){this.blockSize=-1;this.blockSize=64;this.Rc=[];this.YE=[];this.E6=[];this.OB=[];this.OB[0]=128;for(var a=1;a<this.blockSize;++a)this.OB[a]=0;this.ED=this.fr=0;this.reset()};_.eb(Ng,Mg);Ng.prototype.reset=function(){this.Rc[0]=1732584193;this.Rc[1]=4023233417;this.Rc[2]=2562383102;this.Rc[3]=271733878;this.Rc[4]=3285377520;this.ED=this.fr=0};
var Og=function(a,b,c){c||(c=0);var d=a.E6;if(typeof b==="string")for(var e=0;e<16;e++)d[e]=b.charCodeAt(c)<<24|b.charCodeAt(c+1)<<16|b.charCodeAt(c+2)<<8|b.charCodeAt(c+3),c+=4;else for(e=0;e<16;e++)d[e]=b[c]<<24|b[c+1]<<16|b[c+2]<<8|b[c+3],c+=4;for(b=16;b<80;b++)c=d[b-3]^d[b-8]^d[b-14]^d[b-16],d[b]=(c<<1|c>>>31)&4294967295;b=a.Rc[0];c=a.Rc[1];e=a.Rc[2];for(var f=a.Rc[3],h=a.Rc[4],k,l,m=0;m<80;m++)m<40?m<20?(k=f^c&(e^f),l=1518500249):(k=c^e^f,l=1859775393):m<60?(k=c&e|f&(c|e),l=2400959708):(k=c^
e^f,l=3395469782),k=(b<<5|b>>>27)+k+h+l+d[m]&4294967295,h=f,f=e,e=(c<<30|c>>>2)&4294967295,c=b,b=k;a.Rc[0]=a.Rc[0]+b&4294967295;a.Rc[1]=a.Rc[1]+c&4294967295;a.Rc[2]=a.Rc[2]+e&4294967295;a.Rc[3]=a.Rc[3]+f&4294967295;a.Rc[4]=a.Rc[4]+h&4294967295};
Ng.prototype.update=function(a,b){if(a!=null){b===void 0&&(b=a.length);for(var c=b-this.blockSize,d=0,e=this.YE,f=this.fr;d<b;){if(f==0)for(;d<=c;)Og(this,a,d),d+=this.blockSize;if(typeof a==="string")for(;d<b;){if(e[f]=a.charCodeAt(d),++f,++d,f==this.blockSize){Og(this,e);f=0;break}}else for(;d<b;)if(e[f]=a[d],++f,++d,f==this.blockSize){Og(this,e);f=0;break}}this.fr=f;this.ED+=b}};
Ng.prototype.digest=function(){var a=[],b=this.ED*8;this.fr<56?this.update(this.OB,56-this.fr):this.update(this.OB,this.blockSize-(this.fr-56));for(var c=this.blockSize-1;c>=56;c--)this.YE[c]=b&255,b/=256;Og(this,this.YE);for(c=b=0;c<5;c++)for(var d=24;d>=0;d-=8)a[b]=this.Rc[c]>>d&255,++b;return a};_.Pg=function(){this.bN=new Ng};_.g=_.Pg.prototype;_.g.reset=function(){this.bN.reset()};_.g.C1=function(a){this.bN.update(a)};_.g.ZQ=function(){return this.bN.digest()};_.g.ux=function(a){a=unescape(encodeURIComponent(a));for(var b=[],c=a.length,d=0;d<c;++d)b.push(a.charCodeAt(d));this.C1(b)};_.g.Si=function(){for(var a=this.ZQ(),b="",c=0;c<a.length;c++)b+="0123456789ABCDEF".charAt(Math.floor(a[c]/16))+"0123456789ABCDEF".charAt(a[c]%16);return b};
var th;_.sh=function(a){_.Xa.setTimeout(function(){throw a;},0)};th=0;_.uh=function(a){return Object.prototype.hasOwnProperty.call(a,_.ab)&&a[_.ab]||(a[_.ab]=++th)};
_.vh=function(){return _.Mc("Safari")&&!(_.Wc()||(_.Oc()?0:_.Mc("Coast"))||_.Pc()||_.Sc()||_.Tc()||_.Uc()||_.Vc()||_.Mc("Silk")||_.Mc("Android"))};_.wh=function(){return _.Mc("Android")&&!(_.Wc()||_.Vc()||_.Pc()||_.Mc("Silk"))};_.yh=_.Vc();_.zh=_.ad()||_.Mc("iPod");_.Ah=_.Mc("iPad");_.Bh=_.wh();_.Ch=_.Wc();_.Dh=_.vh()&&!_.bd();
_.di=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};_.ei=function(a){var b=_.di();if(!a)return b;a=a.split("/");for(var c=0,d=a.length;b&&typeof b==="object"&&c<d;++c)b=b[a[c]];return c===a.length&&b!==void 0?b:void 0};
_.fi=function(a,b,c,d){for(var e=0,f=a.length,h;e<f;){var k=e+(f-e>>>1);var l=c?b.call(void 0,a[k],k,a):b(d,a[k]);l>0?e=k+1:(f=k,h=!l)}return h?e:-e-1};_.gi=function(a,b){var c={},d;for(d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c};var hi;hi=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/u\/(\d)\//;
_.ii=function(a){var b=_.ei("googleapis.config/sessionIndex");"string"===typeof b&&b.length>254&&(b=null);b==null&&(b=window.__X_GOOG_AUTHUSER);"string"===typeof b&&b.length>254&&(b=null);if(b==null){var c=window.google;c&&(b=c.authuser)}"string"===typeof b&&b.length>254&&(b=null);b==null&&(a=a||window.location.href,b=_.Ge(a,"authuser")||null,b==null&&(b=(b=a.match(hi))?b[1]:null));if(b==null)return null;b=String(b);b.length>254&&(b=null);return b};
_.vi=function(){if(!_.Xa.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};_.Xa.addEventListener("test",c,b);_.Xa.removeEventListener("test",c,b)}catch(d){}return a}();
var wi=function(){var a=_.Me.ms||_.Me.u;if(a)return(new URL(a)).origin};var Di=function(a){this.KS=a;this.count=this.count=0};Di.prototype.rb=function(a,b){a?this.count+=a:this.count++;this.KS&&(b===void 0||b)&&this.KS()};Di.prototype.get=function(){return this.count};Di.prototype.reset=function(){this.count=0};var Fi,Ii;Fi=function(){var a=!0,b=this;a=a===void 0?!0:a;this.Ny=new Map;this.RE=!1;var c=wi();c&&(this.url=c+"/js/gen_204",c=_.ei("gen204logger")||{},this.ju=c.interval,this.LS=c.rate,this.RE=c.cqa,a&&this.url&&Ei(this),document.addEventListener("visibilitychange",this.flush),this.flush(),document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&b.flush()}),document.addEventListener("pagehide",this.flush.bind(this)))};_.Gi=function(){Fi.NW||(Fi.NW=new Fi);return Fi.NW};
Ii=function(a){var b=_.Me.dm||[];if(b&&b.length!==0){b=_.Aa(b);for(var c=b.next();!c.done;c=b.next())_.Hi(a,c.value).rb(1,!1);delete _.Me.dm;a.flush()}};_.Hi=function(a,b){a.Ny.has(b)||a.Ny.set(b,new Di(a.RE?void 0:function(){a.flush()}));return a.Ny.get(b)};
Fi.prototype.flush=function(){var a=this;if(this.url&&this.LS){Ii(this);for(var b="",c=_.Aa(this.Ny),d=c.next();!d.done;d=c.next()){var e=_.Aa(d.value);d=e.next().value;e=e.next().value;var f=e.get();f>0&&(b+=b.length>0?"&":"",b+="c=",b+=encodeURIComponent(d+":"+f),e.reset());if(b.length>1E3)break}if(b!==""&&Math.random()<this.LS){try{var h=AbortSignal.timeout(3E4)}catch(k){h=void 0}fetch(this.url+"?"+b,{method:"GET",mode:"no-cors",signal:h}).catch(function(){}).finally(function(){Ei(a)})}}};
Fi.prototype.setInterval=function(a){this.ju=a};var Ei=function(a){a.ju&&a.RE&&setTimeout(function(){a.flush()},a.ju)};var Ki,Ji,Qi,Ri,Li,Oi,Mi,Si,Ni;_.Pi=function(){_.Hi(_.Gi(),50).rb();if(Ji){var a=new _.xe.Uint32Array(1);Ki.getRandomValues(a);a=Number("0."+a[0])}else a=Li,a+=parseInt(Mi.substr(0,20),16),Mi=Ni(Mi),a/=Oi+1.2089258196146292E24;return a};Ki=_.xe.crypto;Ji=!1;Qi=0;Ri=0;Li=1;Oi=0;Mi="";Si=function(a){a=a||_.xe.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Li=Li*b%Oi;Qi>0&&++Ri==Qi&&_.Ke(_.xe,"mousemove",Si,"remove","de")};
Ni=function(a){var b=new _.Pg;b.ux(a);return b.Si()};Ji=!!Ki&&typeof Ki.getRandomValues=="function";Ji||(Oi=(screen.width*screen.width+screen.height)*1E6,Mi=Ni(_.ye.cookie+"|"+_.ye.location+"|"+(new Date).getTime()+"|"+Math.random()),Qi=_.ei("random/maxObserveMousemove")||0,Qi!=0&&_.Le(_.xe,"mousemove",Si));
_.bj=function(a){var b=window;a=(a||b.location.href).match(RegExp(".*(\\?|#|&)usegapi=([^&#]+)"))||[];return"1"===decodeURIComponent(a[a.length-1]||"")};
var hj;_.gj=function(a,b){b=(0,_.sb)(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c};_.ij=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<hj.length;f++)c=hj[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};hj="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");_.jj=[];_.kj=[];_.lj=!1;
_.mj=function(a){_.jj[_.jj.length]=a;if(_.lj)for(var b=0;b<_.kj.length;b++)a((0,_.z)(_.kj[b].wrap,_.kj[b]))};
var bk=function(a){this.T=a};_.g=bk.prototype;_.g.value=function(){return this.T};_.g.Ne=function(a){this.T.width=a;return this};_.g.Qb=function(){return this.T.width};_.g.Td=function(a){this.T.height=a;return this};_.g.Nc=function(){return this.T.height};_.g.Ei=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.ck=function(a){this.T=a||{}};_.g=_.ck.prototype;_.g.value=function(){return this.T};_.g.setUrl=function(a){this.T.url=a;return this};_.g.getUrl=function(){return this.T.url};_.g.Ei=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.g.Me=function(a){this.T.id=a;return this};_.g.getId=function(){return this.T.id};_.g.Zm=function(a){this.T.rpctoken=a;return this};_.dk=function(a,b){a.T.messageHandlers=b;return a};_.ek=function(a,b){a.T.messageHandlersFilter=b;return a};
_.g=_.ck.prototype;_.g.Wr=_.jb(4);_.g.getContext=function(){return this.T.context};_.g.kd=function(){return this.T.openerIframe};_.g.Vn=function(){this.T.attributes=this.T.attributes||{};return new bk(this.T.attributes)};_.g.Fz=_.jb(5);
var jk;_.fk=function(a){var b={},c;for(c in a)b[c]=a[c];return b};jk=function(){for(var a;a=gk.remove();){try{a.Rh.call(a.scope)}catch(b){_.sh(b)}hk.put(a)}ik=!1};_.kk=function(a){if(!(a instanceof Array)){a=_.Aa(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a};_.lk=function(){};_.mk=function(a){a.prototype.$goog_Thenable=!0};_.nk=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};
_.ok=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:function(a){return a};var pk=function(a,b){this.l8=a;this.afa=b;this.AB=0;this.AA=null};pk.prototype.get=function(){if(this.AB>0){this.AB--;var a=this.AA;this.AA=a.next;a.next=null}else a=this.l8();return a};pk.prototype.put=function(a){this.afa(a);this.AB<100&&(this.AB++,a.next=this.AA,this.AA=a)};_.qk=function(a){return a};_.mj(function(a){_.qk=a});var rk=function(){this.WD=this.Os=null};rk.prototype.add=function(a,b){var c=hk.get();c.set(a,b);this.WD?this.WD.next=c:this.Os=c;this.WD=c};rk.prototype.remove=function(){var a=null;this.Os&&(a=this.Os,this.Os=this.Os.next,this.Os||(this.WD=null),a.next=null);return a};var hk=new pk(function(){return new sk},function(a){return a.reset()}),sk=function(){this.next=this.scope=this.Rh=null};sk.prototype.set=function(a,b){this.Rh=a;this.scope=b;this.next=null};
sk.prototype.reset=function(){this.next=this.scope=this.Rh=null};var tk,ik,gk,uk;ik=!1;gk=new rk;_.vk=function(a,b){tk||uk();ik||(tk(),ik=!0);gk.add(a,b)};uk=function(){var a=Promise.resolve(void 0);tk=function(){a.then(jk)}};var yk,zk,Ak,Ok,Sk,Qk,Tk;_.xk=function(a,b){this.Ca=0;this.qf=void 0;this.cq=this.Bl=this.Gb=null;this.qA=this.fG=!1;if(a!=_.lk)try{var c=this;a.call(b,function(d){wk(c,2,d)},function(d){wk(c,3,d)})}catch(d){wk(this,3,d)}};yk=function(){this.next=this.context=this.Er=this.Ov=this.xn=null;this.Ux=!1};yk.prototype.reset=function(){this.context=this.Er=this.Ov=this.xn=null;this.Ux=!1};zk=new pk(function(){return new yk},function(a){a.reset()});
Ak=function(a,b,c){var d=zk.get();d.Ov=a;d.Er=b;d.context=c;return d};_.Bk=function(a){if(a instanceof _.xk)return a;var b=new _.xk(_.lk);wk(b,2,a);return b};_.Ck=function(a){return new _.xk(function(b,c){c(a)})};_.Ek=function(a,b,c){Dk(a,b,c,null)||_.vk(_.bb(b,a))};_.Fk=function(a){return new _.xk(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m,n){d--;e[m]=n;d==0&&b(e)},h=function(m){c(m)},k,l=0;l<a.length;l++)k=a[l],_.Ek(k,_.bb(f,l),h);else b(e)})};
_.Hk=function(){var a,b,c=new _.xk(function(d,e){a=d;b=e});return new Gk(c,a,b)};_.xk.prototype.then=function(a,b,c){return Ik(this,(0,_.ok)(typeof a==="function"?a:null),(0,_.ok)(typeof b==="function"?b:null),c)};_.mk(_.xk);var Kk=function(a,b,c,d){Jk(a,Ak(b||_.lk,c||null,d))};_.xk.prototype.finally=function(a){var b=this;a=(0,_.ok)(a);return new Promise(function(c,d){Kk(b,function(e){a();c(e)},function(e){a();d(e)})})};_.xk.prototype.zD=function(a,b){return Ik(this,null,(0,_.ok)(a),b)};
_.xk.prototype.catch=_.xk.prototype.zD;_.xk.prototype.cancel=function(a){if(this.Ca==0){var b=new _.Lk(a);_.vk(function(){Mk(this,b)},this)}};
var Mk=function(a,b){if(a.Ca==0)if(a.Gb){var c=a.Gb;if(c.Bl){for(var d=0,e=null,f=null,h=c.Bl;h&&(h.Ux||(d++,h.xn==a&&(e=h),!(e&&d>1)));h=h.next)e||(f=h);e&&(c.Ca==0&&d==1?Mk(c,b):(f?(d=f,d.next==c.cq&&(c.cq=d),d.next=d.next.next):Nk(c),Ok(c,e,3,b)))}a.Gb=null}else wk(a,3,b)},Jk=function(a,b){a.Bl||a.Ca!=2&&a.Ca!=3||Pk(a);a.cq?a.cq.next=b:a.Bl=b;a.cq=b},Ik=function(a,b,c,d){var e=Ak(null,null,null);e.xn=new _.xk(function(f,h){e.Ov=b?function(k){try{var l=b.call(d,k);f(l)}catch(m){h(m)}}:f;e.Er=c?
function(k){try{var l=c.call(d,k);l===void 0&&k instanceof _.Lk?h(k):f(l)}catch(m){h(m)}}:h});e.xn.Gb=a;Jk(a,e);return e.xn};_.xk.prototype.sha=function(a){this.Ca=0;wk(this,2,a)};_.xk.prototype.tha=function(a){this.Ca=0;wk(this,3,a)};
var wk=function(a,b,c){a.Ca==0&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.Ca=1,Dk(c,a.sha,a.tha,a)||(a.qf=c,a.Ca=b,a.Gb=null,Pk(a),b!=3||c instanceof _.Lk||Qk(a,c)))},Dk=function(a,b,c,d){if(a instanceof _.xk)return Kk(a,b,c,d),!0;if(_.nk(a))return a.then(b,c,d),!0;if(_.vb(a))try{var e=a.then;if(typeof e==="function")return Rk(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1},Rk=function(a,b,c,d,e){var f=!1,h=function(l){f||(f=!0,c.call(e,l))},k=function(l){f||(f=!0,
d.call(e,l))};try{b.call(a,h,k)}catch(l){k(l)}},Pk=function(a){a.fG||(a.fG=!0,_.vk(a.nz,a))},Nk=function(a){var b=null;a.Bl&&(b=a.Bl,a.Bl=b.next,b.next=null);a.Bl||(a.cq=null);return b};_.xk.prototype.nz=function(){for(var a;a=Nk(this);)Ok(this,a,this.Ca,this.qf);this.fG=!1};Ok=function(a,b,c,d){if(c==3&&b.Er&&!b.Ux)for(;a&&a.qA;a=a.Gb)a.qA=!1;if(b.xn)b.xn.Gb=null,Sk(b,c,d);else try{b.Ux?b.Ov.call(b.context):Sk(b,c,d)}catch(e){Tk.call(null,e)}zk.put(b)};
Sk=function(a,b,c){b==2?a.Ov.call(a.context,c):a.Er&&a.Er.call(a.context,c)};Qk=function(a,b){a.qA=!0;_.vk(function(){a.qA&&Tk.call(null,b)})};Tk=_.sh;_.Lk=function(a){_.lb.call(this,a);this.rZ=!1};_.eb(_.Lk,_.lb);_.Lk.prototype.name="cancel";var Gk=function(a,b,c){this.promise=a;this.resolve=b;this.reject=c};
_.Uk=function(a){return new _.xk(a)};
var bl=function(){this.nx={lZ:Vk?"../"+Vk:null,Yy:Wk,cU:Xk,Yra:Yk,ho:Zk,Psa:$k};this.Vf=_.xe;this.CY=this.s8;this.m9=/MSIE\s*[0-8](\D|$)/.test(window.navigator.userAgent);if(this.nx.lZ){this.Vf=this.nx.cU(this.Vf,this.nx.lZ);var a=this.Vf.document,b=a.createElement("script");b.setAttribute("type","text/javascript");b.text="window.doPostMsg=function(w,s,o) {window.setTimeout(function(){w.postMessage(s,o);},0);};";a.body.appendChild(b);this.CY=this.Vf.doPostMsg}this.cN={};this.FN={};a=(0,_.z)(this.DH,
this);_.Le(this.Vf,"message",a);_.Be(_.Me,"RPMQ",[]).push(a);this.Vf!=this.Vf.parent&&al(this,this.Vf.parent,this.VI(this.Vf.name),"*")};bl.prototype.VI=function(a){return'{"h":"'+escape(a)+'"}'};var cl=function(a){var b=null;a.indexOf('{"h":"')===0&&a.indexOf('"}')===a.length-2&&(b=unescape(a.substring(6,a.length-2)));return b},dl=function(a){if(!/^\s*{/.test(a))return!1;a=_.Qf(a);return a!==null&&typeof a==="object"&&!!a.g};
bl.prototype.DH=function(a){var b=String(a.data);_.Vf.debug("gapix.rpc.receive("+Yk+"): "+(!b||b.length<=512?b:b.substr(0,512)+"... ("+b.length+" bytes)"));var c=b.indexOf("!_")!==0;c||(b=b.substring(2));var d=dl(b);if(!c&&!d){if(!d&&(c=cl(b))){if(this.cN[c])this.cN[c]();else this.FN[c]=1;return}var e=a.origin,f=this.nx.Yy;this.m9?_.xe.setTimeout(function(){f(b,e)},0):f(b,e)}};bl.prototype.Ib=function(a,b){a===".."||this.FN[a]?(b(),delete this.FN[a]):this.cN[a]=b};
var al=function(a,b,c,d){var e=dl(c)?"":"!_";_.Vf.debug("gapix.rpc.send("+Yk+"): "+(!c||c.length<=512?c:c.substr(0,512)+"... ("+c.length+" bytes)"));a.CY(b,e+c,d)};bl.prototype.s8=function(a,b,c){a.postMessage(b,c)};bl.prototype.send=function(a,b,c){(a=this.nx.cU(this.Vf,a))&&!a.closed&&al(this,a,b,c)};var el,fl,gl,hl,il,jl,kl,Vk,Yk,ll,ml,nl,Xk,Zk,pl,ql,Al,Bl,Dl,$k,Fl,El,rl,sl,Gl,Wk,Hl,Il;el=0;fl=[];gl={};hl={};il=_.xe.location.href;jl=_.Ge(il,"rpctoken");kl=_.Ge(il,"parent")||_.ye.referrer;Vk=_.Ge(il,"rly");Yk=Vk||(_.xe!==_.xe.top||_.xe.opener)&&_.xe.name||"..";ll=null;ml={};nl=function(){};_.ol={send:nl,Ib:nl,VI:nl};
Xk=function(a,b){var c=a;b.charAt(0)=="/"&&(b=b.substring(1),c=_.xe.top);if(b.length===0)return c;for(b=b.split("/");b.length;){a=b.shift();a.charAt(0)=="{"&&a.charAt(a.length-1)=="}"&&(a=a.substring(1,a.length-1));var d=a;if(d==="..")c=c==c.parent?c.opener:c.parent;else if(d!==".."&&c.frames[d]){var e=c;a=d;c=c.frames[d];if(!("postMessage"in c))if(c instanceof HTMLIFrameElement&&"contentWindow"in c)c=c.contentWindow!=null&&"postMessage"in c.contentWindow?c.contentWindow:null;else{d=null;e=_.Aa(e.document.getElementsByTagName("iframe"));
for(var f=e.next();!f.done;f=e.next())if(f=f.value,f.getAttribute("id")==a||f.getAttribute("name")==a)d=f;if(d&&"contentWindow"in d)c=d.contentWindow!=null?d.contentWindow:null;else throw Error("F`"+c+"`"+a);}}else return null}return c};Zk=function(a){return(a=gl[a])&&a.token};pl=function(a){if(a.f in{})return!1;var b=a.t,c=gl[a.r];a=a.origin;return c&&(c.token===b||!c.token&&!b)&&(a===c.origin||c.origin==="*")};
ql=function(a){var b=a.id.split("/"),c=b[b.length-1],d=a.origin;return function(e){var f=e.origin;return e.f==c&&(d==f||d=="*")}};_.tl=function(a,b,c){a=rl(a);hl[a.name]={Rh:b,Cv:a.Cv,Ms:c||pl};sl()};_.ul=function(a){a=rl(a);delete hl[a.name]};Al={};Bl=function(a,b){(a=Al["_"+a])&&a[1](this)&&a[0].call(this,b)};Dl=function(a){var b=a.c;if(!b)return nl;var c=a.r,d=a.g?"legacy__":"";return function(){var e=[].slice.call(arguments,0);e.unshift(c,d+"__cb",null,b);_.Cl.apply(null,e)}};
$k=function(a){ll=a};Fl=function(a){ml[a]||(ml[a]=_.xe.setTimeout(function(){ml[a]=!1;El(a)},0))};El=function(a){var b=gl[a];if(b&&b.ready){var c=b.iK;for(b.iK=[];c.length;)_.ol.send(a,_.Rf(c.shift()),b.origin)}};rl=function(a){return a.indexOf("legacy__")===0?{name:a.substring(8),Cv:!0}:{name:a,Cv:!1}};
sl=function(){for(var a=_.ei("rpc/residenceSec")||60,b=(new Date).getTime()/1E3,c,d=0;c=fl[d];++d){var e=c.kp;if(!e||a>0&&b-c.timestamp>a)fl.splice(d,1),--d;else{var f=e.s,h=hl[f]||hl["*"];if(h)if(fl.splice(d,1),--d,e.origin=c.origin,c=Dl(e),e.callback=c,h.Ms(e)){if(f!=="__cb"&&!!h.Cv!=!!e.g)break;e=h.Rh.apply(e,e.a);e!==void 0&&c(e)}else _.Vf.debug("gapix.rpc.rejected("+Yk+"): "+f)}}};Gl=function(a,b,c){fl.push({kp:a,origin:b,timestamp:(new Date).getTime()/1E3});c||sl()};
Wk=function(a,b){a=_.Qf(a);Gl(a,b,!1)};Hl=function(a){for(;a.length;)Gl(a.shift(),this.origin,!0);sl()};Il=function(a){var b=!1;a=a.split("|");var c=a[0];c.indexOf("/")>=0&&(b=!0);return{id:c,origin:a[1]||"*",rI:b}};
_.Jl=function(a,b,c,d){var e=Il(a);d&&(_.xe.frames[e.id]=_.xe.frames[e.id]||d);a=e.id;if(!gl.hasOwnProperty(a)){c=c||null;d=e.origin;if(a==="..")d=_.Ig(kl),c=c||jl;else if(!e.rI){var f=_.ye.getElementById(a);f&&(f=f.src,d=_.Ig(f),c=c||_.Ge(f,"rpctoken"))}e.origin==="*"&&d||(d=e.origin);gl[a]={token:c,iK:[],origin:d,mfa:b,dZ:function(){var h=a;gl[h].ready=1;El(h)}};_.ol.Ib(a,gl[a].dZ)}return gl[a].dZ};
_.Cl=function(a,b,c,d){a=a||"..";_.Jl(a);a=a.split("|",1)[0];var e=b,f=a,h=[].slice.call(arguments,3),k=c,l=Yk,m=jl,n=gl[f],p=l,q=Il(f);if(n&&f!==".."){if(q.rI){if(!(m=gl[f].mfa)){m=ll?ll.substring(1).split("/"):[Yk];p=m.length-1;for(f=_.xe.parent;f!==_.xe.top;){var r=f.parent;if(!p--){for(var w=null,u=r.frames.length,x=0;x<u;++x)r.frames[x]==f&&(w=x);m.unshift("{"+w+"}")}f=r}m="/"+m.join("/")}p=m}else p=l="..";m=n.token}k&&q?(n=pl,q.rI&&(n=ql(q)),Al["_"+ ++el]=[k,n],k=el):k=null;h={s:e,f:l,r:p,t:m,
c:k,a:h};e=rl(e);h.s=e.name;h.g=e.Cv;gl[a].iK.push(h);Fl(a)};if(typeof _.xe.postMessage==="function"||typeof _.xe.postMessage==="object")_.ol=new bl,_.tl("__cb",Bl,function(){return!0}),_.tl("_processBatch",Hl,function(){return!0}),_.Jl("..");
var Ll,Ml,Nl,Ol,Pl,Ql,Rl,Sl,Tl,Ul,Xl,Yl,bm,cm,dm,em,fm,gm,hm,im;_.Kl=function(a,b){if(!a)throw Error(b||"");};Ll=/&/g;Ml=/</g;Nl=/>/g;Ol=/"/g;Pl=/'/g;Ql=function(a){return String(a).replace(Ll,"&amp;").replace(Ml,"&lt;").replace(Nl,"&gt;").replace(Ol,"&quot;").replace(Pl,"&#39;")};Rl=/[\ud800-\udbff][\udc00-\udfff]|[^!-~]/g;Sl=/%([a-f]|[0-9a-fA-F][a-f])/g;Tl=/^(https?|ftp|file|chrome-extension):$/i;
Ul=function(a){a=String(a);a=a.replace(Rl,function(e){try{return encodeURIComponent(e)}catch(f){return encodeURIComponent(e.replace(/^[^%]+$/g,"\ufffd"))}}).replace(_.Ie,function(e){return e.replace(/%/g,"%25")}).replace(Sl,function(e){return e.toUpperCase()});a=a.match(_.He)||[];var b=_.Ce(),c=function(e){return e.replace(/\\/g,"%5C").replace(/\^/g,"%5E").replace(/`/g,"%60").replace(/\{/g,"%7B").replace(/\|/g,"%7C").replace(/\}/g,"%7D")},d=!!(a[1]||"").match(Tl);b.mt=c((a[1]||"")+(a[2]||"")+(a[3]||
(a[2]&&d?"/":"")));d=function(e){return c(e.replace(/\?/g,"%3F").replace(/#/g,"%23"))};b.query=a[5]?[d(a[5])]:[];b.Xi=a[7]?[d(a[7])]:[];return b};Xl=function(a){return a.mt+(a.query.length>0?"?"+a.query.join("&"):"")+(a.Xi.length>0?"#"+a.Xi.join("&"):"")};Yl=function(a,b){var c=[];if(a)for(var d in a)if(_.De(a,d)&&a[d]!=null){var e=b?b(a[d]):a[d];c.push(encodeURIComponent(d)+"="+encodeURIComponent(e))}return c};
_.Zl=function(a,b,c,d){a=Ul(a);a.query.push.apply(a.query,Yl(b,d));a.Xi.push.apply(a.Xi,Yl(c,d));return Xl(a)};
_.$l=function(a,b){var c=Ul(b);b=c.mt;c.query.length&&(b+="?"+c.query.join(""));c.Xi.length&&(b+="#"+c.Xi.join(""));var d="";b.length>2E3&&(c=b,b=b.substr(0,2E3),b=b.replace(_.Je,""),d=c.substr(b.length));var e=a.createElement("div");a=a.createElement("a");c=Ul(b);b=c.mt;c.query.length&&(b+="?"+c.query.join(""));c.Xi.length&&(b+="#"+c.Xi.join(""));_.Ec(a,new _.lc(_.we(b)));e.appendChild(a);_.Hc(e,_.ec(e.innerHTML));b=String(e.firstChild.href);e.parentNode&&e.parentNode.removeChild(e);c=Ul(b+d);b=
c.mt;c.query.length&&(b+="?"+c.query.join(""));c.Xi.length&&(b+="#"+c.Xi.join(""));return b};_.am=/^https?:\/\/[^\/%\\?#\s]+\/[^\s]*$/i;cm=function(a){for(;a.firstChild;)a.removeChild(a.firstChild)};dm=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/b\/(\d{10,21})\//;
em=function(){var a=_.ei("googleapis.config/sessionDelegate");"string"===typeof a&&a.length>21&&(a=null);a==null&&(a=(a=window.location.href.match(dm))?a[1]:null);if(a==null)return null;a=String(a);a.length>21&&(a=null);return a};fm=function(){var a=_.Me.onl;if(!a){a=_.Ce();_.Me.onl=a;var b=_.Ce();a.e=function(c){var d=b[c];d&&(delete b[c],d())};a.a=function(c,d){b[c]=d};a.r=function(c){delete b[c]}}return a};gm=function(a,b){b=b.onload;return typeof b==="function"?(fm().a(a,b),b):null};
hm=function(a){_.Kl(/^\w+$/.test(a),"Unsupported id - "+a);return'onload="window.___jsl.onl.e(&#34;'+a+'&#34;)"'};im=function(a){fm().r(a)};var km,lm,pm;_.jm={allowtransparency:"true",frameborder:"0",hspace:"0",marginheight:"0",marginwidth:"0",scrolling:"no",style:"",tabindex:"0",vspace:"0",width:"100%"};km={allowtransparency:!0,onload:!0};lm=0;_.mm=function(a,b){var c=0;do var d=b.id||["I",lm++,"_",(new Date).getTime()].join("");while(a.getElementById(d)&&++c<5);_.Kl(c<5,"Error creating iframe id");return d};_.nm=function(a,b){return a?b+"/"+a:""};
_.om=function(a,b,c,d){var e={},f={};a.documentMode&&a.documentMode<9&&(e.hostiemode=a.documentMode);_.Ee(d.queryParams||{},e);_.Ee(d.fragmentParams||{},f);var h=d.pfname;var k=_.Ce();_.ei("iframes/dropLegacyIdParam")||(k.id=c);k._gfid=c;k.parent=a.location.protocol+"//"+a.location.host;c=_.Ge(a.location.href,"parent");h=h||"";!h&&c&&(h=_.Ge(a.location.href,"_gfid","")||_.Ge(a.location.href,"id",""),h=_.nm(h,_.Ge(a.location.href,"pfname","")));h||(c=_.Qf(_.Ge(a.location.href,"jcp","")))&&typeof c==
"object"&&(h=_.nm(c.id,c.pfname));k.pfname=h;d.connectWithJsonParam&&(h={},h.jcp=_.Rf(k),k=h);h=_.Ge(b,"rpctoken")||e.rpctoken||f.rpctoken;h||(h=d.rpctoken||String(Math.round(_.Pi()*1E8)),k.rpctoken=h);d.rpctoken=h;_.Ee(k,d.connectWithQueryParams?e:f);k=a.location.href;a=_.Ce();(h=_.Ge(k,"_bsh",_.Me.bsh))&&(a._bsh=h);(k=_.Me.dpo?_.Me.h:_.Ge(k,"jsh",_.Me.h))&&(a.jsh=k);d.hintInFragment?_.Ee(a,f):_.Ee(a,e);return _.Zl(b,e,f,d.paramsSerializer)};
pm=function(a){_.Kl(!a||_.am.test(a),"Illegal url for new iframe - "+a)};
_.qm=function(a,b,c,d,e){pm(c.src);var f,h=gm(d,c),k=h?hm(d):"";try{document.all&&(f=a.createElement('<iframe frameborder="'+Ql(String(c.frameborder))+'" scrolling="'+Ql(String(c.scrolling))+'" '+k+' name="'+Ql(String(c.name))+'"/>'))}catch(m){}finally{f||(f=_.ae(a).createElement("IFRAME"),h&&(f.onload=function(){f.onload=null;h.call(this)},im(d)))}f.setAttribute("ng-non-bindable","");for(var l in c)a=c[l],l==="style"&&typeof a==="object"?_.Ee(a,f.style):km[l]||f.setAttribute(l,String(a));(l=e&&e.beforeNode||
null)||e&&e.dontclear||cm(b);b.insertBefore(f,l);f=l?l.previousSibling:b.lastChild;c.allowtransparency&&(f.allowTransparency=!0);return f};var rm,um;rm=/^:[\w]+$/;_.sm=/:([a-zA-Z_]+):/g;_.tm=function(){var a=_.ii()||"0",b=em();var c=_.ii()||a;var d=em(),e="";c&&(e+="u/"+encodeURIComponent(String(c))+"/");d&&(e+="b/"+encodeURIComponent(String(d))+"/");c=e||null;(e=(d=_.ei("isLoggedIn")===!1)?"_/im/":"")&&(c="");var f=_.ei("iframes/:socialhost:"),h=_.ei("iframes/:im_socialhost:");return bm={socialhost:f,ctx_socialhost:d?h:f,session_index:a,session_delegate:b,session_prefix:c,im_prefix:e}};um=function(a,b){return _.tm()[b]||""};
_.vm=function(a){return _.$l(_.ye,a.replace(_.sm,um))};_.wm=function(a){var b=a;rm.test(a)&&(b="iframes/"+b.substring(1)+"/url",b=_.ei(b),_.Kl(!!b,"Unknown iframe url config for - "+a));return _.vm(b)};
_.xm=function(a,b,c){c=c||{};var d=c.attributes||{};_.Kl(!(c.allowPost||c.forcePost)||!d.onload,"onload is not supported by post iframe (allowPost or forcePost)");a=_.wm(a);d=b.ownerDocument||_.ye;var e=_.mm(d,c);a=_.om(d,a,e,c);var f=c,h=_.Ce();_.Ee(_.jm,h);_.Ee(f.attributes,h);h.name=h.id=e;h.src=a;c.eurl=a;c=(f=c)||{};var k=!!c.allowPost;if(c.forcePost||k&&a.length>2E3){c=Ul(a);h.src="";f.dropDataPostorigin||(h["data-postorigin"]=a);a=_.qm(d,b,h,e);if(navigator.userAgent.indexOf("WebKit")!=-1){var l=
a.contentWindow.document;l.open();h=l.createElement("div");k={};var m=e+"_inner";k.name=m;k.src="";k.style="display:none";_.qm(d,h,k,m,f)}h=(f=c.query[0])?f.split("&"):[];f=[];for(k=0;k<h.length;k++)m=h[k].split("=",2),f.push([decodeURIComponent(m[0]),decodeURIComponent(m[1])]);c.query=[];h=Xl(c);_.Kl(_.am.test(h),"Invalid URL: "+h);c=d.createElement("form");c.method="POST";c.target=e;c.style.display="none";e=_.sc(h);e!==void 0&&(c.action=e);for(e=0;e<f.length;e++)h=d.createElement("input"),h.type=
"hidden",h.name=f[e][0],h.value=f[e][1],c.appendChild(h);b.appendChild(c);c.submit();c.parentNode.removeChild(c);l&&l.close();b=a}else b=_.qm(d,b,h,e,f);return b};
var ym;
ym=function(){function a(k,l){k=window.getComputedStyle(k,"").getPropertyValue(l).match(/^([0-9]+)/);return parseInt(k[0],10)}for(var b=0,c=[document.body];c.length>0;){var d=c.shift(),e=d.childNodes;if(typeof d.style!=="undefined"){var f=d.style.overflowY;f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.overflowY:null);if(f!="visible"&&f!="inherit"&&(f=d.style.height,f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.height:""),f.length>0&&f!="auto"))continue}for(d=0;d<e.length;d++){f=e[d];
if(typeof f.offsetTop!=="undefined"&&typeof f.offsetHeight!=="undefined"){var h=f.offsetTop+f.offsetHeight+a(f,"margin-bottom");b=Math.max(b,h)}c.push(f)}}return b+a(document.body,"border-bottom")+a(document.body,"margin-bottom")+a(document.body,"padding-bottom")};
_.zm=function(){var a=0;self.innerHeight?a=self.innerHeight:document.documentElement&&document.documentElement.clientHeight?a=document.documentElement.clientHeight:document.body&&(a=document.body.clientHeight);var b=document.body,c=document.documentElement;if(document.compatMode==="CSS1Compat"&&c.scrollHeight)return c.scrollHeight!==a?c.scrollHeight:c.offsetHeight;if(navigator.userAgent.indexOf("AppleWebKit")>=0)return ym();if(b&&c){var d=c.scrollHeight,e=c.offsetHeight;c.clientHeight!==e&&(d=b.scrollHeight,
e=b.offsetHeight);return d>a?d>e?d:e:d<e?d:e}};
var Am=function(a,b){return _.fi(a,b,!0)},Bm=function(a){this.T=a||{}},Cm=function(a){var b=function(c){return new (a().Context)(c)};b.prototype.addOnConnectHandler=function(c,d,e,f){return a().Context.prototype.addOnConnectHandler.apply(this,[c,d,e,f])};b.prototype.addOnOpenerHandler=function(c,d,e){return a().Context.prototype.addOnOpenerHandler.apply(this,[c,d,e])};b.prototype.closeSelf=function(c,d,e){return a().Context.prototype.closeSelf.apply(this,[c,d,e])};b.prototype.connectIframes=function(c,
d){a().Context.prototype.connectIframes.apply(this,[c,d])};b.prototype.getFrameName=function(){return a().Context.prototype.getFrameName.apply(this)};b.prototype.getGlobalParam=function(c){a().Context.prototype.getGlobalParam.apply(this,[c])};b.prototype.getParentIframe=function(){return a().Context.prototype.getParentIframe.apply(this)};b.prototype.getWindow=function(){return a().Context.prototype.getWindow.apply(this)};b.prototype.isDisposed=function(){return a().Context.prototype.isDisposed.apply(this)};
b.prototype.open=function(c,d){return a().Context.prototype.open.apply(this,[c,d])};b.prototype.openChild=function(c){return a().Context.prototype.openChild.apply(this,[c])};b.prototype.ready=function(c,d,e,f){a().Context.prototype.ready.apply(this,[c,d,e,f])};b.prototype.removeOnConnectHandler=function(c){a().Context.prototype.removeOnConnectHandler.apply(this,[c])};b.prototype.restyleSelf=function(c,d,e){return a().Context.prototype.restyleSelf.apply(this,[c,d,e])};b.prototype.setCloseSelfFilter=
function(c){a().Context.prototype.setCloseSelfFilter.apply(this,[c])};b.prototype.setGlobalParam=function(c,d){a().Context.prototype.setGlobalParam.apply(this,[c,d])};b.prototype.setRestyleSelfFilter=function(c){a().Context.prototype.setRestyleSelfFilter.apply(this,[c])};return b},Dm=function(a){var b=function(c,d,e,f){return new (a().Iframe)(c,d,e,f)};b.prototype.applyIframesApi=function(c){a().Iframe.prototype.applyIframesApi(c)};b.prototype.close=function(c,d){return a().Iframe.prototype.close.apply(this,
[c,d])};b.prototype.getContext=function(){return a().Iframe.prototype.getContext.apply(this,[])};b.prototype.getFrameName=function(){return a().Iframe.prototype.getFrameName.apply(this,[])};b.prototype.getId=function(){return a().Iframe.prototype.getId.apply(this,[])};b.prototype.getIframeEl=function(){return a().Iframe.prototype.getIframeEl.apply(this,[])};b.prototype.getOrigin=function(){return a().Iframe.prototype.getOrigin.apply(this,[])};b.prototype.getParam=function(c){a().Iframe.prototype.getParam.apply(this,
[c])};b.prototype.getSiteEl=function(){return a().Iframe.prototype.getSiteEl.apply(this,[])};b.prototype.getWindow=function(){return a().Iframe.prototype.getWindow.apply(this,[])};b.prototype.isDisposed=function(){return a().Iframe.prototype.isDisposed.apply(this,[])};b.prototype.ping=function(c,d){return a().Iframe.prototype.ping.apply(this,[c,d])};b.prototype.register=function(c,d,e){a().Iframe.prototype.register.apply(this,[c,d,e])};b.prototype.registerWasClosed=function(c,d){a().Iframe.prototype.registerWasClosed.apply(this,
[c,d])};b.prototype.registerWasRestyled=function(c,d){a().Iframe.prototype.registerWasRestyled.apply(this,[c,d])};b.prototype.restyle=function(c,d){return a().Iframe.prototype.restyle.apply(this,[c,d])};b.prototype.send=function(c,d,e,f){return a().Iframe.prototype.send.apply(this,[c,d,e,f])};b.prototype.setParam=function(c,d){a().Iframe.prototype.setParam.apply(this,[c,d])};b.prototype.setSiteEl=function(c){a().Iframe.prototype.setSiteEl.apply(this,[c])};b.prototype.unregister=function(c,d){a().Iframe.prototype.unregister.apply(this,
[c,d])};return b},Em,Fm,Im,Km,Mm,Rm,Zm,$m,bn,fn,gn,kn,mn,nn,pn,on,qn;_.ck.prototype.Fz=_.pb(5,function(){return this.T.controller});_.ck.prototype.Wr=_.pb(4,function(a){this.T.apis=a;return this});Em=function(a,b){a.T.onload=b};Fm=function(a){return a.T.rpctoken};_.Gm=function(a,b){a.T.queryParams=b;return a};_.Hm=function(a,b){a.T.relayOpen=b;return a};Im=function(a){return a.T.apis};_.Jm=function(a,b){a.T.onClose=b;return a};Km=function(a,b){a.T.controllerData=b};
_.Lm=function(a){a.T.waitForOnload=!0};Mm=function(a){return(a=a.T.timeout)?a:null};_.Nm=function(a){return!!a&&typeof a==="object"&&_.Ae.test(a.push)};_.Om=function(a){for(var b=0;b<this.length;b++)if(this[b]===a)return b;return-1};_.Pm=function(a,b,c){if(a){_.Kl(_.Nm(a),"arrayForEach was called with a non array value");for(var d=0;d<a.length;d++)b.call(c,a[d],d)}};
_.Qm=function(a,b,c){if(a)if(_.Nm(a))_.Pm(a,b,c);else{_.Kl(typeof a==="object","objectForEach was called with a non object value");c=c||a;for(var d in a)_.De(a,d)&&a[d]!==void 0&&b.call(c,a[d],d)}};Rm=function(a){this.T=a||{}};Rm.prototype.value=function(){return this.T};Rm.prototype.getIframe=function(){return this.T.iframe};var Sm=function(a,b){a.T.role=b;return a},Tm=function(a,b){a.T.data=b;return a};Rm.prototype.Vk=function(a){this.T.setRpcReady=a;return this};var Um=function(a){return a.T.setRpcReady};
Rm.prototype.Zm=function(a){this.T.rpctoken=a;return this};var Vm=function(a){a.T.selfConnect=!0;return a};Bm.prototype.value=function(){return this.T};var Xm=function(a){var b=new Wm;b.T.role=a;return b};Bm.prototype.QT=function(){return this.T.role};Bm.prototype.Fc=function(a){this.T.handler=a;return this};Bm.prototype.wb=function(){return this.T.handler};var Ym=function(a,b){a.T.filter=b;return a};Bm.prototype.Wr=function(a){this.T.apis=a;return this};bn=/^[\w\.\-]*$/;
_.cn=function(a){return a.getOrigin()===a.getContext().getOrigin()};_.dn=function(){return!0};_.en=function(a){for(var b=_.Ce(),c=0;c<a.length;c++)b[a[c]]=!0;return function(d){return!!b[d.Cd]}};fn=function(a,b,c){a=Zm[a];if(!a)return[];for(var d=[],e=0;e<a.length;e++)d.push(_.Bk(a[e].call(c,b,c)));return d};gn=function(a,b,c){return function(d){if(!b.isDisposed()){var e=this.origin,f=b.getOrigin();_.Kl(e===f,"Wrong origin "+e+" != "+f);e=this.callback;d=fn(a,d,b);!c&&d.length>0&&_.Fk(d).then(e)}}};
_.hn=function(a,b,c){_.Kl(a!="_default","Cannot update default api");$m[a]={map:b,filter:c}};_.jn=function(a,b,c){_.Kl(a!="_default","Cannot update default api");_.Be($m,a,{map:{},filter:_.cn}).map[b]=c};kn=function(a,b){_.Be($m,"_default",{map:{},filter:_.dn}).map[a]=b;_.Qm(_.an.dg,function(c){c.register(a,b,_.dn)})};_.ln=function(){return _.an};mn=/^https?:\/\/[^\/%\\?#\s]+$/i;
nn={longdesc:!0,name:!0,src:!0,frameborder:!0,marginwidth:!0,marginheight:!0,scrolling:!0,align:!0,height:!0,width:!0,id:!0,"class":!0,title:!0,tabindex:!0,hspace:!0,vspace:!0,allowtransparency:!0};pn=function(a){this.resolve=this.reject=null;this.promise=_.Uk((0,_.z)(function(b,c){this.resolve=b;this.reject=c},this));a&&(this.promise=on(this.promise,a))};on=function(a,b){return a.then(function(c){try{b(c)}catch(d){}return c})};qn=function(a){this.ig=a;this.Context=Cm(a);this.Iframe=Dm(a)};_.g=qn.prototype;
_.g.CROSS_ORIGIN_IFRAMES_FILTER=function(a){return this.ig().CROSS_ORIGIN_IFRAMES_FILTER(a)};_.g.SAME_ORIGIN_IFRAMES_FILTER=function(a){return this.ig().SAME_ORIGIN_IFRAMES_FILTER(a)};_.g.create=function(a,b,c){return this.ig().create(a,b,c)};_.g.getBeforeOpenStyle=function(a){return this.ig().getBeforeOpenStyle(a)};_.g.getContext=function(){return this.ig().getContext()};_.g.getStyle=function(a){return this.ig().getStyle(a)};_.g.makeWhiteListIframesFilter=function(a){return this.ig().makeWhiteListIframesFilter(a)};
_.g.registerBeforeOpenStyle=function(a,b){return this.ig().registerBeforeOpenStyle(a,b)};_.g.registerIframesApi=function(a,b,c){return this.ig().registerIframesApi(a,b,c)};_.g.registerIframesApiHandler=function(a,b,c){return this.ig().registerIframesApiHandler(a,b,c)};_.g.registerStyle=function(a,b){return this.ig().registerStyle(a,b)};var rn=function(){this.yi=[]};rn.prototype.ig=function(a){return this.yi.length?sn(this.yi[0],a):void 0};var sn=function(a,b){b=b===void 0?function(c){return new c}:b;return a.ctor?b(a.ctor):a.instance},tn=function(){rn.apply(this,arguments)};_.y(tn,rn);var vn=function(a){var b=un.TQ,c=a.priority,d=~Am(b.yi,function(e){return e.priority<c?-1:1});b.yi.splice(d,0,a)};var un=new function(){var a=this;this.TQ=new tn;this.instance=new qn(function(){return a.TQ.ig()()})};vn({instance:function(){return window.gapi.iframes},priority:1});_.wn=un.instance;var xn,yn;xn={height:!0,width:!0};yn=/^(?!-*(?:expression|(?:moz-)?binding))(?:[.#]?-?(?:[_a-z0-9-]+)(?:-[_a-z0-9-]+)*-?|-?(?:[0-9]+(?:\.[0-9]*)?|\.[0-9]+)(?:[a-z]{1,2}|%)?|!important|)$/i;_.zn=function(a){typeof a==="number"&&(a=String(a)+"px");return a};var An=function(){Rm.apply(this,arguments)};_.y(An,Rm);var Wm=function(){Bm.apply(this,arguments)};_.y(Wm,Bm);var Bn=function(){_.ck.apply(this,arguments)};_.y(Bn,_.ck);var Cn=function(a){Bn.call(this,a)};_.y(Cn,Bn);var Dn=function(a,b){a.T.frameName=b;return a};Cn.prototype.getFrameName=function(){return this.T.frameName};var En=function(a,b){a.T.rpcAddr=b;return a};Cn.prototype.og=function(){return this.T.rpcAddr};var Fn=function(a,b){a.T.retAddr=b;return a};Cn.prototype.Zh=function(){return this.T.retAddr};Cn.prototype.Mj=function(a){this.T.origin=a;return this};Cn.prototype.getOrigin=function(){return this.T.origin};
Cn.prototype.Vk=function(a){this.T.setRpcReady=a;return this};var Gn=function(a){return a.T._popupWindow};Cn.prototype.wp=function(a){this.T.context=a};var Hn=function(a,b){a.T._rpcReadyFn=b};Cn.prototype.getIframeEl=function(){return this.T.iframeEl};var In=function(a,b,c){var d=a.og(),e=b.Zh();Fn(En(c,a.Zh()+"/"+b.og()),e+"/"+d);Dn(c,b.getFrameName()).Mj(b.getOrigin())};var Kn=function(a,b,c){a.setTimeout(function(){b.closed||c==5?Jn(b):(b.close(),c++,Kn(a,b,c))},1E3)},Jn=function(a){a.closed||a.document&&a.document.body&&_.ve(a.document.body,"Please close this window.")};_.Ln=function(a,b,c,d){this.Lg=!1;this.qb=a;this.DK=b;this.uq=c;this.Ka=d;this.DZ=this.Ka.Zh();this.Cd=this.Ka.getOrigin();this.Bba=this.Ka.getIframeEl();this.u0=this.Ka.T.where;this.yi=[];this.applyIframesApi("_default");a=Im(this.Ka)||[];for(b=0;b<a.length;b++)this.applyIframesApi(a[b]);this.qb.dg[c]=this};_.g=_.Ln.prototype;_.g.isDisposed=function(){return this.Lg};
_.g.dispose=function(){if(!this.isDisposed()){for(var a=0;a<this.yi.length;a++)this.unregister(this.yi[a]);delete _.an.dg[this.getFrameName()];this.Lg=!0}};_.g.getContext=function(){return this.qb};_.g.getOptions=function(){return this.Ka};_.g.og=function(){return this.DK};_.g.Zh=function(){return this.DZ};_.g.getFrameName=function(){return this.uq};_.g.getIframeEl=function(){return this.Bba};_.g.getSiteEl=function(){return this.u0};_.g.setSiteEl=function(a){this.u0=a};_.g.Vk=function(){(0,this.Ka.T._rpcReadyFn)()};
_.g.setParam=function(a,b){this.Ka.value()[a]=b};_.g.getParam=function(a){return this.Ka.value()[a]};_.g.wc=function(){return this.Ka.value()};_.g.getId=function(){return this.Ka.getId()};_.g.getOrigin=function(){return this.Cd};var Mn=function(a,b){var c=a.uq;a=a.qb.getFrameName();return c+":"+a+":"+b};_.g=_.Ln.prototype;
_.g.register=function(a,b,c){_.Kl(!this.isDisposed(),"Cannot register handler on disposed iframe "+a);_.Kl((c||_.cn)(this),"Rejecting untrusted message "+a);c=Mn(this,a);_.Be(Zm,c,[]).push(b)==1&&(this.yi.push(a),_.tl(c,gn(c,this,a==="_g_wasClosed")))};_.g.unregister=function(a,b){var c=Mn(this,a),d=Zm[c];d&&(b?(b=_.Om.call(d,b),b>=0&&d.splice(b,1)):d.splice(0,d.length),d.length==0&&(b=_.Om.call(this.yi,a),b>=0&&this.yi.splice(b,1),_.ul(c)))};_.g.B$=function(){return this.yi};
_.g.applyIframesApi=function(a){this.HE=this.HE||[];if(!(_.Om.call(this.HE,a)>=0)){this.HE.push(a);a=$m[a]||{map:{}};for(var b in a.map)_.De(a.map,b)&&this.register(b,a.map[b],a.filter)}};_.g.getWindow=function(){if(!_.cn(this))return null;var a=Gn(this.Ka);if(a)return a;var b=this.DK.split("/");a=this.getContext().getWindow();for(var c=0;c<b.length&&a;c++){var d=b[c];a=".."===d?a==a.parent?a.opener:a.parent:a.frames[d]}return a};
var Nn=function(a){var b={};if(a)for(var c in a)_.De(a,c)&&_.De(xn,c)&&yn.test(a[c])&&(b[c]=a[c]);return b};_.g=_.Ln.prototype;_.g.close=function(a,b){return On(this,"_g_close",a,b)};_.g.restyle=function(a,b){return On(this,"_g_restyle",a,b)};_.g.Qr=function(a,b){return On(this,"_g_restyleDone",a,b)};_.g.X7=function(a){return this.getContext().closeSelf(a,void 0,this)};_.g.ifa=function(a){if(a&&typeof a==="object")return this.getContext().restyleSelf(a,void 0,this)};
_.g.jfa=function(a){var b=this.Ka.T.onRestyle;b&&b.call(this,a,this);a=a&&typeof a==="object"?Nn(a):{};(b=this.getIframeEl())&&a&&typeof a==="object"&&(_.De(a,"height")&&(a.height=_.zn(a.height)),_.De(a,"width")&&(a.width=_.zn(a.width)),_.Ee(a,b.style))};
_.g.Y7=function(a){var b=this.Ka.T.onClose;b&&b.call(this,a,this);if(b=Gn(this.getOptions())){var c=this.getContext().getWindow().document.getElementById(this.getId());c&&c.parentNode&&c.parentNode.removeChild(c);c=this.getContext().getWindow();_.Dd&&_.Dh&&c?(c.focus(),Kn(c,b,0)):(b.close(),Jn(b))}b||(b=this.getIframeEl())&&b.parentNode&&b.parentNode.removeChild(b);if(b=this.Ka.Fz())c={},c.frameName=this.getFrameName(),On(b,"_g_disposeControl",c);b=Mn(this,"_g_wasClosed");fn(b,a,this)};
_.g.registerWasRestyled=function(a,b){this.register("_g_wasRestyled",a,b)};_.g.registerWasClosed=function(a,b){this.register("_g_wasClosed",a,b)};_.g.Mha=function(){delete this.getContext().dg[this.getFrameName()];this.getContext().getWindow().setTimeout((0,_.z)(function(){this.dispose()},this),0)};
_.g.send=function(a,b,c,d){_.Kl(!this.isDisposed(),"Cannot send message to disposed iframe - "+a);_.Kl((d||_.cn)(this),"Wrong target for message "+a);c=new pn(c);a=this.qb.getFrameName()+":"+this.uq+":"+a;_.Cl(this.DK,a,c.resolve,b);return c.promise};var On=function(a,b,c,d){return a.send(b,c,d,_.dn)};_.g=_.Ln.prototype;_.g.iea=function(a){return a};_.g.ping=function(a,b){return On(this,"_g_ping",b,a)};
_.g.h8=function(a){a=a&&typeof a==="object"?a:{};for(var b=a.rpcAddr,c=(this.og()+"/"+b).split("/"),d=this.getContext().getWindow(),e;(e=c.shift())&&d;)d=e==".."?d.parent:d.frames[e];_.Kl(!!d,"Bad rpc address "+b);a._window=d;a._parentRpcAddr=this.og();a._parentRetAddr=this.Zh();this.getContext();b=new _.Pn(a);this.wda&&this.wda(b,a.controllerData);this.tF=this.tF||[];this.tF.push(b,a.controllerData)};
_.g.x8=function(a){a=(a||{}).frameName;for(var b=this.tF||[],c=0;c<b.length;c++)if(b[c].getFrameName()===a){a=b.splice(c,1)[0];a.dispose();this.Ada&&this.Ada(a);return}_.Kl(!1,"Unknown contolled iframe to dispose - "+a)};
_.g.e8=function(a){var b=new Cn(a);a=new An(b.value());if(a.T.selfConnect)var c=this;else(_.Kl(mn.test(b.getOrigin()),"Illegal origin for connected iframe - "+b.getOrigin()),c=this.getContext().dg[b.getFrameName()],c)?Um(b)&&(c.Vk(),On(c,"_g_rpcReady")):(b=Dn(Fn(En(new Cn,b.og()),b.Zh()).Mj(b.getOrigin()),b.getFrameName()).Vk(Um(b)).Zm(Fm(b)),c=this.getContext().attach(b.value()));b=this.getContext();var d=a.T.role;a=a.T.data;Qn(b);d=d||"";_.Be(b.rF,d,[]).push({ef:c,data:a});Rn(c,a,b.wJ[d])};
_.g.iM=function(a,b){(new Cn(b)).T._relayedDepth||(b={},Vm(Sm(new An(b),"_opener")),On(a,"_g_connect",b))};
_.g.VX=function(a){var b=this,c=a.T.messageHandlers,d=a.T.messageHandlersFilter,e=a.T.onClose;_.Jm(_.ek(_.dk(a,null),null),null);return On(this,"_g_open",a.value()).then(function(f){var h=new Cn(f[0]),k=h.getFrameName();f=new Cn;var l=b.Zh(),m=h.Zh();Fn(En(f,b.og()+"/"+h.og()),m+"/"+l);Dn(f,k);f.Mj(h.getOrigin());f.Wr(Im(h));f.Zm(Fm(a));_.dk(f,c);_.ek(f,d);_.Jm(f,e);(h=b.getContext().dg[k])||(h=b.getContext().attach(f.value()));return h})};
_.g.GK=function(a){var b=a.getUrl();_.Kl(!b||_.am.test(b),"Illegal url for new iframe - "+b);var c=a.Vn().value();b={};for(var d in c)_.De(c,d)&&_.De(nn,d)&&(b[d]=c[d]);_.De(c,"style")&&(d=c.style,typeof d==="object"&&(b.style=Nn(d)));a.value().attributes=b};
_.g.Sda=function(a){a=new Cn(a);this.GK(a);var b=a.T._relayedDepth||0;a.T._relayedDepth=b+1;a.T.openerIframe=this;var c=Fm(a);a.Zm(null);var d=this;return this.getContext().open(a.value()).then(function(e){var f=Im(new Cn(e.wc())),h=new Cn;In(e,d,h);b==0&&Sm(new An(h.value()),"_opener");h.Vk(!0);h.Zm(c);On(e,"_g_connect",h.value());h=new Cn;Dn(Fn(En(h,e.og()),e.DZ),e.getFrameName()).Mj(e.getOrigin()).Wr(f);return h.value()})};
_.g.hfa=function(a){this.getContext().addOnOpenerHandler(function(b){b.send("_g_wasRestyled",a,void 0,_.dn)},null,_.dn)};var Wn;_.Sn=_.Ce();_.Tn=_.Ce();_.Un=function(a,b){_.Sn[a]=b};_.Vn=function(a){return _.Sn[a]};Wn=function(a,b){_.Fe.load("gapi.iframes.style."+a,b)};_.Xn=function(a,b){_.Tn[a]=b};_.Yn=function(a){return _.Tn[a]};_.Pn=function(a){a=a||{};this.Lg=!1;this.wi=_.Ce();this.dg=_.Ce();this.Vf=a._window||_.xe;this.Hd=this.Vf.location.href;this.mY=(this.QJ=Zn(this.Hd,"parent"))?Zn(this.Hd,"pfname"):"";this.Da=this.QJ?Zn(this.Hd,"_gfid")||Zn(this.Hd,"id"):"";this.uq=_.nm(this.Da,this.mY);this.Cd=_.Ig(this.Hd);if(this.Da){var b=new Cn;En(b,a._parentRpcAddr||"..");Fn(b,a._parentRetAddr||this.Da);b.Mj(_.Ig(this.QJ||this.Hd));Dn(b,this.mY);this.Gb=this.attach(b.value())}else this.Gb=null};_.g=_.Pn.prototype;
_.g.isDisposed=function(){return this.Lg};_.g.dispose=function(){if(!this.isDisposed()){for(var a=_.Aa(Object.values(this.dg)),b=a.next();!b.done;b=a.next())b.value.dispose();this.Lg=!0}};_.g.getFrameName=function(){return this.uq};_.g.getOrigin=function(){return this.Cd};_.g.getWindow=function(){return this.Vf};_.g.ub=function(){return this.Vf.document};_.g.setGlobalParam=function(a,b){this.wi[a]=b};_.g.getGlobalParam=function(a){return this.wi[a]};
_.g.attach=function(a){_.Kl(!this.isDisposed(),"Cannot attach iframe in disposed context");a=new Cn(a);a.og()||En(a,a.getId());a.Zh()||Fn(a,"..");a.getOrigin()||a.Mj(_.Ig(a.getUrl()));a.getFrameName()||Dn(a,_.nm(a.getId(),this.uq));var b=a.getFrameName();if(this.dg[b])return this.dg[b];var c=a.og(),d=c;a.getOrigin()&&(d=c+"|"+a.getOrigin());var e=a.Zh(),f=Fm(a);f||(f=(f=a.getIframeEl())&&(f.getAttribute("data-postorigin")||f.src)||a.getUrl(),f=_.Ge(f,"rpctoken"));Hn(a,_.Jl(d,e,f,Gn(a)));d=((window.gadgets||
{}).rpc||{}).setAuthToken;f&&d&&d(c,f);var h=new _.Ln(this,c,b,a),k=a.T.messageHandlersFilter;_.Qm(a.T.messageHandlers,function(l,m){h.register(m,l,k)});Um(a)&&h.Vk();On(h,"_g_rpcReady");return h};_.g.GK=function(a){Dn(a,null);var b=a.getId();!b||bn.test(b)&&!this.getWindow().document.getElementById(b)||(_.Vf.log("Ignoring requested iframe ID - "+b),a.Me(null))};var Zn=function(a,b){var c=_.Ge(a,b);c||(c=_.Qf(_.Ge(a,"jcp",""))[b]);return c||""};
_.Pn.prototype.openChild=function(a){_.Kl(!this.isDisposed(),"Cannot open iframe in disposed context");var b=new Cn(a);$n(this,b);var c=b.getFrameName();if(c&&this.dg[c])return this.dg[c];this.GK(b);c=b.getUrl();_.Kl(c,"No url for new iframe");var d=b.T.queryParams||{};d.usegapi="1";_.Gm(b,d);d=this.GU&&this.GU(c,b);d||(d=b.T.where,_.Kl(!!d,"No location for new iframe"),c=_.xm(c,d,a),b.T.iframeEl=c,d=c.getAttribute("id"));En(b,d).Me(d);b.Mj(_.Ig(b.T.eurl||""));this.SW&&this.SW(b,b.getIframeEl());
c=this.attach(a);c.iM&&c.iM(c,a);(a=b.T.onCreate)&&a(c);b.T.disableRelayOpen||c.applyIframesApi("_open");return c};
var ao=function(a,b,c){var d=b.T.canvasUrl;if(!d)return c;_.Kl(!b.T.allowPost&&!b.T.forcePost,"Post is not supported when using canvas url");var e=b.getUrl();_.Kl(e&&_.Ig(e)===a.Cd&&_.Ig(d)===a.Cd,"Wrong origin for canvas or hidden url "+d);b.setUrl(d);_.Lm(b);b.T.canvasUrl=null;return function(f){var h=f.getWindow(),k=h.location.hash;k=_.wm(e)+(/#/.test(e)?k.replace(/^#/,"&"):k);h.location.replace(k);c&&c(f)}},bo=function(a,b,c){var d=b.T.relayOpen;if(d){var e=a.getParentIframe();d instanceof _.Ln?
(e=d,_.Hm(b,0)):Number(d)>0&&_.Hm(b,Number(d)-1);if(e){_.Kl(!!e.VX,"Relaying iframe open is disabled");if(d=b.getStyle())if(d=_.Tn[d])b.wp(a),d(b.value()),b.wp(null);b.T.openerIframe=null;c.resolve(e.VX(b));return!0}}return!1},co=function(a,b,c){var d=b.getStyle();if(d)if(_.Kl(!!_.Vn,"Defer style is disabled, when requesting style "+d),_.Sn[d])$n(a,b);else return Wn(d,function(){_.Kl(!!_.Sn[d],"Fail to load style - "+d);c.resolve(a.open(b.value()))}),!0;return!1};
_.Pn.prototype.open=function(a,b){_.Kl(!this.isDisposed(),"Cannot open iframe in disposed context");var c=new Cn(a);b=ao(this,c,b);var d=new pn(b);(b=c.getUrl())&&c.setUrl(_.wm(b));if(bo(this,c,d)||co(this,c,d)||bo(this,c,d))return d.promise;if(Mm(c)!=null){var e=setTimeout(function(){h.getIframeEl().src="about:blank";d.reject({timeout:"Exceeded time limit of :"+Mm(c)+"milliseconds"})},Mm(c)),f=d.resolve;d.resolve=function(k){clearTimeout(e);f(k)}}c.T.waitForOnload&&Em(c.Vn(),function(){d.resolve(h)});
var h=this.openChild(a);c.T.waitForOnload||d.resolve(h);return d.promise};_.Pn.prototype.getParentIframe=function(){return this.Gb};var eo=function(a,b){var c=a.getParentIframe(),d=!0;b.filter&&(d=b.filter.call(b.ef,b.params));return _.Bk(d).then(function(e){return e&&c?(b.kY&&b.kY.call(a,b.params),e=b.sender?b.sender(b.params):On(c,b.message,b.params),b.Kha?e.then(function(){return!0}):!0):!1})};_.g=_.Pn.prototype;
_.g.closeSelf=function(a,b,c){a=eo(this,{sender:function(d){var e=_.an.getParentIframe();_.Qm(_.an.dg,function(f){f!==e&&On(f,"_g_wasClosed",d)});return On(e,"_g_closeMe",d)},message:"_g_closeMe",params:a,ef:c,filter:this.getGlobalParam("onCloseSelfFilter")});b=new pn(b);b.resolve(a);return b.promise};_.g.restyleSelf=function(a,b,c){a=a||{};b=new pn(b);b.resolve(eo(this,{message:"_g_restyleMe",params:a,ef:c,filter:this.getGlobalParam("onRestyleSelfFilter"),Kha:!0,kY:this.B1}));return b.promise};
_.g.B1=function(a){a.height==="auto"&&(a.height=_.zm())};_.g.setCloseSelfFilter=function(a){this.setGlobalParam("onCloseSelfFilter",a)};_.g.setRestyleSelfFilter=function(a){this.setGlobalParam("onRestyleSelfFilter",a)};var $n=function(a,b){var c=b.getStyle();if(c){b.Ei(null);var d=_.Sn[c];_.Kl(d,"No such style: "+c);b.wp(a);d(b.value());b.wp(null)}};
_.Pn.prototype.ready=function(a,b,c,d){var e=b||{},f=this.getParentIframe();this.addOnOpenerHandler(function(k){_.Qm(e,function(l,m){k.register(m,l,d)},this);k!==f&&k.send("_ready",h,void 0,d)},void 0,d);var h=a||{};h.height=h.height||"auto";this.B1(h);f&&f.send("_ready",h,c,_.dn)};
_.Pn.prototype.connectIframes=function(a,b){a=new An(a);var c=new An(b),d=Um(a);b=a.getIframe();var e=c.getIframe();if(e){var f=Fm(a),h=new Cn;In(b,e,h);Tm(Sm((new An(h.value())).Zm(f),a.T.role),a.T.data).Vk(d);var k=new Cn;In(e,b,k);Tm(Sm((new An(k.value())).Zm(f),c.T.role),c.T.data).Vk(!0);On(b,"_g_connect",h.value(),function(){d||On(e,"_g_connect",k.value())});d&&On(e,"_g_connect",k.value())}else c={},Tm(Sm(Vm(new An(c)),a.T.role),a.T.data),On(b,"_g_connect",c)};
var Qn=function(a){a.rF||(a.rF=_.Ce(),a.wJ=_.Ce())};_.Pn.prototype.addOnConnectHandler=function(a,b,c,d){Qn(this);typeof a==="object"?(b=new Wm(a),c=b.QT()||""):(b=Ym(Xm(a).Fc(b).Wr(c),d),c=a);d=this.rF[c]||[];a=!1;for(var e=0;e<d.length&&!a;e++)Rn(this.dg[d[e].ef.getFrameName()],d[e].data,[b]),a=b.T.runOnce;c=_.Be(this.wJ,c,[]);a||b.T.dontWait||c.push(b)};
_.Pn.prototype.removeOnConnectHandler=function(a,b){a=_.Be(this.wJ,a,[]);if(b)for(var c=!1,d=0;!c&&d<a.length;d++)a[d].wb()===b&&(c=!0,a.splice(d,1));else a.splice(0,a.length)};var Rn=function(a,b,c){c=c||[];for(var d=0;d<c.length;d++){var e=c[d];if(e&&a){var f=e.T.filter||_.cn;if(a&&f(a)){f=Im(e)||[];for(var h=0;h<f.length;h++)a.applyIframesApi(f[h]);e.wb()&&e.wb()(a,b);e.T.runOnce&&(c.splice(d,1),--d)}}}};
_.Pn.prototype.addOnOpenerHandler=function(a,b,c){var d=this.addOnConnectHandler;a=Ym(Xm("_opener").Fc(a).Wr(b),c);a.T.runOnce=!0;d.call(this,a.value())};_.Pn.prototype.SW=function(a,b){var c=a.Fz();if(c){_.Kl(c.Cd===a.getOrigin(),"Wrong controller origin "+this.Cd+" !== "+a.getOrigin());var d=a.og();En(a,c.og());Fn(a,c.Zh());var e=new Cn;Km(En(e,d),a.T.controllerData);_.Le(b,"load",function(){c.send("_g_control",e.value())})}};
var fo=function(a,b,c){a=a.getWindow();var d=a.document,e=c.T.reuseWindow;if(e){var f=c.getId();if(!f)throw Error("G");}else f=_.mm(d,c);var h=f,k=c.T.rpcRelayUrl;if(k){k=_.vm(k);h=c.T.fragmentParams||{};h.rly=f;c.T.fragmentParams=h;h=c.T.where||d.body;_.Kl(!!h,"Cannot open window in a page with no body");var l={};l.src=k;l.style="display:none;";l.id=f;l.name=f;_.qm(d,h,l,f);h=f+"_relay"}b=_.wm(b);var m=_.om(d,b,f,c.value());c.T.eurl=m;b=c.T.openAsWindow;typeof b!=="string"&&(b=void 0);c=window.navigator.userAgent||
"";/Trident|MSIE/i.test(c)&&/#/.test(c)&&(m="javascript:window.location.replace("+_.xe.JSON.stringify(m).replace(/#/g,"\\x23")+")");if(e){var n=e;setTimeout(function(){n.location.replace(m)})}else n=_.Fc(a,m,h,b);return{id:f,c2:n}};_.Pn.prototype.GU=function(a,b){if(b.T.openAsWindow){a=fo(this,a,b);var c=a.id;_.Kl(!!a.c2,"Open popup window failed");b.T._popupWindow=a.c2}return c};Zm=_.Ce();$m=_.Ce();_.an=new _.Pn;kn("_g_rpcReady",_.Ln.prototype.Vk);kn("_g_discover",_.Ln.prototype.B$);kn("_g_ping",_.Ln.prototype.iea);kn("_g_close",_.Ln.prototype.X7);kn("_g_closeMe",_.Ln.prototype.Y7);kn("_g_restyle",_.Ln.prototype.ifa);kn("_g_restyleMe",_.Ln.prototype.jfa);kn("_g_wasClosed",_.Ln.prototype.Mha);_.jn("control","_g_control",_.Ln.prototype.h8);_.jn("control","_g_disposeControl",_.Ln.prototype.x8);var go=_.an.getParentIframe();
go&&go.register("_g_restyleDone",_.Ln.prototype.hfa,_.dn);kn("_g_connect",_.Ln.prototype.e8);var ho={};ho._g_open=_.Ln.prototype.Sda;_.hn("_open",ho,_.dn);var io={Context:_.Pn,Iframe:_.Ln,SAME_ORIGIN_IFRAMES_FILTER:_.cn,CROSS_ORIGIN_IFRAMES_FILTER:_.dn,makeWhiteListIframesFilter:_.en,getContext:_.ln,registerIframesApi:_.hn,registerIframesApiHandler:_.jn,registerStyle:_.Un,registerBeforeOpenStyle:_.Xn,getStyle:_.Vn,getBeforeOpenStyle:_.Yn,create:_.xm};vn({instance:function(){return io},priority:2});_.jn("gapi.load","_g_gapi.load",function(a){return new _.xk(function(b){_.Fe.load(a&&typeof a==="object"&&a.features||"",b)})});
_.to=function(a,b){a.T.where=b;return a};_.uo=function(){_.ck.apply(this,arguments)};_.y(_.uo,_.ck);
_.vo=_.Ce();
_.Eo={};window.iframer=_.Eo;
var Go=function(a){var b=[new Fo];if(b.length===0)throw Error("j");if(b.map(function(c){if(c instanceof Fo)c=c.MY;else throw Error("j");return c}).every(function(c){return"data-gapiscan".indexOf(c)!==0}))throw Error("k`data-gapiscan");a.setAttribute("data-gapiscan","true")},Fo=function(){this.MY=Ho[0].toLowerCase()},Io,Jo,Ko,Lo,Mo,Qo,Ro;Fo.prototype.toString=function(){return this.MY};Io=function(a){if(_.Ae.test(Object.keys))return Object.keys(a);var b=[],c;for(c in a)_.De(a,c)&&b.push(c);return b};
Jo={button:!0,div:!0,span:!0};Ko=function(a){var b=_.Be(_.Me,"sws",[]);return _.Om.call(b,a)>=0};Lo=function(a){return _.Be(_.Me,"watt",_.Ce())[a]};Mo=function(a){return function(b,c){return a?_.tm()[c]||a[c]||"":_.tm()[c]||""}};_.No={callback:1,clientid:1,cookiepolicy:1,openidrealm:-1,includegrantedscopes:-1,requestvisibleactions:1,scope:1};_.Oo=!1;
_.Po=function(){if(!_.Oo){for(var a=document.getElementsByTagName("meta"),b=0;b<a.length;++b){var c=a[b].name.toLowerCase();if(_.wc(c,"google-signin-")){c=c.substring(14);var d=a[b].content;_.No[c]&&d&&(_.vo[c]=d)}}if(window.self!==window.top){a=document.location.toString();for(var e in _.No)_.No[e]>0&&(b=_.Ge(a,e,""))&&(_.vo[e]=b)}_.Oo=!0}e=_.Ce();_.Ee(_.vo,e);return e};Qo=function(a){var b;a.match(/^https?%3A/i)&&(b=decodeURIComponent(a));a=b?b:a;return _.$l(document,a)};
Ro=function(a){a=a||"canonical";for(var b=document.getElementsByTagName("link"),c=0,d=b.length;c<d;c++){var e=b[c],f=e.getAttribute("rel");if(f&&f.toLowerCase()==a&&(e=e.getAttribute("href"))&&(e=Qo(e))&&e.match(/^https?:\/\/[\w\-_\.]+/i)!=null)return e}return window.location.href};_.So=function(){return window.location.origin||window.location.protocol+"//"+window.location.host};_.To=function(a,b,c,d){return(a=typeof a=="string"?a:void 0)?Qo(a):Ro(d)};
_.Uo=function(a,b,c){a==null&&c&&(a=c.db,a==null&&(a=c.gwidget&&c.gwidget.db));return a||void 0};_.Vo=function(a,b,c){a==null&&c&&(a=c.ecp,a==null&&(a=c.gwidget&&c.gwidget.ecp));return a||void 0};_.Wo=function(a,b,c){return _.To(a,b,c,b.action?void 0:"publisher")};var Xo,Yo,Zo,$o,ap,bp,dp,cp;Xo={se:"0"};Yo={post:!0};Zo={style:"position:absolute;top:-10000px;width:450px;margin:0px;border-style:none"};$o="onPlusOne _ready _close _open _resizeMe _renderstart oncircled drefresh erefresh".split(" ");ap=_.Be(_.Me,"WI",_.Ce());bp=["style","data-gapiscan"];
dp=function(a){for(var b=_.Ce(),c=a.nodeName.toLowerCase().indexOf("g:")!=0,d=a.attributes.length,e=0;e<d;e++){var f=a.attributes[e],h=f.name,k=f.value;_.Om.call(bp,h)>=0||c&&h.indexOf("data-")!=0||k==="null"||"specified"in f&&!f.specified||(c&&(h=h.substr(5)),b[h.toLowerCase()]=k)}a=a.style;(c=cp(a&&a.height))&&(b.height=String(c));(a=cp(a&&a.width))&&(b.width=String(a));return b};
_.fp=function(a,b,c,d,e,f){if(c.rd)var h=b;else h=document.createElement("div"),b.dataset.gapistub=!0,h.style.cssText="position:absolute;width:450px;left:-10000px;",b.parentNode.insertBefore(h,b);f.siteElement=h;h.id||(h.id=_.ep(a));b=_.Ce();b[">type"]=a;_.Ee(c,b);a=_.xm(d,h,e);f.iframeNode=a;f.id=a.getAttribute("id")};_.ep=function(a){_.Be(ap,a,0);return"___"+a+"_"+ap[a]++};cp=function(a){var b=void 0;typeof a==="number"?b=a:typeof a==="string"&&(b=parseInt(a,10));return b};var Ho=_.gd(["data-"]),jp,kp,lp,mp,np=/(?:^|\s)g-((\S)*)(?:$|\s)/,op={plusone:!0,autocomplete:!0,profile:!0,signin:!0,signin2:!0};jp=_.Be(_.Me,"SW",_.Ce());kp=_.Be(_.Me,"SA",_.Ce());lp=_.Be(_.Me,"SM",_.Ce());mp=_.Be(_.Me,"FW",[]);
var pp=function(a,b){return(typeof a==="string"?document.getElementById(a):a)||b},tp=function(a,b){var c;qp.ps0=(new Date).getTime();rp("ps0");a=pp(a,_.ye);var d=_.ye.documentMode;if(a.querySelectorAll&&(!d||d>8)){d=b?[b]:Io(jp).concat(Io(kp)).concat(Io(lp));for(var e=[],f=0;f<d.length;f++){var h=d[f];e.push(".g-"+h,"g\\:"+h)}d=a.querySelectorAll(e.join(","))}else d=a.getElementsByTagName("*");a=_.Ce();for(e=0;e<d.length;e++){f=d[e];var k=f;h=b;var l=k.nodeName.toLowerCase(),m=void 0;if(k.hasAttribute("data-gapiscan"))h=
null;else{var n=l.indexOf("g:");n==0?m=l.substr(2):(n=(n=String(k.className||k.getAttribute("class")))&&np.exec(n))&&(m=n[1]);h=!m||!(jp[m]||kp[m]||lp[m])||h&&m!==h?null:m}h&&(op[h]||f.nodeName.toLowerCase().indexOf("g:")==0||Io(dp(f)).length!=0)&&(Go(f),_.Be(a,h,[]).push(f))}for(p in a)mp.push(p);qp.ps1=(new Date).getTime();rp("ps1");if(b=mp.join(":"))try{_.Fe.load(b,void 0)}catch(q){_.Vf.log(q);return}e=[];for(c in a){d=a[c];var p=0;for(b=d.length;p<b;p++)f=d[p],sp(c,f,dp(f),e,b)}};var up=function(a,b){var c=Lo(a);b&&c?(c(b),(c=b.iframeNode)&&c.setAttribute("data-gapiattached",!0)):_.Fe.load(a,function(){var d=Lo(a),e=b&&b.iframeNode,f=b&&b.userParams;e&&d?(d(b),e.setAttribute("data-gapiattached",!0)):(d=_.Fe[a].go,a=="signin2"?d(e,f):d(e&&e.parentNode,f))})},sp=function(a,b,c,d,e,f,h){switch(vp(b,a,f)){case 0:a=lp[a]?a+"_annotation":a;d={};d.iframeNode=b;d.userParams=c;up(a,d);break;case 1:if(b.parentNode){for(var k in c){if(f=_.De(c,k))f=c[k],f=!!f&&typeof f==="object"&&(!f.toString||
f.toString===Object.prototype.toString||f.toString===Array.prototype.toString);if(f)try{c[k]=_.Rf(c[k])}catch(x){delete c[k]}}k=!0;c.dontclear&&(k=!1);delete c.dontclear;var l;f={};var m=l=a;a=="plus"&&c.action&&(l=a+"_"+c.action,m=a+"/"+c.action);(l=_.Xe("iframes/"+l+"/url"))||(l=":im_socialhost:/:session_prefix::im_prefix:_/widget/render/"+m+"?usegapi=1");for(n in Xo)f[n]=n+"/"+(c[n]||Xo[n])+"/";var n=_.$l(_.ye,l.replace(_.sm,Mo(f)));m="iframes/"+a+"/params/";f={};_.Ee(c,f);(l=_.Xe("lang")||_.Xe("gwidget/lang"))&&
(f.hl=l);Yo[a]||(f.origin=_.So());f.exp=_.Xe(m+"exp");if(m=_.Xe(m+"location"))for(l=0;l<m.length;l++){var p=m[l];f[p]=_.xe.location[p]}switch(a){case "plus":case "follow":f.url=_.Wo(f.href,c,null);delete f.href;break;case "plusone":m=(m=c.href)?Qo(m):Ro();f.url=m;f.db=_.Uo(c.db,void 0,_.Xe());f.ecp=_.Vo(c.ecp,void 0,_.Xe());delete f.href;break;case "signin":f.url=Ro()}_.Me.ILI&&(f.iloader="1");delete f["data-onload"];delete f.rd;for(var q in Xo)f[q]&&delete f[q];f.gsrc=_.Xe("iframes/:source:");q=
_.Xe("inline/css");typeof q!=="undefined"&&e>0&&q>=e&&(f.ic="1");q=/^#|^fr-/;e={};for(var r in f)_.De(f,r)&&q.test(r)&&(e[r.replace(q,"")]=f[r],delete f[r]);r=_.Xe("iframes/"+a+"/params/si")=="q"?f:e;q=_.Po();for(var w in q)!_.De(q,w)||_.De(f,w)||_.De(e,w)||(r[w]=q[w]);w=[].concat($o);r=_.Xe("iframes/"+a+"/methods");_.Nm(r)&&(w=w.concat(r));for(u in c)_.De(c,u)&&/^on/.test(u)&&(a!="plus"||u!="onconnect")&&(w.push(u),delete f[u]);delete f.callback;e._methods=w.join(",");var u=_.Zl(n,f,e);w=h||{};w.allowPost=
1;w.attributes=Zo;w.dontclear=!k;h={};h.userParams=c;h.url=u;h.type=a;_.fp(a,b,c,u,w,h);b=h.id;c=_.Ce();c.id=b;c.userParams=h.userParams;c.url=h.url;c.type=h.type;c.state=1;_.lo[b]=c;b=h}else b=null;b&&((c=b.id)&&d.push(c),up(a,b))}},vp=function(a,b,c){if(a&&a.nodeType===1&&b){if(c)return 1;if(lp[b]){if(Jo[a.nodeName.toLowerCase()])return(a=a.innerHTML)&&a.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")?0:1}else{if(kp[b])return 0;if(jp[b])return 1}}return null};_.Be(_.Fe,"platform",{}).go=function(a,b){tp(a,b)};var wp=_.Be(_.Me,"perf",_.Ce()),qp=_.Be(wp,"g",_.Ce()),xp=_.Be(wp,"i",_.Ce()),yp,zp,Ap,rp,Cp,Dp,Ep;_.Be(wp,"r",[]);yp=_.Ce();zp=_.Ce();Ap=function(a,b,c,d){yp[c]=yp[c]||!!d;_.Be(zp,c,[]);zp[c].push([a,b])};rp=function(a,b,c){var d=wp.r;typeof d==="function"?d(a,b,c):d.push([a,b,c])};Cp=function(a,b,c,d){if(b=="_p")throw Error("H");_.Bp(a,b,c,d)};_.Bp=function(a,b,c,d){Dp(b,c)[a]=d||(new Date).getTime();rp(a,b,c)};Dp=function(a,b){a=_.Be(xp,a,_.Ce());return _.Be(a,b,_.Ce())};
Ep=function(a,b,c){var d=null;b&&c&&(d=Dp(b,c)[a]);return d||qp[a]};(function(){function a(h){this.t={};this.tick=function(k,l,m){this.t[k]=[m!=void 0?m:(new Date).getTime(),l];if(m==void 0)try{window.console.timeStamp("CSI/"+k)}catch(n){}};this.getStartTickTime=function(){return this.t.start[0]};this.tick("start",null,h)}var b;if(window.performance)var c=(b=window.performance.timing)&&b.responseStart;var d=c>0?new a(c):new a;window.__gapi_jstiming__={Timer:a,load:d};if(b){var e=b.navigationStart;e>0&&c>=e&&(window.__gapi_jstiming__.srt=c-e)}if(b){var f=window.__gapi_jstiming__.load;
e>0&&c>=e&&(f.tick("_wtsrt",void 0,e),f.tick("wtsrt_","_wtsrt",c),f.tick("tbsd_","wtsrt_"))}try{b=null,window.chrome&&window.chrome.csi&&(b=Math.floor(window.chrome.csi().pageT),f&&e>0&&(f.tick("_tbnd",void 0,window.chrome.csi().startE),f.tick("tbnd_","_tbnd",e))),b==null&&window.gtbExternal&&(b=window.gtbExternal.pageT()),b==null&&window.external&&(b=window.external.pageT,f&&e>0&&(f.tick("_tbnd",void 0,window.external.startE),f.tick("tbnd_","_tbnd",e))),b&&(window.__gapi_jstiming__.pt=b)}catch(h){}})();if(window.__gapi_jstiming__){window.__gapi_jstiming__.GP={};window.__gapi_jstiming__.Qea=1;var Fp=function(a,b,c){var d=a.t[b],e=a.t.start;if(d&&(e||c))return d=a.t[b][0],e=c!=void 0?c:e[0],Math.round(d-e)},Gp=function(a,b,c){var d="";window.__gapi_jstiming__.srt&&(d+="&srt="+window.__gapi_jstiming__.srt,delete window.__gapi_jstiming__.srt);window.__gapi_jstiming__.pt&&(d+="&tbsrt="+window.__gapi_jstiming__.pt,delete window.__gapi_jstiming__.pt);try{window.external&&window.external.tran?d+="&tran="+
window.external.tran:window.gtbExternal&&window.gtbExternal.tran?d+="&tran="+window.gtbExternal.tran():window.chrome&&window.chrome.csi&&(d+="&tran="+window.chrome.csi().tran)}catch(p){}var e=window.chrome;if(e&&(e=e.loadTimes)&&typeof e==="function"&&(e=e())){e.wasFetchedViaSpdy&&(d+="&p=s");if(e.wasNpnNegotiated){d+="&npn=1";var f=e.npnNegotiatedProtocol;f&&(d+="&npnv="+(encodeURIComponent||escape)(f))}e.wasAlternateProtocolAvailable&&(d+="&apa=1")}var h=a.t,k=h.start;e=[];f=[];for(var l in h)if(l!=
"start"&&l.indexOf("_")!=0){var m=h[l][1];m?h[m]&&f.push(l+"."+Fp(a,l,h[m][0])):k&&e.push(l+"."+Fp(a,l))}delete h.start;if(b)for(var n in b)d+="&"+n+"="+b[n];(b=c)||(b="https:"==document.location.protocol?"https://csi.gstatic.com/csi":"http://csi.gstatic.com/csi");return[b,"?v=3","&s="+(window.__gapi_jstiming__.sn||"gwidget")+"&action=",a.name,f.length?"&it="+f.join(","):"",d,"&rt=",e.join(",")].join("")},Hp=function(a,b,c){a=Gp(a,b,c);if(!a)return"";b=new Image;var d=window.__gapi_jstiming__.Qea++;
window.__gapi_jstiming__.GP[d]=b;b.onload=b.onerror=function(){window.__gapi_jstiming__&&delete window.__gapi_jstiming__.GP[d]};b.src=a;b=null;return a};window.__gapi_jstiming__.report=function(a,b,c){var d=document.visibilityState,e="visibilitychange";d||(d=document.webkitVisibilityState,e="webkitvisibilitychange");if(d=="prerender"){var f=!1,h=function(){if(!f){b?b.prerender="1":b={prerender:"1"};if((document.visibilityState||document.webkitVisibilityState)=="prerender")var k=!1;else Hp(a,b,c),
k=!0;k&&(f=!0,document.removeEventListener(e,h,!1))}};document.addEventListener(e,h,!1);return""}return Hp(a,b,c)}};var Ip={g:"gapi_global",m:"gapi_module",w:"gwidget"},Jp=function(a,b){this.type=a?a=="_p"?"m":"w":"g";this.name=a;this.Cs=b};Jp.prototype.key=function(){switch(this.type){case "g":return this.type;case "m":return this.type+"."+this.Cs;case "w":return this.type+"."+this.name+this.Cs}};
var Kp=new Jp,Lp=navigator.userAgent.match(/iPhone|iPad|Android|PalmWebOS|Maemo|Bada/),Mp=_.Be(wp,"_c",_.Ce()),Np=Math.random()<(_.Xe("csi/rate")||0),Pp=function(a,b,c){for(var d=new Jp(b,c),e=_.Be(Mp,d.key(),_.Ce()),f=zp[a]||[],h=0;h<f.length;++h){var k=f[h],l=k[0],m=a,n=b,p=c;k=Ep(k[1],n,p);m=Ep(m,n,p);e[l]=k&&m?m-k:null}yp[a]&&Np&&(Op(Kp),Op(d))},Qp=function(a,b){b=b||[];for(var c=[],d=0;d<b.length;d++)c.push(a+b[d]);return c},Op=function(a){var b=_.xe.__gapi_jstiming__;b.sn=Ip[a.type];var c=new b.Timer(0);
a:{switch(a.type){case "g":var d="global";break a;case "m":d=a.Cs;break a;case "w":d=a.name;break a}d=void 0}c.name=d;d=!1;var e=a.key(),f=Mp[e];c.tick("_start",null,0);for(var h in f)c.tick(h,"_start",f[h]),d=!0;Mp[e]=_.Ce();d&&(h=[],h.push("l"+(_.Xe("isPlusUser")?"1":"0")),d="m"+(Lp?"1":"0"),h.push(d),a.type=="m"?h.push("p"+a.Cs):a.type=="w"&&(e="n"+a.Cs,h.push(e),a.Cs=="0"&&h.push(d+e)),h.push("u"+(_.Xe("isLoggedIn")?"1":"0")),a=Qp("",h),a=Qp("abc_",a).join(","),b.report(c,{e:a}))};
Ap("blt","bs0","bs1");Ap("psi","ps0","ps1");Ap("rpcqi","rqe","rqd");Ap("bsprt","bsrt0","bsrt1");Ap("bsrqt","bsrt1","bsrt2");Ap("bsrst","bsrt2","bsrt3");Ap("mli","ml0","ml1");Ap("mei","me0","me1",!0);Ap("wcdi","wrs","wcdi");Ap("wci","wrs","wdc");Ap("wdi","wrs","wrdi");Ap("wdt","bs0","wrdt");Ap("wri","wrs","wrri",!0);Ap("wrt","bs0","wrrt");Ap("wji","wje0","wje1",!0);Ap("wjli","wjl0","wjl1");Ap("whi","wh0","wh1",!0);Ap("wai","waaf0","waaf1",!0);Ap("wadi","wrs","waaf1",!0);Ap("wadt","bs0","waaf1",!0);
Ap("wprt","wrt0","wrt1");Ap("wrqt","wrt1","wrt2");Ap("wrst","wrt2","wrt3",!0);Ap("fbprt","fsrt0","fsrt1");Ap("fbrqt","fsrt1","fsrt2");Ap("fbrst","fsrt2","fsrt3",!0);Ap("fdns","fdns0","fdns1");Ap("fcon","fcon0","fcon1");Ap("freq","freq0","freq1");Ap("frsp","frsp0","frsp1");Ap("fttfb","fttfb0","fttfb1");Ap("ftot","ftot0","ftot1",!0);var Rp=wp.r;if(typeof Rp!=="function"){for(var Sp;Sp=Rp.shift();)Pp.apply(null,Sp);wp.r=Pp};var Tp=["div"],Up="onload",Vp=!0,Wp=!0,Xp=function(a){return a},Yp=null,Zp=function(a){var b=_.Xe(a);return typeof b!=="undefined"?b:_.Xe("gwidget/"+a)},cq,dq,eq,fq,gq,hq,iq,oq,jq,pq,qq,rq,sq,tq,kq,mq,uq,lq,vq,wq,xq,yq,zq,Aq;Yp=_.Xe();_.Xe("gwidget");var $p=Zp("parsetags");Up=$p==="explicit"||$p==="onload"?$p:Up;var aq=Zp("google_analytics");typeof aq!=="undefined"&&(Vp=!!aq);var bq=Zp("data_layer");typeof bq!=="undefined"&&(Wp=!!bq);cq=function(){var a=this&&this.getId();a&&(_.Me.drw=a)};
dq=function(){_.Me.drw=null};eq=function(a){return function(b){var c=a;typeof b==="number"?c=b:typeof b==="string"&&(c=b.indexOf("px"),c!=-1&&(b=b.substring(0,c)),c=parseInt(b,10));return c}};fq=function(a){typeof a==="string"&&(a=window[a]);return typeof a==="function"?a:null};gq=function(){return Zp("lang")||"en-US"};
hq=function(a){if(!_.Ta.wb("attach")){var b={},c=_.Ta.wb("inline"),d;for(d in c)c.hasOwnProperty(d)&&(b[d]=c[d]);b.open=function(e){var f=e.wc().renderData.id;f=document.getElementById(f);if(!f)throw Error("I");return c.attach(e,f)};_.Ta.Fc("attach",b)}a.style="attach"};iq=function(){var a={};a.width=[eq(450)];a.height=[eq(24)];a.onready=[fq];a.lang=[gq,"hl"];a.iloader=[function(){return _.Me.ILI},"iloader"];return a}();
oq=function(a){var b={};b.Qe=a[0];b.Pp=-1;b.uta="___"+b.Qe+"_";b.Nha="g:"+b.Qe;b.Cra="g-"+b.Qe;b.pZ=[];b.config={};b.xy=[];b.N1={};b.FD={};var c=function(e){for(var f in e)if(_.De(e,f)){b.config[f]=[fq];b.xy.push(f);var h=e[f],k=null,l=null,m=null;typeof h==="function"?k=h:h&&typeof h==="object"&&(k=h.rra,l=h.GD,m=h.NN);m&&(b.xy.push(m),b.config[m]=[fq],b.N1[f]=m);k&&(b.config[f]=[k]);l&&(b.FD[f]=l)}},d=function(e){for(var f={},h=0;h<e.length;++h)f[e[h].toLowerCase()]=1;f[b.Nha]=1;b.Uca=f};a[1]&&
(b.parameters=a[1]);(function(e){b.config=e;for(var f in iq)iq.hasOwnProperty(f)&&!b.config.hasOwnProperty(f)&&(b.config[f]=iq[f])})(a[2]||{});a[3]&&c(a[3]);a[4]&&d(a[4]);a[5]&&(b.Pm=a[5]);b.nta=a[6]===!0;b.qea=a[7];b.Bha=a[8];b.Uca||d(Tp);b.EJ=function(e){b.Pp++;Cp("wrs",b.Qe,String(b.Pp));var f=[],h=e.element,k=e.config,l=":"+b.Qe;l==":plus"&&e.Ed&&e.Ed.action&&(l+="_"+e.Ed.action);var m=jq(b,k),n={};_.Ee(_.Po(),n);for(var p in e.Ed)e.Ed[p]!=null&&(n[p]=e.Ed[p]);p={container:h.id,renderData:e.Lea,
style:"inline",height:k.height,width:k.width};hq(p);b.Pm&&(f[2]=p,f[3]=n,f[4]=m,b.Pm("i",f));l=_.Ta.open(l,p,n,m);e=e.m8;kq(l,k);lq(l,h);mq(b,l,e);nq(b.Qe,b.Pp.toString(),l);f[5]=l;b.Pm&&b.Pm("e",f)};return b};
jq=function(a,b){for(var c={},d=a.xy.length-1;d>=0;--d){var e=a.xy[d],f=b[a.N1[e]||e]||b[e],h=b[e];h&&f!==h&&(f=function(l,m){return function(n){m.apply(this,arguments);l.apply(this,arguments)}}(f,h));f&&(c[e]=f)}for(var k in a.FD)a.FD.hasOwnProperty(k)&&(c[k]=pq(c[k]||function(){},a.FD[k]));c.drefresh=cq;c.erefresh=dq;return c};
pq=function(a,b){return function(c){var d=b(c);if(d){var e=c.href||null;if(Vp){if(window._gat)try{var f=window._gat._getTrackerByName("~0");f&&f._getAccount()!="UA-XXXXX-X"?f._trackSocial("Google",d,e):window._gaq&&window._gaq.push(["_trackSocial","Google",d,e])}catch(k){}if(window.ga&&window.ga.getAll)try{var h=window.ga.getAll();for(f=0;f<h.length;f++)h[f].send("social","Google",d,e)}catch(k){}}if(Wp&&window.dataLayer)try{window.dataLayer.push({event:"social",socialNetwork:"Google",socialAction:d,
socialTarget:e})}catch(k){}}a.call(this,c)}};qq=function(a){return _.Ln&&a instanceof _.Ln};rq=function(a){return qq(a)?"_renderstart":"renderstart"};sq=function(a){return qq(a)?"_ready":"ready"};tq=function(){return!0};kq=function(a,b){if(b.onready){var c=!1,d=function(){c||(c=!0,b.onready.call(null))};a.register(sq(a),d,tq);a.register(rq(a),d,tq)}};
mq=function(a,b,c){var d=a.Qe,e=String(a.Pp),f=!1,h=function(){f||(f=!0,b.getIframeEl(),c&&Cp("wrdt",d,e),Cp("wrdi",d,e))};b.register(rq(b),h,tq);var k=!1;a=function(){k||(k=!0,h(),c&&Cp("wrrt",d,e),Cp("wrri",d,e))};b.register(sq(b),a,tq);qq(b)?b.register("widget-interactive-"+b.id,a,tq):_.$f.register("widget-interactive-"+b.id,a);_.$f.register("widget-csi-tick-"+b.id,function(l,m,n){l==="wdc"?Cp("wdc",d,e,n):l==="wje0"?Cp("wje0",d,e,n):l==="wje1"?Cp("wje1",d,e,n):l=="wh0"?_.Bp("wh0",d,e,n):l=="wh1"?
_.Bp("wh1",d,e,n):l=="wcdi"&&_.Bp("wcdi",d,e,n)})};uq=function(a){return typeof a=="number"?a+"px":a=="100%"?a:null};lq=function(a,b){var c=function(d){d=d||a;var e=uq(d.width);e&&b.style.width!=e&&(b.style.width=e);(d=uq(d.height))&&b.style.height!=d&&(b.style.height=d)};qq(a)?a.setParam("onRestyle",c):(a.register("ready",c,tq),a.register("renderstart",c,tq),a.register("resize",c,tq))};vq=function(a,b){for(var c in iq)if(iq.hasOwnProperty(c)){var d=iq[c][1];d&&!b.hasOwnProperty(d)&&(b[d]=a[d])}return b};
wq=function(a,b){var c={},d;for(d in a)a.hasOwnProperty(d)&&(c[a[d][1]||d]=(a[d]&&a[d][0]||Xp)(b[d.toLowerCase()],b,Yp));return c};xq=function(a){if(a=a.qea)for(var b=0;b<a.length;b++)(new Image).src=a[b]};yq=function(a,b){var c=b.userParams,d=b.siteElement;d||(d=(d=b.iframeNode)&&d.parentNode);if(d&&d.nodeType===1){var e=wq(a.config,c);a.pZ.push({element:d,config:e,Ed:vq(e,wq(a.parameters,c)),xsa:3,m8:!!c["data-onload"],Lea:b})}b=a.pZ;for(a=a.EJ;b.length>0;)a(b.shift())};
zq=function(a,b){a.Pp++;Cp("wrs",a.Qe,String(a.Pp));var c=b.userParams,d=wq(a.config,c),e=[],f=b.iframeNode,h=b.siteElement,k=jq(a,d),l=wq(a.parameters,c);_.Ee(_.Po(),l);l=vq(d,l);c=!!c["data-onload"];var m=_.an,n=_.Ce();n.renderData=b;n.height=d.height;n.width=d.width;n.id=b.id;n.url=b.url;n.iframeEl=f;n.where=n.container=h;n.apis=["_open"];n.messageHandlers=k;n.messageHandlersFilter=_.dn;_.so(n);f=l;a.Pm&&(e[2]=n,e[3]=f,e[4]=k,a.Pm("i",e));k=m.attach(n);k.id=b.id;k.iM(k,n);kq(k,d);lq(k,h);mq(a,
k,c);nq(a.Qe,a.Pp.toString(),k);e[5]=k;a.Pm&&a.Pm("e",e)};Aq=function(a,b){var c=b.url;a.Bha||_.bj(c)?zq(a,b):_.Ta.open?yq(a,b):(0,_.Fg)("iframes",function(){yq(a,b)})};
_.Bq=function(a){var b=oq(a);xq(b);_.Yf(b.Qe,function(d){Aq(b,d)});jp[b.Qe]=!0;var c={va:function(d,e,f){var h=e||{};h.type=b.Qe;e=h.type;delete h.type;var k=pp(d);if(k){d={};for(var l in h)_.De(h,l)&&(d[l.toLowerCase()]=h[l]);d.rd=1;(l=!!d.ri)&&delete d.ri;sp(e,k,d,[],0,l,f)}else _.Vf.log("gapi."+e+".render: missing element "+typeof d==="string"?d:"")},go:function(d){tp(d,b.Qe)},zsa:function(){var d=_.Be(_.Me,"WI",_.Ce()),e;for(e in d)delete d[e]}};a=function(){Up==="onload"&&c.go()};if(!Ko(b.Qe)){if(!_.Wf())try{a()}catch(d){}_.Xf(a)}_.t("gapi."+
b.Qe+".go",c.go);_.t("gapi."+b.Qe+".render",c.va);return c};var Cq=function(){var a=window;return!!a.performance&&!!a.performance.getEntries},nq=function(a,b,c){if(Cq()){var d=function(){var f=!1;return function(){if(f)return!0;f=!0;return!1}}(),e=function(){d()||window.setTimeout(function(){var f=c.getIframeEl().src;var h=f.indexOf("#");h!=-1&&(f=f.substring(0,h));f=window.performance.getEntriesByName(f);f.length<1?f=null:(f=f[0],f=f.responseStart==0?null:f);if(f){h=Math.round(f.requestStart);var k=Math.round(f.responseStart),l=Math.round(f.responseEnd);
Cp("wrt0",a,b,Math.round(f.startTime));Cp("wrt1",a,b,h);Cp("wrt2",a,b,k);Cp("wrt3",a,b,l)}},1E3)};c.register(rq(c),e,tq);c.register(sq(c),e,tq)}};_.t("gapi.widget.make",_.Bq);
_.af=_.af||{};_.af.Hv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};_.af.lB=function(a){var b,c,d={};for(b=0;c=a[b];++b)d[c]=c;return d};
_.af=_.af||{};_.af.i7=function(a){var b=window;typeof b.addEventListener!="undefined"?b.addEventListener("mousemove",a,!1):typeof b.attachEvent!="undefined"?b.attachEvent("onmousemove",a):_.bf("cannot attachBrowserEvent: mousemove")};_.af.Jea=function(a){var b=window;b.removeEventListener?b.removeEventListener("mousemove",a,!1):b.detachEvent?b.detachEvent("onmousemove",a):_.bf("cannot removeBrowserEvent: mousemove")};
_.af=_.af||{};
(function(){function a(c,d){return String.fromCharCode(d)}var b={0:!1,10:!0,13:!0,34:!0,39:!0,60:!0,62:!0,92:!0,8232:!0,8233:!0,65282:!0,65287:!0,65308:!0,65310:!0,65340:!0};_.af.escape=function(c,d){if(c){if(typeof c==="string")return _.af.aG(c);if(typeof c==="Array"){var e=0;for(d=c.length;e<d;++e)c[e]=_.af.escape(c[e])}else if(typeof c==="object"&&d){d={};for(e in c)c.hasOwnProperty(e)&&(d[_.af.aG(e)]=_.af.escape(c[e],!0));return d}}return c};_.af.aG=function(c){if(!c)return c;for(var d=[],e,f,
h=0,k=c.length;h<k;++h)e=c.charCodeAt(h),f=b[e],f===!0?d.push("&#",e,";"):f!==!1&&d.push(c.charAt(h));return d.join("")};_.af.ota=function(c){return c?c.replace(/&#([0-9]+);/g,a):c}})();
_.Qg=function(){function a(m){var n=new _.Pg;n.ux(m);return n.Si()}var b=window.crypto;if(b&&typeof b.getRandomValues=="function")return function(){var m=new window.Uint32Array(1);b.getRandomValues(m);return Number("0."+m[0])};var c=_.Xe("random/maxObserveMousemove");c==null&&(c=-1);var d=0,e=Math.random(),f=1,h=(screen.width*screen.width+screen.height)*1E6,k=function(m){m=m||window.event;var n=m.screenX+m.clientX<<16;n+=m.screenY+m.clientY;n*=(new Date).getTime()%1E6;f=f*n%h;c>0&&++d==c&&_.af.Jea(k)};
c!=0&&_.af.i7(k);var l=a(document.cookie+"|"+document.location+"|"+(new Date).getTime()+"|"+e);return function(){var m=f;m+=parseInt(l.substr(0,20),16);l=a(l);return m/(h+1.2089258196146292E24)}}();_.t("shindig.random",_.Qg);
_.Ta.Ma={};_.Ta.Ma.Mi={};_.Ta.Ma.Mi.z7=function(a){try{return!!a.document}catch(b){}return!1};_.Ta.Ma.Mi.ZT=function(a){var b=a.parent;return a!=b&&_.Ta.Ma.Mi.z7(b)?_.Ta.Ma.Mi.ZT(b):a};_.Ta.Ma.Mi.sra=function(a){var b=a.userAgent||"";a=a.product||"";return b.indexOf("Opera")!=0&&b.indexOf("WebKit")==-1&&a=="Gecko"&&b.indexOf("rv:1.")>0};
_.Ta.Ma.Mi.Hv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};
var Jq,Kq,Lq,Mq,Pq,Qq,Rq,Sq,Tq,Uq,Vq,Wq,Xq;Jq=function(){_.$f.register("_noop_echo",function(){this.callback(_.Ta.w$(_.Ta.rm[this.f]))})};Kq=function(){window.setTimeout(function(){_.$f.call("..","_noop_echo",_.Ta.aea)},0)};Lq=function(a,b,c){var d=function(e){var f=Array.prototype.slice.call(arguments,0),h=f[f.length-1];if(typeof h==="function"){var k=h;f.pop()}f.unshift(b,a,k,c);_.$f.call.apply(_.$f,f)};d._iframe_wrapped_rpc_=!0;return d};
Mq=function(a){_.Ta.dC[a]||(_.Ta.dC[a]={},_.$f.register(a,function(b,c){var d=this.f;if(!(typeof b!="string"||b in{}||d in{})){var e=this.callback,f=_.Ta.dC[a][d],h;f&&Object.hasOwnProperty.call(f,b)?h=f[b]:Object.hasOwnProperty.call(_.Ta.Pq,a)&&(h=_.Ta.Pq[a]);if(h)return d=Array.prototype.slice.call(arguments,1),h._iframe_wrapped_rpc_&&e&&d.push(e),h.apply({},d)}_.Vf.error(['Unregistered call in window "',window.name,'" for method "',a,'", via proxyId "',b,'" from frame "',d,'".'].join(""));return null}));
return _.Ta.dC[a]};_.Nq=function(){var a={};var b=window.location.href;var c=b.indexOf("?"),d=b.indexOf("#");b=(d===-1?b.substr(c+1):[b.substr(c+1,d-c-1),"&",b.substr(d+1)].join("")).split("&");c=window.decodeURIComponent?decodeURIComponent:unescape;d=b.length;for(var e=0;e<d;++e){var f=b[e].indexOf("=");if(f!==-1){var h=b[e].substring(0,f);f=b[e].substring(f+1);f=f.replace(/\+/g," ");try{a[h]=c(f)}catch(k){}}}return a};_.Oq=function(){return _.xe.location.origin||_.xe.location.protocol+"//"+_.xe.location.host};
Pq=function(a){_.Me.h=a};Qq=function(a){_.Me.bsh=a};Rq=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Sq=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Tq=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Sq(a[d])&&!Sq(b[d])?Tq(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Sq(b[d])?[]:{},Tq(a[d],b[d])):a[d]=b[d])};
Uq=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Vq=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};
Wq=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Rq("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Uq())if(e=Vq(c),d.push(25),typeof e===
"object")return e;return{}}};Xq=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Tq(c,b);a.push(c)};
_.Yq=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;_.di(!0);d=window.___gcfg;b=Rq("cu");a=window.___gu;d&&d!==a&&(Xq(b,d),window.___gu=d);d=Rq("cu");e=document.getElementsByTagName("script")||[];a=[];f=[];f.push.apply(f,Rq("us"));for(h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&a.push(k);a.length==0&&e.length>0&&e[e.length-1].src&&a.push(e[e.length-1]);for(e=0;e<
a.length;++e)a[e].getAttribute("gapi_processed")||(a[e].setAttribute("gapi_processed",!0),(f=a[e])?(h=f.nodeType,f=h==3||h==4?f.nodeValue:f.textContent||""):f=void 0,h=a[e].nonce||a[e].getAttribute("nonce"),(f=Wq(f,h))&&d.push(f));c&&Xq(b,c);a=Rq("cd");c=0;for(d=a.length;c<d;++c)Tq(_.di(),a[c],!0);a=Rq("ci");c=0;for(d=a.length;c<d;++c)Tq(_.di(),a[c],!0);c=0;for(d=b.length;c<d;++c)Tq(_.di(),b[c],!0)};var Zq,$q=window.location.href,ar=$q.indexOf("?"),br=$q.indexOf("#");
Zq=(br===-1?$q.substr(ar+1):[$q.substr(ar+1,br-ar-1),"&",$q.substr(br+1)].join("")).split("&");for(var cr=window.decodeURIComponent?decodeURIComponent:unescape,dr=0,er=Zq.length;dr<er;++dr){var fr=Zq[dr].indexOf("=");if(fr!==-1){Zq[dr].substring(0,fr);var gr=Zq[dr].substring(fr+1);gr=gr.replace(/\+/g," ");try{cr(gr)}catch(a){}}};if(window.ToolbarApi)hr=window.ToolbarApi,hr.Ia=window.ToolbarApi.getInstance,hr.prototype=window.ToolbarApi.prototype,_.g=hr.prototype,_.g.openWindow=hr.prototype.openWindow,_.g.oQ=hr.prototype.closeWindow,_.g.B_=hr.prototype.setOnCloseHandler,_.g.XP=hr.prototype.canClosePopup,_.g.yZ=hr.prototype.resizeWindow;else{var hr=function(){};hr.Ia=function(){!ir&&window.external&&window.external.GTB_IsToolbar&&(ir=new hr);return ir};_.g=hr.prototype;_.g.openWindow=function(a){return window.external.GTB_OpenPopup&&
window.external.GTB_OpenPopup(a)};_.g.oQ=function(a){window.external.GTB_ClosePopupWindow&&window.external.GTB_ClosePopupWindow(a)};_.g.B_=function(a,b){window.external.GTB_SetOnCloseHandler&&window.external.GTB_SetOnCloseHandler(a,b)};_.g.XP=function(a){return window.external.GTB_CanClosePopup&&window.external.GTB_CanClosePopup(a)};_.g.yZ=function(a,b){return window.external.GTB_ResizeWindow&&window.external.GTB_ResizeWindow(a,b)};var ir=null;window.ToolbarApi=hr;window.ToolbarApi.getInstance=hr.Ia};var jr=/^[-_.0-9A-Za-z]+$/,kr={open:"open",onready:"ready",close:"close",onresize:"resize",onOpen:"open",onReady:"ready",onClose:"close",onResize:"resize",onRenderStart:"renderstart"},lr={onBeforeParentOpen:"beforeparentopen"},mr={onOpen:function(a){var b=a.wc();a.eh(b.container||b.element);return a},onClose:function(a){a.remove()}},nr=function(){_.Ta.hV++;return["I",_.Ta.hV,"_",(new Date).getTime()].join("")},or,pr,qr,tr,ur,vr,wr,yr,xr;_.Ta.Vn=function(a){var b=_.Ce();_.Ee(_.jm,b);_.Ee(a,b);return b};
or=function(a){return a instanceof Array?a.join(","):a instanceof Object?_.Rf(a):a};pr=function(a){var b=_.ei("googleapis.config/elog");if(b)try{b(a)}catch(c){}};qr=function(a){a&&a.match(jr)&&_.Yq("googleapis.config/gcv",a)};_.rr=function(a,b){b=b||{};for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};
_.sr=function(a,b,c,d,e){var f=[],h;for(h in a)if(a.hasOwnProperty(h)){var k=b,l=c,m=a[h],n=d,p=Mq(h);p[k]=p[k]||{};n=_.Ta.Ma.Mi.Hv(n,m);m._iframe_wrapped_rpc_&&(n._iframe_wrapped_rpc_=!0);p[k][l]=n;f.push(h)}if(e)for(var q in _.Ta.Pq)_.Ta.Pq.hasOwnProperty(q)&&f.push(q);return f.join(",")};tr=function(a,b,c){var d={};if(a&&a._methods){a=a._methods.split(",");for(var e=0;e<a.length;e++){var f=a[e];d[f]=Lq(f,b,c)}}return d};
ur=function(a){if(a&&a.disableMultiLevelParentRelay)a=!1;else{var b;if(b=_.Eo&&_.Eo._open&&a.style!="inline"&&a.inline!==!0)a=a.container,b=!(a&&(typeof a=="string"&&document.getElementById(a)||document==(a.ownerDocument||a.document)));a=b}return a};vr=function(a,b){var c={};b=b.params||{};for(var d in a)d.charAt(0)=="#"&&(c[d.substring(1)]=a[d]),d.indexOf("fr-")==0&&(c[d.substring(3)]=a[d]),b[d]=="#"&&(c[d]=a[d]);for(var e in c)delete a["fr-"+e],delete a["#"+e],delete a[e];return c};
wr=function(a){if(a.charAt(0)==":"){a="iframes/"+a.substring(1);var b=_.ei(a);a={};_.Ee(b,a);(b=a.url)&&(a.url=_.vm(b));a.params||(a.params={});return a}return{url:_.vm(a)}};yr=function(a){function b(){}b.prototype=xr.prototype;a.prototype=new b};
xr=function(a,b,c,d,e,f,h,k){this.config=wr(a);this.openParams=this.GB=b||{};this.params=c||{};this.methods=d;this.uD=!1;zr(this,b.style);this.callbacks={};Ar(this,function(){var l;(l=this.GB.style)&&_.Ta.Rw[l]?l=_.Ta.Rw[l]:l?(_.Vf.warn(['Missing handler for style "',l,'". Continuing with default handler.'].join("")),l=null):l=mr;if(l){if(typeof l==="function")var m=l(this);else{var n={};for(m in l){var p=l[m];n[m]=typeof p==="function"?_.Ta.Ma.Mi.Hv(l,p,this):p}m=n}for(var q in e)l=m[q],typeof l===
"function"&&Br(this,e[q],_.Ta.Ma.Mi.Hv(m,l))}f&&Br(this,"close",f)});this.Ik=this.ac=h;this.KJ=(k||[]).slice();h&&this.KJ.unshift(h.getId())};xr.prototype.wc=function(){return this.GB};xr.prototype.YG=function(){return this.params};xr.prototype.Wz=function(){return this.methods};xr.prototype.kd=function(){return this.Ik};
var zr=function(a,b){a.uD||((b=b&&!_.Ta.Rw[b]&&_.Ta.KF[b])?(a.JF=[],b(function(){a.uD=!0;for(var c=a.JF.length,d=0;d<c;++d)a.JF[d].call(a)})):a.uD=!0)},Ar=function(a,b){a.uD?b.call(a):a.JF.push(b)};xr.prototype.ye=function(a,b){Ar(this,function(){Br(this,a,b)})};var Br=function(a,b,c){a.callbacks[b]=a.callbacks[b]||[];a.callbacks[b].push(c)};xr.prototype.gp=function(a,b){Ar(this,function(){var c=this.callbacks[a];if(c)for(var d=c.length,e=0;e<d;++e)if(c[e]===b){c.splice(e,1);break}})};
xr.prototype.fi=function(a,b){var c=this.callbacks[a];if(c)for(var d=Array.prototype.slice.call(arguments,1),e=c.length,f=0;f<e;++f)try{var h=c[f].apply({},d)}catch(k){_.Vf.error(['Exception when calling callback "',a,'" with exception "',k.name,": ",k.message,'".'].join("")),pr(k)}return h};var Cr=function(a){return typeof a=="number"?{value:a,tG:a+"px"}:a=="100%"?{value:100,tG:"100%",YV:!0}:null};xr.prototype.send=function(a,b,c){_.Ta.XZ(this,a,b,c)};
xr.prototype.register=function(a,b){var c=this;c.ye(a,function(d){b.call(c,d)})};var Dr=function(a,b,c,d,e,f,h){var k=this;xr.call(this,a,b,c,d,kr,e,f,h);this.id=b.id||nr();this.qw=b.rpctoken&&String(b.rpctoken)||Math.round(_.Pi()*1E9);this.nba=vr(this.params,this.config);this.hG={};Ar(this,function(){k.fi("open");_.rr(k.hG,k)})};yr(Dr);_.g=Dr.prototype;
_.g.eh=function(a,b){if(!this.config.url)return _.Vf.error("Cannot open iframe, empty URL."),this;var c=this.id;_.Ta.rm[c]=this;var d=_.rr(this.methods);d._ready=this.FB;d._close=this.close;d._open=this.WX;d._resizeMe=this.zZ;d._renderstart=this.QX;var e=this.nba;this.qw&&(e.rpctoken=this.qw);e._methods=_.sr(d,c,"",this,!0);this.el=a=typeof a==="string"?document.getElementById(a):a;d={id:c};if(b){d.attributes=b;var f=b.style;if(typeof f==="string"){if(f){var h=[];f=f.split(";");for(var k=f.length,
l=0;l<k;++l){var m=f[l];if(m.length!=0||l+1!=k)m=m.split(":"),m.length==2&&m[0].match(/^[ a-zA-Z_-]+$/)&&m[1].match(/^[ +.%0-9a-zA-Z_-]+$/)?h.push(m.join(":")):_.Vf.error(['Iframe style "',f[l],'" not allowed.'].join(""))}h=h.join(";")}else h="";b.style=h}}this.wc().allowPost&&(d.allowPost=!0);this.wc().forcePost&&(d.forcePost=!0);d.queryParams=this.params;d.fragmentParams=e;d.paramsSerializer=or;this.ji=_.xm(this.config.url,a,d);a=this.ji.getAttribute("data-postorigin")||this.ji.src;_.Ta.rm[c]=this;
_.$f.OC(this.id,this.qw);_.$f.Oj(this.id,a);return this};_.g.Ph=function(a,b){this.hG[a]=b};_.g.getId=function(){return this.id};_.g.getIframeEl=function(){return this.ji};_.g.getSiteEl=function(){return this.el};_.g.setSiteEl=function(a){this.el=a};_.g.FB=function(a){var b=tr(a,this.id,"");this.Ik&&typeof this.methods._ready=="function"&&(a._methods=_.sr(b,this.Ik.getId(),this.id,this,!1),this.methods._ready(a));_.rr(a,this);_.rr(b,this);this.fi("ready",a)};
_.g.QX=function(a){this.fi("renderstart",a)};_.g.close=function(a){a=this.fi("close",a);delete _.Ta.rm[this.id];return a};_.g.remove=function(){var a=document.getElementById(this.id);a&&a.parentNode&&a.parentNode.removeChild(a)};
_.g.WX=function(a){var b=tr(a.params,this.id,a.proxyId);delete a.params._methods;a.openParams.anchor=="_parent"&&(a.openParams.anchor=this.el);if(ur(a.openParams))new Er(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain);else{var c=new Dr(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain),d=this;Ar(c,function(){var e={childId:c.getId()},f=c.hG;f._toclose=c.close;e._methods=_.sr(f,d.id,c.id,c,!1);b._onopen(e)})}};
_.g.zZ=function(a){if(this.fi("resize",a)===void 0&&this.ji){var b=Cr(a.width);b!=null&&(this.ji.style.width=b.tG);a=Cr(a.height);a!=null&&(this.ji.style.height=a.tG);this.ji.parentElement&&(b!=null&&b.YV||a!=null&&a.YV)&&(this.ji.parentElement.style.display="block")}};
var Er=function(a,b,c,d,e,f,h){var k=this;xr.call(this,a,b,c,d,lr,e,f,h);this.url=a;this.Jp=null;this.eK=nr();Ar(this,function(){k.fi("beforeparentopen");var l=_.rr(k.methods);l._onopen=k.Rda;l._ready=k.FB;l._onclose=k.Pda;k.params._methods=_.sr(l,"..",k.eK,k,!0);l={};for(var m in k.params)l[m]=or(k.params[m]);_.Eo._open({url:k.config.url,openParams:k.GB,params:l,proxyId:k.eK,openedByProxyChain:k.KJ})})};yr(Er);Er.prototype.H$=function(){return this.Jp};
Er.prototype.Rda=function(a){this.Jp=a.childId;var b=tr(a,"..",this.Jp);_.rr(b,this);this.close=b._toclose;_.Ta.rm[this.Jp]=this;this.Ik&&this.methods._onopen&&(a._methods=_.sr(b,this.Ik.getId(),this.Jp,this,!1),this.methods._onopen(a))};Er.prototype.FB=function(a){var b=String(this.Jp),c=tr(a,"..",b);_.rr(a,this);_.rr(c,this);this.fi("ready",a);this.Ik&&this.methods._ready&&(a._methods=_.sr(c,this.Ik.getId(),b,this,!1),this.methods._ready(a))};
Er.prototype.Pda=function(a){if(this.Ik&&this.methods._onclose)this.methods._onclose(a);else return a=this.fi("close",a),delete _.Ta.rm[this.Jp],a};
var Fr=function(a,b,c,d,e,f,h){xr.call(this,a,b,c,d,lr,f,h);this.id=b.id||nr();this.oha=e;d._close=this.close;this.onClosed=this.JX;this.d2=0;Ar(this,function(){this.fi("beforeparentopen");var k=_.rr(this.methods);this.params._methods=_.sr(k,"..",this.eK,this,!0);k={};k.queryParams=this.params;a=_.om(_.ye,this.config.url,this.id,k);var l=e.openWindow(a);this.canAutoClose=function(m){m(e.XP(l))};e.B_(l,this);this.d2=l})};yr(Fr);
Fr.prototype.close=function(a){a=this.fi("close",a);this.oha.oQ(this.d2);return a};Fr.prototype.JX=function(){this.fi("close")};_.Eo.send=function(a,b,c){_.Ta.XZ(_.Eo,a,b,c)};
(function(){function a(h){return _.Ta.Rw[h]}function b(h,k){_.Ta.Rw[h]=k}function c(h){h=h||{};h.height==="auto"&&(h.height=_.zm());var k=window&&hr&&hr.Ia();k?k.yZ(h.width||0,h.height||0):_.Eo&&_.Eo._resizeMe&&_.Eo._resizeMe(h)}function d(h){qr(h)}_.Ta.rm={};_.Ta.Rw={};_.Ta.KF={};_.Ta.hV=0;_.Ta.dC={};_.Ta.Pq={};_.Ta.QB=null;_.Ta.PB=[];_.Ta.aea=function(h){var k=!1;try{if(h!=null){var l=window.parent.frames[h.id];k=l.iframer.id==h.id&&l.iframes.openedId_(_.Eo.id)}}catch(m){}try{_.Ta.QB={origin:this.origin,
referer:this.referer,claimedOpenerId:h&&h.id,claimedOpenerProxyChain:h&&h.proxyChain||[],sameOrigin:k};for(h=0;h<_.Ta.PB.length;++h)_.Ta.PB[h](_.Ta.QB);_.Ta.PB=[]}catch(m){pr(m)}};_.Ta.w$=function(h){var k=h&&h.Ik,l=null;k&&(l={},l.id=k.getId(),l.proxyChain=h.KJ);return l};Jq();if(window.parent!=window){var e=_.Nq();e.gcv&&qr(e.gcv);var f=e.jsh;f&&Pq(f);_.rr(tr(e,"..",""),_.Eo);_.rr(e,_.Eo);Kq()}_.Ta.wb=a;_.Ta.Fc=b;_.Ta.lga=d;_.Ta.resize=c;_.Ta.U9=function(h){return _.Ta.KF[h]};_.Ta.pL=function(h,
k){_.Ta.KF[h]=k};_.Ta.xZ=c;_.Ta.Gga=d;_.Ta.uA={};_.Ta.uA.get=a;_.Ta.uA.set=b;_.Ta.allow=function(h,k){Mq(h);_.Ta.Pq[h]=k||window[h]};_.Ta.vqa=function(h){delete _.Ta.Pq[h]};_.Ta.open=function(h,k,l,m,n,p){arguments.length==3?m={}:arguments.length==4&&typeof m==="function"&&(n=m,m={});var q=k.style==="bubble"&&hr?hr.Ia():null;return q?new Fr(h,k,l,m,q,n,p):ur(k)?new Er(h,k,l,m,n,p):new Dr(h,k,l,m,n,p)};_.Ta.close=function(h,k){_.Eo&&_.Eo._close&&_.Eo._close(h,k)};_.Ta.ready=function(h,k,l){arguments.length==
2&&typeof k==="function"&&(l=k,k={});var m=h||{};"height"in m||(m.height=_.zm());m._methods=_.sr(k||{},"..","",_.Eo,!0);_.Eo&&_.Eo._ready&&_.Eo._ready(m,l)};_.Ta.KT=function(h){_.Ta.QB?h(_.Ta.QB):_.Ta.PB.push(h)};_.Ta.Tda=function(h){return!!_.Ta.rm[h]};_.Ta.d$=function(){return["https://ssl.gstatic.com/gb/js/",_.ei("googleapis.config/gcv")].join("")};_.Ta.RY=function(h){var k={mouseover:1,mouseout:1};if(_.Eo._event)for(var l=0;l<h.length;l++){var m=h[l];m in k&&document.addEventListener(m,function(n){_.Eo._event({event:n.type,
timestamp:(new Date).getTime()})},!0)}};_.Ta.XZ=function(h,k,l,m){var n=this,p=[];l!==void 0&&p.push(l);m&&p.push(function(q){m.call(n,[q])});h[k]&&h[k].apply(h,p)};_.Ta.CROSS_ORIGIN_IFRAMES_FILTER=function(){return!0};_.Ta.y7=function(h,k,l){var m=Array.prototype.slice.call(arguments);_.Ta.KT(function(n){n.sameOrigin&&(m.unshift("/"+n.claimedOpenerId+"|"+window.location.protocol+"//"+window.location.host),_.$f.call.apply(_.$f,m))})};_.Ta.Fea=function(h,k){_.$f.register(h,k)};_.Ta.sga=Pq;_.Ta.f_=
Qq;_.Ta.MW=pr;_.Ta.iV=_.Eo})();_.t("iframes.allow",_.Ta.allow);_.t("iframes.callSiblingOpener",_.Ta.y7);_.t("iframes.registerForOpenedSibling",_.Ta.Fea);_.t("iframes.close",_.Ta.close);_.t("iframes.getGoogleConnectJsUri",_.Ta.d$);_.t("iframes.getHandler",_.Ta.wb);_.t("iframes.getDeferredHandler",_.Ta.U9);_.t("iframes.getParentInfo",_.Ta.KT);_.t("iframes.iframer",_.Ta.iV);_.t("iframes.open",_.Ta.open);_.t("iframes.openedId_",_.Ta.Tda);_.t("iframes.propagate",_.Ta.RY);_.t("iframes.ready",_.Ta.ready);_.t("iframes.resize",_.Ta.resize);
_.t("iframes.setGoogleConnectJsVersion",_.Ta.lga);_.t("iframes.setBootstrapHint",_.Ta.f_);_.t("iframes.setJsHint",_.Ta.sga);_.t("iframes.setHandler",_.Ta.Fc);_.t("iframes.setDeferredHandler",_.Ta.pL);_.t("IframeBase",xr);_.t("IframeBase.prototype.addCallback",xr.prototype.ye);_.t("IframeBase.prototype.getMethods",xr.prototype.Wz);_.t("IframeBase.prototype.getOpenerIframe",xr.prototype.kd);_.t("IframeBase.prototype.getOpenParams",xr.prototype.wc);_.t("IframeBase.prototype.getParams",xr.prototype.YG);
_.t("IframeBase.prototype.removeCallback",xr.prototype.gp);_.t("Iframe",Dr);_.t("Iframe.prototype.close",Dr.prototype.close);_.t("Iframe.prototype.exposeMethod",Dr.prototype.Ph);_.t("Iframe.prototype.getId",Dr.prototype.getId);_.t("Iframe.prototype.getIframeEl",Dr.prototype.getIframeEl);_.t("Iframe.prototype.getSiteEl",Dr.prototype.getSiteEl);_.t("Iframe.prototype.openInto",Dr.prototype.eh);_.t("Iframe.prototype.remove",Dr.prototype.remove);_.t("Iframe.prototype.setSiteEl",Dr.prototype.setSiteEl);
_.t("Iframe.prototype.addCallback",Dr.prototype.ye);_.t("Iframe.prototype.getMethods",Dr.prototype.Wz);_.t("Iframe.prototype.getOpenerIframe",Dr.prototype.kd);_.t("Iframe.prototype.getOpenParams",Dr.prototype.wc);_.t("Iframe.prototype.getParams",Dr.prototype.YG);_.t("Iframe.prototype.removeCallback",Dr.prototype.gp);_.t("IframeProxy",Er);_.t("IframeProxy.prototype.getTargetIframeId",Er.prototype.H$);_.t("IframeProxy.prototype.addCallback",Er.prototype.ye);_.t("IframeProxy.prototype.getMethods",Er.prototype.Wz);
_.t("IframeProxy.prototype.getOpenerIframe",Er.prototype.kd);_.t("IframeProxy.prototype.getOpenParams",Er.prototype.wc);_.t("IframeProxy.prototype.getParams",Er.prototype.YG);_.t("IframeProxy.prototype.removeCallback",Er.prototype.gp);_.t("IframeWindow",Fr);_.t("IframeWindow.prototype.close",Fr.prototype.close);_.t("IframeWindow.prototype.onClosed",Fr.prototype.JX);_.t("iframes.util.getTopMostAccessibleWindow",_.Ta.Ma.Mi.ZT);_.t("iframes.handlers.get",_.Ta.uA.get);_.t("iframes.handlers.set",_.Ta.uA.set);
_.t("iframes.resizeMe",_.Ta.xZ);_.t("iframes.setVersionOverride",_.Ta.Gga);_.t("iframes.CROSS_ORIGIN_IFRAMES_FILTER",_.Ta.CROSS_ORIGIN_IFRAMES_FILTER);_.t("IframeBase.prototype.send",xr.prototype.send);_.t("IframeBase.prototype.register",xr.prototype.register);_.t("Iframe.prototype.send",Dr.prototype.send);_.t("Iframe.prototype.register",Dr.prototype.register);_.t("IframeProxy.prototype.send",Er.prototype.send);_.t("IframeProxy.prototype.register",Er.prototype.register);
_.t("IframeWindow.prototype.send",Fr.prototype.send);_.t("IframeWindow.prototype.register",Fr.prototype.register);_.t("iframes.iframer.send",_.Ta.iV.send);
var St=_.Ta.Fc,Tt={open:function(a){var b=_.oo(a.wc());return a.eh(b,{style:_.po(b)})},attach:function(a,b){var c=_.oo(a.wc()),d=b.id,e=b.getAttribute("data-postorigin")||b.src,f=/#(?:.*&)?rpctoken=(\d+)/.exec(e);f=f&&f[1];a.id=d;a.qw=f;a.el=c;a.ji=b;_.Ta.rm[d]=a;b=_.rr(a.methods);b._ready=a.FB;b._close=a.close;b._open=a.WX;b._resizeMe=a.zZ;b._renderstart=a.QX;_.sr(b,d,"",a,!0);_.$f.OC(a.id,a.qw);_.$f.Oj(a.id,e);c=_.Ta.Vn({style:_.po(c)});for(var h in c)Object.prototype.hasOwnProperty.call(c,h)&&
(h=="style"?a.ji.style.cssText=c[h]:a.ji.setAttribute(h,c[h]))}};Tt.onready=_.qo;Tt.onRenderStart=_.qo;Tt.close=_.ro;St("inline",Tt);
_.Eh=function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(_.kd(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var h=0;h<f;h++)a[e+h]=d[h]}else a.push(d)}};_.Fh=function(a,b){b=b||a;for(var c=0,d=0,e={};d<a.length;){var f=a[d++],h=_.vb(f)?"o"+_.uh(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(e,h)||(e[h]=!0,b[c++]=f)}b.length=c};_.Gh=function(a){for(var b in a)return!1;return!0};
_.Hh=function(a,b){a.src=_.kc(b);(b=_.Gc("script",a.ownerDocument))&&a.setAttribute("nonce",b)};_.Ih=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}return b};var Jh,Kh,Mh;Jh={};Kh=null;_.Lh=_.Bd||_.Cd||!_.Dh&&typeof _.Xa.atob=="function";_.Nh=function(a,b){b===void 0&&(b=0);Mh();b=Jh[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,f=0;e<a.length-2;e+=3){var h=a[e],k=a[e+1],l=a[e+2],m=b[h>>2];h=b[(h&3)<<4|k>>4];k=b[(k&15)<<2|l>>6];l=b[l&63];c[f++]=m+h+k+l}m=0;l=d;switch(a.length-e){case 2:m=a[e+1],l=b[(m&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|m>>4]+l+d}return c.join("")};
_.Oh=function(a,b){function c(l){for(;d<a.length;){var m=a.charAt(d++),n=Kh[m];if(n!=null)return n;if(!_.xc(m))throw Error("w`"+m);}return l}Mh();for(var d=0;;){var e=c(-1),f=c(0),h=c(64),k=c(64);if(k===64&&e===-1)break;b(e<<2|f>>4);h!=64&&(b(f<<4&240|h>>2),k!=64&&b(h<<6&192|k))}};
Mh=function(){if(!Kh){Kh={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));Jh[c]=d;for(var e=0;e<d.length;e++){var f=d[e];Kh[f]===void 0&&(Kh[f]=e)}}}};
var ki;_.ji=function(a){this.Bc=a||{cookie:""}};_.g=_.ji.prototype;_.g.isEnabled=function(){if(!_.Xa.navigator.cookieEnabled)return!1;if(!this.isEmpty())return!0;this.set("TESTCOOKIESENABLED","1",{YI:60});if(this.get("TESTCOOKIESENABLED")!=="1")return!1;this.remove("TESTCOOKIESENABLED");return!0};
_.g.set=function(a,b,c){var d=!1;if(typeof c==="object"){var e=c.Gsa;d=c.secure||!1;var f=c.domain||void 0;var h=c.path||void 0;var k=c.YI}if(/[;=\s]/.test(a))throw Error("z`"+a);if(/[;\r\n]/.test(b))throw Error("A`"+b);k===void 0&&(k=-1);this.Bc.cookie=a+"="+b+(f?";domain="+f:"")+(h?";path="+h:"")+(k<0?"":k==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+k*1E3)).toUTCString())+(d?";secure":"")+(e!=null?";samesite="+e:"")};
_.g.get=function(a,b){for(var c=a+"=",d=(this.Bc.cookie||"").split(";"),e=0,f;e<d.length;e++){f=_.zc(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};_.g.remove=function(a,b,c){var d=this.Fl(a);this.set(a,"",{YI:0,path:b,domain:c});return d};_.g.lg=function(){return ki(this).keys};_.g.Ye=function(){return ki(this).values};_.g.isEmpty=function(){return!this.Bc.cookie};_.g.Zb=function(){return this.Bc.cookie?(this.Bc.cookie||"").split(";").length:0};
_.g.Fl=function(a){return this.get(a)!==void 0};_.g.clear=function(){for(var a=ki(this).keys,b=a.length-1;b>=0;b--)this.remove(a[b])};ki=function(a){a=(a.Bc.cookie||"").split(";");for(var b=[],c=[],d,e,f=0;f<a.length;f++)e=_.zc(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));return{keys:b,values:c}};_.li=new _.ji(typeof document=="undefined"?null:document);
_.ti={};_.ui=function(a){return _.ti[a||"token"]||null};
_.cj=function(a){a&&typeof a.dispose=="function"&&a.dispose()};_.dj=function(){this.Lg=this.Lg;this.Qo=this.Qo};_.dj.prototype.Lg=!1;_.dj.prototype.isDisposed=function(){return this.Lg};_.dj.prototype.dispose=function(){this.Lg||(this.Lg=!0,this.ua())};_.dj.prototype[Symbol.dispose]=function(){this.dispose()};_.fj=function(a,b){_.ej(a,_.bb(_.cj,b))};_.ej=function(a,b){a.Lg?b():(a.Qo||(a.Qo=[]),a.Qo.push(b))};_.dj.prototype.ua=function(){if(this.Qo)for(;this.Qo.length;)this.Qo.shift()()};
var nj;nj=function(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1};_.oj=function(a){this.src=a;this.je={};this.ox=0};_.qj=function(a,b){this.type="function"==typeof _.pj&&a instanceof _.pj?String(a):a;this.currentTarget=this.target=b;this.defaultPrevented=this.dw=!1};_.qj.prototype.stopPropagation=function(){this.dw=!0};_.qj.prototype.preventDefault=function(){this.defaultPrevented=!0};_.rj=function(a,b){_.qj.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.XJ=!1;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.Df=null;a&&this.init(a,b)};_.eb(_.rj,_.qj);
_.rj.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=_.Cd||a.offsetX!==void 0?a.offsetX:a.layerX,
this.offsetY=_.Cd||a.offsetY!==void 0?a.offsetY:a.layerY,this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.XJ=_.Ed?a.metaKey:a.ctrlKey;this.pointerId=a.pointerId||0;this.pointerType=
a.pointerType;this.state=a.state;this.timeStamp=a.timeStamp;this.Df=a;a.defaultPrevented&&_.rj.N.preventDefault.call(this)};_.rj.prototype.stopPropagation=function(){_.rj.N.stopPropagation.call(this);this.Df.stopPropagation?this.Df.stopPropagation():this.Df.cancelBubble=!0};_.rj.prototype.preventDefault=function(){_.rj.N.preventDefault.call(this);var a=this.Df;a.preventDefault?a.preventDefault():a.returnValue=!1};_.sj="closure_listenable_"+(Math.random()*1E6|0);_.tj=function(a){return!(!a||!a[_.sj])};var uj=0;var vj=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.Jf=e;this.key=++uj;this.iw=this.vy=!1},wj=function(a){a.iw=!0;a.listener=null;a.proxy=null;a.src=null;a.Jf=null};_.oj.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.je[f];a||(a=this.je[f]=[],this.ox++);var h=xj(a,b,d,e);h>-1?(b=a[h],c||(b.vy=!1)):(b=new vj(b,this.src,f,!!d,e),b.vy=c,a.push(b));return b};_.oj.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.je))return!1;var e=this.je[a];b=xj(e,b,c,d);return b>-1?(wj(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.je[a],this.ox--),!0):!1};
_.yj=function(a,b){var c=b.type;if(!(c in a.je))return!1;var d=_.gj(a.je[c],b);d&&(wj(b),a.je[c].length==0&&(delete a.je[c],a.ox--));return d};_.oj.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.je)if(!a||c==a){for(var d=this.je[c],e=0;e<d.length;e++)++b,wj(d[e]);delete this.je[c];this.ox--}return b};_.oj.prototype.Hq=function(a,b,c,d){a=this.je[a.toString()];var e=-1;a&&(e=xj(a,b,c,d));return e>-1?a[e]:null};
_.oj.prototype.hasListener=function(a,b){var c=a!==void 0,d=c?a.toString():"",e=b!==void 0;return nj(this.je,function(f){for(var h=0;h<f.length;++h)if(!(c&&f[h].type!=d||e&&f[h].capture!=b))return!0;return!1})};var xj=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.iw&&f.listener==b&&f.capture==!!c&&f.Jf==d)return e}return-1};var zj,Aj,Bj,Fj,Hj,Ij,Jj,Lj;zj="closure_lm_"+(Math.random()*1E6|0);Aj={};Bj=0;_.Dj=function(a,b,c,d,e){if(d&&d.once)return _.Cj(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.Dj(a,b[f],c,d,e);return null}c=_.Ej(c);return _.tj(a)?a.na(b,c,_.vb(d)?!!d.capture:!!d,e):Fj(a,b,c,!1,d,e)};
Fj=function(a,b,c,d,e,f){if(!b)throw Error("B");var h=_.vb(e)?!!e.capture:!!e,k=_.Gj(a);k||(a[zj]=k=new _.oj(a));c=k.add(b,c,d,h,f);if(c.proxy)return c;d=Hj();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)_.vi||(e=h),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Ij(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("C");Bj++;return c};
Hj=function(){var a=Jj,b=function(c){return a.call(b.src,b.listener,c)};return b};_.Cj=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.Cj(a,b[f],c,d,e);return null}c=_.Ej(c);return _.tj(a)?a.rr(b,c,_.vb(d)?!!d.capture:!!d,e):Fj(a,b,c,!0,d,e)};
_.Kj=function(a){if(typeof a==="number"||!a||a.iw)return!1;var b=a.src;if(_.tj(b))return b.DN(a);var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Ij(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Bj--;(c=_.Gj(b))?(_.yj(c,a),c.ox==0&&(c.src=null,b[zj]=null)):wj(a);return!0};Ij=function(a){return a in Aj?Aj[a]:Aj[a]="on"+a};
Jj=function(a,b){if(a.iw)a=!0;else{b=new _.rj(b,this);var c=a.listener,d=a.Jf||a.src;a.vy&&_.Kj(a);a=c.call(d,b)}return a};_.Gj=function(a){a=a[zj];return a instanceof _.oj?a:null};Lj="__closure_events_fn_"+(Math.random()*1E9>>>0);_.Ej=function(a){if(typeof a==="function")return a;a[Lj]||(a[Lj]=function(b){return a.handleEvent(b)});return a[Lj]};_.mj(function(a){Jj=a(Jj)});
_.Mj=function(a,b){var c=a.length-b.length;return c>=0&&a.indexOf(b,c)==c};_.Zd.prototype.O=_.pb(1,function(a){return _.be(this.Bc,a)});_.Nj=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)_.Nj(a,b[f],c,d,e);else d=_.vb(d)?!!d.capture:!!d,c=_.Ej(c),_.tj(a)?a.Ac(b,c,d,e):a&&(a=_.Gj(a))&&(b=a.Hq(b,c,d,e))&&_.Kj(b)};_.Oj=function(){_.dj.call(this);this.rk=new _.oj(this);this.U6=this;this.PJ=null};_.eb(_.Oj,_.dj);_.Oj.prototype[_.sj]=!0;_.g=_.Oj.prototype;_.g.Yn=function(){return this.PJ};
_.g.aD=function(a){this.PJ=a};_.g.addEventListener=function(a,b,c,d){_.Dj(this,a,b,c,d)};_.g.removeEventListener=function(a,b,c,d){_.Nj(this,a,b,c,d)};
_.g.dispatchEvent=function(a){var b,c=this.Yn();if(c)for(b=[];c;c=c.Yn())b.push(c);c=this.U6;var d=a.type||a;if(typeof a==="string")a=new _.qj(a,c);else if(a instanceof _.qj)a.target=a.target||c;else{var e=a;a=new _.qj(d,c);_.ij(a,e)}e=!0;var f;if(b)for(f=b.length-1;!a.dw&&f>=0;f--){var h=a.currentTarget=b[f];e=h.iu(d,!0,a)&&e}a.dw||(h=a.currentTarget=c,e=h.iu(d,!0,a)&&e,a.dw||(e=h.iu(d,!1,a)&&e));if(b)for(f=0;!a.dw&&f<b.length;f++)h=a.currentTarget=b[f],e=h.iu(d,!1,a)&&e;return e};
_.g.ua=function(){_.Oj.N.ua.call(this);this.tK();this.PJ=null};_.g.na=function(a,b,c,d){return this.rk.add(String(a),b,!1,c,d)};_.g.rr=function(a,b,c,d){return this.rk.add(String(a),b,!0,c,d)};_.g.Ac=function(a,b,c,d){return this.rk.remove(String(a),b,c,d)};_.g.DN=function(a){return _.yj(this.rk,a)};_.g.tK=function(){this.rk&&this.rk.removeAll(void 0)};
_.g.iu=function(a,b,c){a=this.rk.je[String(a)];if(!a)return!0;a=a.concat();for(var d=!0,e=0;e<a.length;++e){var f=a[e];if(f&&!f.iw&&f.capture==b){var h=f.listener,k=f.Jf||f.src;f.vy&&this.DN(f);d=h.call(k,c)!==!1&&d}}return d&&!c.defaultPrevented};_.g.Hq=function(a,b,c,d){return this.rk.Hq(String(a),b,c,d)};_.g.hasListener=function(a,b){return this.rk.hasListener(a!==void 0?String(a):void 0,b)};
var Gr;Gr=function(){var a=_.Jc();if(_.Qc())return _.Yc(a);a=_.Nc(a);var b=_.Xc(a);return _.Pc()?b(["Version","Opera"]):_.Sc()?b(["Edge"]):_.Tc()?b(["Edg"]):_.Mc("Silk")?b(["Silk"]):_.Wc()?b(["Chrome","CriOS","HeadlessChrome"]):(a=a[2])&&a[1]||""};_.Hr=function(a){return _.Dc(Gr(),a)>=0};_.Jr=function(){return _.Sb&&_.Kc?_.Kc.mobile:!_.Ir()&&(_.Mc("iPod")||_.Mc("iPhone")||_.Mc("Android")||_.Mc("IEMobile"))};
_.Ir=function(){return _.Sb&&_.Kc?!_.Kc.mobile&&(_.Mc("iPad")||_.Mc("Android")||_.Mc("Silk")):_.Mc("iPad")||_.Mc("Android")&&!_.Mc("Mobile")||_.Mc("Silk")};_.Kr=function(){return!_.Jr()&&!_.Ir()};
var ct;ct=function(a,b,c){return arguments.length<=2?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)};_.dt=function(a,b,c,d){return Array.prototype.splice.apply(a,ct(arguments,1))};_.et=function(a,b,c){if(a!==null&&b in a)throw Error("h`"+b);a[b]=c};_.ft=function(a,b){var c=b||document;c.getElementsByClassName?a=c.getElementsByClassName(a)[0]:(c=document,a=a?(b||c).querySelector(a?"."+a:""):_.ce(c,"*",a,b)[0]||null);return a||null};
_.gt=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b.nextSibling)};_.it=function(a,b,c){a&&!c&&(a=a.parentNode);for(c=0;a;){if(b(a))return a;a=a.parentNode;c++}return null};_.jt=function(a){_.dj.call(this);this.ug=a;this.mc={}};_.eb(_.jt,_.dj);var kt=[];_.jt.prototype.na=function(a,b,c,d){return this.Dv(a,b,c,d)};
_.jt.prototype.Dv=function(a,b,c,d,e){Array.isArray(b)||(b&&(kt[0]=b.toString()),b=kt);for(var f=0;f<b.length;f++){var h=_.Dj(a,b[f],c||this.handleEvent,d||!1,e||this.ug||this);if(!h)break;this.mc[h.key]=h}return this};_.jt.prototype.rr=function(a,b,c,d){return lt(this,a,b,c,d)};var lt=function(a,b,c,d,e,f){if(Array.isArray(c))for(var h=0;h<c.length;h++)lt(a,b,c[h],d,e,f);else{b=_.Cj(b,c,d||a.handleEvent,e,f||a.ug||a);if(!b)return a;a.mc[b.key]=b}return a};
_.jt.prototype.Ac=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)this.Ac(a,b[f],c,d,e);else c=c||this.handleEvent,d=_.vb(d)?!!d.capture:!!d,e=e||this.ug||this,c=_.Ej(c),d=!!d,b=_.tj(a)?a.Hq(b,c,d,e):a?(a=_.Gj(a))?a.Hq(b,c,d,e):null:null,b&&(_.Kj(b),delete this.mc[b.key]);return this};_.jt.prototype.removeAll=function(){_.Zb(this.mc,function(a,b){this.mc.hasOwnProperty(b)&&_.Kj(a)},this);this.mc={}};_.jt.prototype.ua=function(){_.jt.N.ua.call(this);this.removeAll()};
_.jt.prototype.handleEvent=function(){throw Error("K");};
var Xu,Yu,Zu,$u,av,cv,dv,ev,fv,hv;_.Vu=function(a,b){for(var c in a)if(!(c in b)||a[c]!==b[c])return!1;for(var d in b)if(!(d in a))return!1;return!0};_.Wu=!1;Xu=function(a){try{_.Wu&&window.console&&window.console.log&&window.console.log(a)}catch(b){}};Yu=function(a){try{window.console&&window.console.warn&&window.console.warn(a)}catch(b){}};Zu=function(a,b){if(!a)return-1;if(a.indexOf)return a.indexOf(b,void 0);for(var c=0,d=a.length;c<d;c++)if(a[c]===b)return c;return-1};
$u=function(a,b){function c(){}if(!a)throw Error("N");if(!b)throw Error("O");c.prototype=b.prototype;a.prototype=new c;a.prototype.constructor=a};av=function(a){return Object.prototype.toString.call(a)==="[object Function]"};_.bv=function(a){var b={};if(a)for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};cv=function(a){var b=location.hash;a=new RegExp("[&#]"+a+"=([^&]*)");b=decodeURIComponent(b);b=a.exec(b);return b==null?"":b[1].replace(/\+/g," ")};
dv=function(a,b,c){if(a.addEventListener)a.addEventListener(b,c,!1);else if(a.attachEvent)a.attachEvent("on"+b,c);else throw Error("P`"+b);};ev={token:1,id_token:1};fv=function(){var a=navigator.userAgent.toLowerCase();return a.indexOf("msie")!=-1&&parseInt(a.split("msie")[1],10)==8};_.gv=window.JSON;hv=function(a){this.PN=a||[];this.qc={}};
hv.prototype.addEventListener=function(a,b){if(!(Zu(this.PN,a)>=0))throw Error("R`"+a);if(!av(b))throw Error("S`"+a);this.qc[a]||(this.qc[a]=[]);Zu(this.qc[a],b)<0&&this.qc[a].push(b)};hv.prototype.removeEventListener=function(a,b){if(!(Zu(this.PN,a)>=0))throw Error("R`"+a);av(b)&&this.qc[a]&&this.qc[a].length&&(b=Zu(this.qc[a],b),b>=0&&this.qc[a].splice(b,1))};
hv.prototype.dispatchEvent=function(a){var b=a.type;if(!(b&&Zu(this.PN,b)>=0))throw Error("T`"+b);if(this.qc[b]&&this.qc[b].length)for(var c=this.qc[b].length,d=0;d<c;d++)this.qc[b][d](a)};var iv,jv,lv,pv,qv,Hv,Iv,Kv,Lv,Nv,Rv,Sv,Tv,Xv;iv={};jv={};_.kv=function(){if(_.ed()&&!_.Hr("118"))return!1;var a=_.Wc()&&!_.Tc()&&!_.Uc(),b=_.$c()||_.Kr();return"IdentityCredential"in window&&a&&b&&_.Hr("132")&&(_.Kr()||_.$c())};lv={google:{fedcmConfigUrl:"https://accounts.google.com/o/fedcm/config.json",authServerUrl:"https://accounts.google.com/o/oauth2/auth",idpIFrameUrl:"https://accounts.google.com/o/oauth2/iframe"}};_.mv=function(a,b){if(a=lv[a])return a[b]};
_.nv=function(a,b){if(!a)throw Error("U");if(!b.authServerUrl)throw Error("V");if(!b.idpIFrameUrl)throw Error("W");lv[a]={authServerUrl:b.authServerUrl,idpIFrameUrl:b.idpIFrameUrl};b.fedcmConfigUrl?lv[a].fedcmConfigUrl=b.fedcmConfigUrl:a==="google"&&(lv[a].fedcmConfigUrl="https://accounts.google.com/o/fedcm/config.json")};_.ov=void 0;
pv=function(a){a.style.position="absolute";a.style.width="1px";a.style.height="1px";a.style.left="-9999px";a.style.top="-9999px";a.style.right="-9999px";a.style.bottom="-9999px";a.style.display="none";a.setAttribute("aria-hidden","true")};qv=function(){this.Ki=window;this.Sy=this.An=this.Zv=this.xi=null};
qv.prototype.open=function(a,b,c,d){rv(this);this.Zv?(this.An&&(this.An(),this.An=null),sv(this)):this.Zv="authPopup"+Math.floor(Math.random()*1E6+1);a:{this.xi=this.Ki.open(a,this.Zv,b);try{this.xi.focus();if(this.xi.closed||typeof this.xi.closed=="undefined")throw Error("Y");_.ov=this.xi}catch(e){d&&setTimeout(d,0);this.xi=null;break a}c&&(this.An=c,tv(this))}};
var rv=function(a){try{if(a.xi==null||a.xi.closed)a.xi=null,a.Zv=null,sv(a),a.An&&(a.An(),a.An=null)}catch(b){a.xi=null,a.Zv=null,sv(a)}},tv=function(a){a.Sy=window.setInterval(function(){rv(a)},300)},sv=function(a){a.Sy&&(window.clearInterval(a.Sy),a.Sy=null)};jv=jv||{};var uv=function(a,b){this.Yb=a;this.kI=b;this.Qc=null;this.uo=!1};uv.prototype.start=function(){if(!this.uo&&!this.Qc){var a=this;this.Qc=window.setTimeout(function(){a.clear();a.uo||(a.Yb(),a.uo=!0)},jv.WT(this.kI))}};
uv.prototype.clear=function(){this.Qc&&(window.clearTimeout(this.Qc),this.Qc=null)};var vv=function(a,b){var c=jv.dt;this.oba=jv.Ts;this.W1=c;this.Yb=a;this.kI=b;this.Qc=null;this.uo=!1;var d=this;this.X1=function(){document[d.oba]||(d.clear(),d.start())}};vv.prototype.start=function(){if(!this.uo&&!this.Qc){dv(document,this.W1,this.X1);var a=this;this.Qc=window.setTimeout(function(){a.clear();a.uo||(a.Yb(),a.uo=!0)},jv.WT(this.kI))}};
vv.prototype.clear=function(){var a=this.W1,b=this.X1,c=document;if(c.removeEventListener)c.removeEventListener(a,b,!1);else if(c.detachEvent)c.detachEvent("on"+a,b);else throw Error("Q`"+a);this.Qc&&(window.clearTimeout(this.Qc),this.Qc=null)};jv.Ts=null;jv.dt=null;
jv.Qba=function(){var a=document;typeof a.hidden!=="undefined"?(jv.Ts="hidden",jv.dt="visibilitychange"):typeof a.msHidden!=="undefined"?(jv.Ts="msHidden",jv.dt="msvisibilitychange"):typeof a.webkitHidden!=="undefined"&&(jv.Ts="webkitHidden",jv.dt="webkitvisibilitychange")};jv.Qba();jv.k8=function(a,b){return jv.Ts&&jv.dt?new vv(a,b):new uv(a,b)};jv.WT=function(a){return Math.max(1,a-(new Date).getTime())};
var wv=function(a,b){document.cookie="G_ENABLED_IDPS="+a+";domain=."+b+";expires=Fri, 31 Dec 9999 12:00:00 GMT;path=/"},xv=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var q=h,r=0;r<64;r+=4)q[r/4]=p[r]<<24|p[r+1]<<16|p[r+2]<<8|p[r+3];for(r=16;r<80;r++)p=q[r-3]^q[r-8]^q[r-14]^q[r-16],q[r]=(p<<1|p>>>31)&4294967295;p=e[0];var w=e[1],u=e[2],x=e[3],A=e[4];for(r=0;r<80;r++){if(r<40)if(r<20){var D=x^w&(u^x);var E=1518500249}else D=
w^u^x,E=1859775393;else r<60?(D=w&u|x&(w|u),E=2400959708):(D=w^u^x,E=3395469782);D=((p<<5|p>>>27)&4294967295)+D+A+E+q[r]&4294967295;A=x;x=u;u=(w<<30|w>>>2)&4294967295;w=p;p=D}e[0]=e[0]+p&4294967295;e[1]=e[1]+w&4294967295;e[2]=e[2]+u&4294967295;e[3]=e[3]+x&4294967295;e[4]=e[4]+A&4294967295}function c(p,q){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var r=[],w=0,u=p.length;w<u;++w)r.push(p.charCodeAt(w));p=r}q||(q=p.length);r=0;if(m==0)for(;r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64;for(;r<
q;)if(f[m++]=p[r++],n++,m==64)for(m=0,b(f);r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64}function d(){var p=[],q=n*8;m<56?c(k,56-m):c(k,64-(m-56));for(var r=63;r>=56;r--)f[r]=q&255,q>>>=8;b(f);for(r=q=0;r<5;r++)for(var w=24;w>=0;w-=8)p[q++]=e[r]>>w&255;return p}for(var e=[],f=[],h=[],k=[128],l=1;l<64;++l)k[l]=0;var m,n;a();return{reset:a,update:c,digest:d,Si:function(){for(var p=d(),q="",r=0;r<p.length;r++)q+="0123456789ABCDEF".charAt(Math.floor(p[r]/16))+"0123456789ABCDEF".charAt(p[r]%16);return q}}},yv=
window.crypto,zv=!1,Av=0,Bv=1,Cv=0,Dv="",Ev=function(a){a=a||window.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Bv=Bv*b%Cv;if(++Av==3)if(a=window,b=Ev,a.removeEventListener)a.removeEventListener("mousemove",b,!1);else if(a.detachEvent)a.detachEvent("onmousemove",b);else throw Error("Q`mousemove");},Fv=function(a){var b=xv();b.update(a);return b.Si()};zv=!!yv&&typeof yv.getRandomValues=="function";
zv||(Cv=(screen.width*screen.width+screen.height)*1E6,Dv=Fv(document.cookie+"|"+document.location+"|"+(new Date).getTime()+"|"+Math.random()),dv(window,"mousemove",Ev));iv=iv||{};iv.g4="ssIFrame_";
_.Gv=function(a,b,c){c=c===void 0?!1:c;this.Bb=a;if(!this.Bb)throw Error("Z");a=_.mv(a,"idpIFrameUrl");if(!a)throw Error("$");this.fV=a;if(!b)throw Error("aa");this.Um=b;a=this.fV;b=document.createElement("a");b.setAttribute("href",a);a=[b.protocol,"//",b.hostname];b.protocol=="http:"&&b.port!=""&&b.port!="0"&&b.port!="80"?(a.push(":"),a.push(b.port)):b.protocol=="https:"&&b.port!=""&&b.port!="0"&&b.port!="443"&&(a.push(":"),a.push(b.port));this.TH=a.join("");this.pfa=[location.protocol,"//",location.host].join("");
this.Yw=this.SH=this.yo=!1;this.bV=null;this.EB=[];this.Mr=[];this.fk={};this.zo=void 0;this.Fs=c};_.g=_.Gv.prototype;_.g.show=function(){var a=this.zo;a.style.position="fixed";a.style.width="100%";a.style.height="100%";a.style.left="0px";a.style.top="0px";a.style.right="0px";a.style.bottom="0px";a.style.display="block";a.style.zIndex="9999999";a.style.overflow="hidden";a.setAttribute("aria-hidden","false")};_.g.hide=function(){pv(this.zo)};
_.g.jB=function(a){if(this.yo)a&&a(this);else{if(!this.zo){var b=iv.g4+this.Bb;var c=this.Bb;var d=location.hostname;var e,f=document.cookie.match("(^|;) ?G_ENABLED_IDPS=([^;]*)(;|$)");f&&f.length>2&&(e=f[2]);(f=e&&Zu(e.split("|"),c)>=0)?wv(e,d):wv(e?e+"|"+c:c,d);c=!f;var h=this.fV,k=this.pfa;d=this.Um;e=this.Fs;e=e===void 0?!1:e;f=document.createElement("iframe");f.setAttribute("id",b);b=f.setAttribute;var l="allow-scripts allow-same-origin";document.requestStorageAccess&&av(document.requestStorageAccess)&&
(l+=" allow-storage-access-by-user-activation");b.call(f,"sandbox",l);f.setAttribute("allow","identity-credentials-get");pv(f);f.setAttribute("frame-border","0");b=[h,"#origin=",encodeURIComponent(k)];b.push("&rpcToken=");b.push(encodeURIComponent(d));c&&b.push("&clearCache=1");_.Wu&&b.push("&debug=1");e&&b.push("&supportBlocked3PCookies=1");document.body.appendChild(f);f.setAttribute("src",b.join(""));this.zo=f}a&&this.EB.push(a)}};_.g.iW=function(){return this.yo&&this.Yw};_.g.Wn=function(){return this.bV};
Hv=function(a){for(var b=0;b<a.EB.length;b++)a.EB[b](a);a.EB=[]};_.Jv=function(a,b,c,d){if(a.yo){if(a.yo&&a.SH)throw a="Failed to communicate with IDP IFrame due to unitialization error: "+a.Wn(),Xu(a),Error(a);Iv(a,{method:b,params:c},d)}else a.Mr.push({kp:{method:b,params:c},callback:d}),a.jB()};Iv=function(a,b,c){if(c){for(var d=b.id;!d||a.fk[d];)d=(new Date).getMilliseconds()+"-"+(Math.random()*1E6+1);b.id=d;a.fk[d]=c}b.rpcToken=a.Um;a.zo.contentWindow.postMessage(_.gv.stringify(b),a.TH)};
Kv=function(a){if(a&&a.indexOf("::")>=0)throw Error("ba");};_.Gv.prototype.zj=function(a,b,c,d,e,f,h,k,l){l=l===void 0?!1:l;Kv(f);b=_.bv(b);_.Jv(this,"getTokenResponse",{clientId:a,loginHint:c,request:b,sessionSelector:d,forceRefresh:h,skipCache:k,id:f,userInteracted:l},e)};_.Gv.prototype.hB=function(a,b,c,d,e){b=_.bv(b);_.Jv(this,"listIdpSessions",{clientId:a,request:b,sessionSelector:c,forceRefresh:e},d)};Lv=function(a,b,c){Kv(b.identifier);_.Jv(a,"getSessionSelector",b,c)};
_.Mv=function(a,b,c,d,e){Kv(b.identifier);_.Jv(a,"setSessionSelector",{domain:b.domain,crossSubDomains:b.crossSubDomains,policy:b.policy,id:b.id,hint:d,disabled:!!c},e)};Nv=function(a,b,c,d,e,f,h){b={clientId:b};c&&(b.pluginName=c);d&&(b.ackExtensionDate=d);b.useFedCm=e;f&&(b.fedCmEnabled=f);_.Jv(a,"monitorClient",b,h)};_.Gv.prototype.revoke=_.jb(8);_.Gv.prototype.wt=_.jb(10);iv.EA={};iv.PG=function(a){return iv.EA[a]};
iv.jB=function(a,b,c){c=c===void 0?!1:c;var d=iv.PG(a);if(!d){d=String;if(zv){var e=new window.Uint32Array(1);yv.getRandomValues(e);e=Number("0."+e[0])}else e=Bv,e+=parseInt(Dv.substr(0,20),16),Dv=Fv(Dv),e/=Cv+1.2089258196146292E24;d=new _.Gv(a,d(2147483647*e),c);iv.EA[a]=d}d.jB(b)};iv.W9=function(a){for(var b in iv.EA){var c=iv.PG(b);if(c&&c.zo&&c.zo.contentWindow==a.source&&c.TH==a.origin)return c}};iv.y$=function(a){for(var b in iv.EA){var c=iv.PG(b);if(c&&c.TH==a)return c}};iv=iv||{};
var Pv=function(){var a=[],b;for(b in _.Ov)a.push(_.Ov[b]);hv.call(this,a);this.um={};Xu("EventBus is ready.")};$u(Pv,hv);_.Ov={Q5:"sessionSelectorChanged",uE:"sessionStateChanged",Rs:"authResult",a3:"displayIFrame"};Rv=function(a,b){var c=Qv;a&&b&&(c.um[a]||(c.um[a]=[]),Zu(c.um[a],b)<0&&c.um[a].push(b))};Sv=function(a){var b=Qv;a&&(b.um[a]||(b.um[a]=[]))};Tv=function(a,b,c){return b&&a.um[b]&&Zu(a.um[b],c)>=0};_.g=Pv.prototype;
_.g.vea=function(a){var b,c=!!a.source&&(a.source===_.ov||a.source.opener===window);if(b=c?iv.y$(a.origin):iv.W9(a)){try{var d=_.gv.parse(a.data)}catch(e){Xu("Bad event, an error happened when parsing data.");return}if(!c){if(!d||!d.rpcToken||d.rpcToken!=b.Um){Xu("Bad event, no RPC token.");return}if(d.id&&!d.method){c=d;if(a=b.fk[c.id])delete b.fk[c.id],a(c.result,c.error);return}}d.method!="fireIdpEvent"?Xu("Bad IDP event, method unknown."):(a=d.params)&&a.type&&this.eV[a.type]?(d=this.eV[a.type],
c&&!d.Z6?Xu("Bad IDP event. Source window cannot be a popup."):d.Ms&&!d.Ms.call(this,b,a)?Xu("Bad IDP event."):d.Jf.call(this,b,a)):Xu("Bad IDP event.")}else Xu("Bad event, no corresponding Idp Stub.")};_.g.Vfa=function(a,b){return Tv(this,a.Bb,b.clientId)};_.g.Ufa=function(a,b){a=a.Bb;b=b.clientId;return!b||Tv(this,a,b)};_.g.l7=function(a,b){return Tv(this,a.Bb,b.clientId)};
_.g.Eda=function(a,b){a.yo=!0;a.Yw=!!b.cookieDisabled;Hv(a);for(b=0;b<a.Mr.length;b++)Iv(a,a.Mr[b].kp,a.Mr[b].callback);a.Mr=[]};_.g.Dda=function(a,b){b={error:b.error};a.yo=!0;a.SH=!0;a.bV=b;a.Mr=[];Hv(a)};_.g.eC=function(a,b){b.originIdp=a.Bb;this.dispatchEvent(b)};var Qv=new Pv,Uv=Qv,Vv={};Vv.idpReady={Jf:Uv.Eda};Vv.idpError={Jf:Uv.Dda};Vv.sessionStateChanged={Jf:Uv.eC,Ms:Uv.Vfa};Vv.sessionSelectorChanged={Jf:Uv.eC,Ms:Uv.Ufa};Vv.authResult={Jf:Uv.eC,Ms:Uv.l7,Z6:!0};Vv.displayIFrame={Jf:Uv.eC};
Qv.eV=Vv||{};dv(window,"message",function(a){Qv.vea.call(Qv,a)});
_.Wv=function(a,b){this.Oe=!1;if(!a)throw Error("ca");var c=[],d;for(d in a)c.push(a[d]);hv.call(this,c);this.Cd=[location.protocol,"//",location.host].join("");this.Xd=b.crossSubDomains?b.domain||this.Cd:this.Cd;if(!b)throw Error("da");if(!b.idpId)throw Error("ea");if(!_.mv(b.idpId,"authServerUrl")||!_.mv(b.idpId,"idpIFrameUrl"))throw Error("fa`"+b.idpId);this.Bb=b.idpId;this.Ob=void 0;this.u8=!!b.disableTokenRefresh;this.s9=!!b.forceTokenRefresh;this.Rga=!!b.skipTokenCache;this.Fs=!!b.supportBlocked3PCookies;
b.pluginName&&(this.kea=b.pluginName);b.ackExtensionDate&&(this.Q6=b.ackExtensionDate);this.I1=b.useFedCm;this.c9=this.Fs&&_.kv();this.setOptions(b);this.Kt=[];this.Yw=this.Ck=this.UV=!1;this.qj=void 0;this.kZ();this.Od=void 0;var e=this,f=function(){Xu("Token Manager is ready.");if(e.Kt.length)for(var h=0;h<e.Kt.length;h++)e.Kt[h].call(e);e.UV=!0;e.Kt=[]};iv.jB(this.Bb,function(h){e.Od=h;h.yo&&h.SH?(e.Ck=!0,e.qj=h.Wn(),e.Dr(e.qj)):(e.Yw=h.iW(),e.Ob?Nv(e.Od,e.Ob,e.kea,e.Q6,e.I1,e.c9,function(k){var l=
!!k.validOrigin,m=!!k.blocked,n=!!k.suppressed;k.invalidExtension?(e.qj={error:"Invalid value for ack_extension_date. Please refer to [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."},e.Ck=!0,e.Dr(e.qj)):l?m?n?(Yu("You have created a new client application that uses libraries for user authentication or authorization that are deprecated. New clients must use the new libraries instead. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."),
Rv(e.Bb,e.Ob),f()):(e.qj={error:"You have created a new client application that uses libraries for user authentication or authorization that are deprecated. New clients must use the new libraries instead. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."},e.Ck=!0,e.Dr(e.qj)):(Yu("Your client application uses libraries for user authentication or authorization that are deprecated. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."),
Rv(e.Bb,e.Ob),f()):(e.qj={error:"Not a valid origin for the client: "+e.Cd+" has not been registered for client ID "+e.Ob+". Please go to https://console.developers.google.com/ and register this origin for your project's client ID."},e.Ck=!0,e.Dr(e.qj))}):(Sv(e.Bb),f()))},this.Fs)};$u(_.Wv,hv);_.g=_.Wv.prototype;_.g.setOptions=function(){};_.g.kZ=function(){};_.g.Dr=function(){};_.g.iW=function(){return this.Yw};_.g.Wn=function(){return this.qj};Xv=function(a,b,c){return function(){b.apply(a,c)}};
_.Yv=function(a,b,c){if(a.UV)b.apply(a,c);else{if(a.Ck)throw a.qj;a.Kt.push(Xv(a,b,c))}};_.Wv.prototype.fQ=_.jb(11);_.Wv.prototype.wt=_.jb(9);_.$v=function(a,b){_.Wv.call(this,a,b);this.BY=new qv;this.Mk=this.Uo=null;Zv(this)};$u(_.$v,_.Wv);_.$v.prototype.setOptions=function(){};
var aw=function(a,b){a.Le={crossSubDomains:!!b.crossSubDomains,id:b.sessionSelectorId,domain:a.Xd};b.crossSubDomains&&(a.Le.policy=b.policy)},bw=function(a,b){if(!b.authParameters)throw Error("ga");if(!b.authParameters.scope)throw Error("ha");if(!b.authParameters.response_type)throw Error("ia");a.rn=b.authParameters;a.rn.redirect_uri||(a.rn.redirect_uri=[location.protocol,"//",location.host,location.pathname].join(""));a.Ij=_.bv(b.rpcAuthParameters||a.rn);if(!a.Ij.scope)throw Error("ja");if(!a.Ij.response_type)throw Error("ka");
a:{var c=a.Ij.response_type.split(" ");for(var d=0,e=c.length;d<e;d++)if(c[d]&&!ev[c[d]]){c=!0;break a}c=!1}if(c)throw Error("la");if(b.enableSerialConsent||b.enableGranularConsent)a.rn.enable_granular_consent=!0,a.Ij.enable_serial_consent=!0;b.authResultIdentifier&&(a.m7=b.authResultIdentifier);b.spec_compliant&&(a.Ij.spec_compliant=b.spec_compliant)};
_.$v.prototype.kZ=function(){var a=this;Qv.addEventListener(_.Ov.Q5,function(b){a.Oe&&a.Le&&b.originIdp==a.Bb&&!b.crossSubDomains==!a.Le.crossSubDomains&&b.domain==a.Le.domain&&b.id==a.Le.id&&a.SX(b)});Qv.addEventListener(_.Ov.uE,function(b){a.Oe&&b.originIdp==a.Bb&&b.clientId==a.Ob&&a.TX(b)});Qv.addEventListener(_.Ov.Rs,function(b){_.ov=void 0;a.Oe&&b.originIdp==a.Bb&&b.clientId==a.Ob&&b.id==a.Jk&&(a.Uo&&(window.clearTimeout(a.Uo),a.Uo=null),a.Jk=void 0,a.Po(b))});Qv.addEventListener(_.Ov.a3,function(b){a.Oe&&
b.originIdp==a.Bb&&(b.hide?a.Od.hide():a.Od.show())})};_.$v.prototype.SX=function(){};_.$v.prototype.TX=function(){};_.$v.prototype.Po=function(){};var dw=function(a,b){cw(a);a.u8||(a.Mk=jv.k8(function(){a.zj(!0)},b-3E5),navigator.onLine&&a.Mk.start())},cw=function(a){a.Mk&&(a.Mk.clear(),a.Mk=null)},Zv=function(a){var b=window;fv()&&(b=document.body);dv(b,"online",function(){a.Mk&&a.Mk.start()});dv(b,"offline",function(){a.Mk&&a.Mk.clear()})};_.$v.prototype.zj=function(){};_.$v.prototype.tX=_.jb(12);
_.$v.prototype.Aca=function(a,b){if(!this.Ob)throw Error("pa");this.Od.hB(this.Ob,this.Ij,this.Le,a,b)};_.$v.prototype.hB=function(a,b){_.Yv(this,this.Aca,[a,b])};_.fw=function(a){this.Fe=void 0;this.Mh=!1;this.Vr=void 0;_.$v.call(this,ew,a)};$u(_.fw,_.$v);var ew={uO:"noSessionBound",ct:"userLoggedOut",m2:"activeSessionChanged",uE:"sessionStateChanged",s6:"tokenReady",r6:"tokenFailed",Rs:"authResult",ERROR:"error"};
_.fw.prototype.setOptions=function(a){if(!a.clientId)throw Error("qa");this.Ob=a.clientId;this.Da=a.id;aw(this,a);bw(this,a)};_.fw.prototype.Dr=function(a){this.dispatchEvent({type:ew.ERROR,error:"idpiframe_initialization_failed",details:a.error,idpId:this.Bb})};var gw=function(a){cw(a);a.Vr=void 0;a.HI=void 0};_.g=_.fw.prototype;
_.g.SX=function(a){var b=a.newValue||{};if(this.Fe!=b.hint||this.Mh!=!!b.disabled){a=this.Fe;var c=!this.Fe||this.Mh;gw(this);this.Fe=b.hint;this.Mh=!!b.disabled;(b=!this.Fe||this.Mh)&&!c?this.dispatchEvent({type:ew.ct,idpId:this.Bb}):b||(a!=this.Fe&&this.dispatchEvent({type:ew.m2,idpId:this.Bb}),this.Fe&&this.zj())}};
_.g.TX=function(a){this.Mh||(this.Fe?a.user||this.Vr?a.user==this.Fe&&(this.Vr?a.sessionState?this.Vr=a.sessionState:(gw(this),this.dispatchEvent({type:ew.ct,idpId:this.Bb})):a.sessionState&&(this.Vr=a.sessionState,this.zj())):this.zj():this.dispatchEvent({type:ew.uE,idpId:this.Bb}))};_.g.Po=function(a){this.dispatchEvent({type:ew.Rs,authResult:a.authResult})};_.g.yu=_.jb(14);_.g.ru=function(a){_.Yv(this,this.DG,[a])};_.g.DG=function(a){Lv(this.Od,this.Le,a)};
_.g.tD=function(a,b,c,d){d=d===void 0?!1:d;if(!a)throw Error("ra");gw(this);this.Fe=a;this.Mh=!1;b&&_.Mv(this.Od,this.Le,!1,this.Fe);this.Oe=!0;this.zj(c,!0,d)};_.g.start=function(){_.Yv(this,this.Pw,[])};
_.g.Pw=function(){var a=this.Ob==cv("client_id")?cv("login_hint"):void 0;var b=this.Ob==cv("client_id")?cv("state"):void 0;this.rJ=b;if(a)window.history.replaceState?window.history.replaceState(null,document.title,window.location.href.split("#")[0]):window.location.href.hash="",this.tD(a,!0,!0,!0);else{var c=this;this.ru(function(d){c.Oe=!0;d&&d.hint?(gw(c),c.Fe=d.hint,c.Mh=!!d.disabled,c.Mh?c.dispatchEvent({type:ew.ct,idpId:c.Bb}):c.tD(d.hint)):(gw(c),c.Fe=void 0,c.Mh=!(!d||!d.disabled),c.dispatchEvent({type:ew.uO,
autoOpenAuthUrl:!c.Mh,idpId:c.Bb}))})}};_.g.o9=function(){var a=this;this.ru(function(b){b&&b.hint?b.disabled?a.dispatchEvent({type:ew.ct,idpId:a.Bb}):a.zj(!0):a.dispatchEvent({type:ew.uO,idpId:a.Bb})})};_.g.TS=function(){_.Yv(this,this.o9,[])};
_.g.zj=function(a,b,c){var d=this;this.Od.zj(this.Ob,this.Ij,this.Fe,this.Le,function(e,f){(f=f||e.error)?f=="user_logged_out"?(gw(d),d.dispatchEvent({type:ew.ct,idpId:d.Bb})):(d.HI=null,d.dispatchEvent({type:ew.r6,idpId:d.Bb,error:f})):(d.HI=e,d.Vr=e.session_state,dw(d,e.expires_at),e.idpId=d.Bb,b&&d.rJ&&(e.state=d.rJ,d.rJ=void 0),d.dispatchEvent({type:ew.s6,idpId:d.Bb,response:e}))},this.Da,a,!1,c===void 0?!1:c)};_.g.revoke=_.jb(7);_.g.GZ=_.jb(15);
_.hw=function(a){this.tn=null;_.$v.call(this,{},a);this.Oe=!0};$u(_.hw,_.$v);_.g=_.hw.prototype;_.g.setOptions=function(a){if(!a.clientId)throw Error("qa");this.Ob=a.clientId;this.Da=a.id;aw(this,a);bw(this,a)};_.g.Dr=function(a){this.tn&&(this.tn({authResult:{error:"idpiframe_initialization_failed",details:a.error}}),this.tn=null)};_.g.Po=function(a){if(this.tn){var b=this.tn;this.tn=null;b(a)}};_.g.yu=_.jb(13);_.g.ru=function(a){this.Ck?a(this.Wn()):_.Yv(this,this.DG,[a])};
_.g.DG=function(a){Lv(this.Od,this.Le,a)};_.iw=function(a,b,c){a.Ck?c(a.Wn()):_.Yv(a,a.Qda,[b,c])};_.hw.prototype.Qda=function(a,b){this.Od.zj(this.Ob,this.Ij,a,this.Le,function(c,d){d?b({error:d}):b(c)},this.Da,this.s9,this.Rga)};_.hw.prototype.HW=_.jb(16);
var jw=function(a){var b=window.location;a=_.sc(a);a!==void 0&&b.assign(a)},kw=function(a){return Array.prototype.concat.apply([],arguments)},lw=function(){try{var a=Array.from((window.crypto||window.msCrypto).getRandomValues(new Uint8Array(64)))}catch(c){a=[];for(var b=0;b<64;b++)a[b]=Math.floor(Math.random()*256)}return _.Nh(a,3).substring(0,64)},mw=function(a){var b=[],c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];if(d===null||d===void 0)d="";b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))}return b.join("&")},
nw=function(a,b){(b===void 0?0:b)||window.addEventListener("hashchange",function(){location.hash.includes("client_id")&&window.location.reload()});jw(a)},ow=function(a,b,c){if(!a.Oe)throw Error("ma");b?_.Mv(a.Od,a.Le,!0,void 0,c):_.Mv(a.Od,a.Le,!0,a.Fe,c)},pw=function(a){if(!a.Oe)throw Error("ma");return a.HI},qw,rw,sw,tw,uw,vw,ww,xw,yw,zw,Aw,Bw,Cw,Dw,Gw,Jw,Kw;
_.hw.prototype.HW=_.pb(16,function(a,b){var c=this.Od,d=this.Ob,e=this.Le,f=_.bv(this.Ij);delete f.response_type;_.Jv(c,"getOnlineCode",{clientId:d,loginHint:a,request:f,sessionSelector:e},b)});_.fw.prototype.GZ=_.pb(15,function(a){pw(this)&&pw(this).access_token&&(this.Od.revoke(this.Ob,pw(this).access_token,a),ow(this,!0))});
_.fw.prototype.yu=_.pb(14,function(){var a=this;return function(b){b&&b.authResult&&b.authResult.login_hint&&(a.aC?(b.authResult.client_id=a.Ob,nw(a.aC+"#"+mw(b.authResult))):a.tD(b.authResult.login_hint,a.Mh||b.authResult.login_hint!=a.Fe,!0,!0))}});
_.hw.prototype.yu=_.pb(13,function(a){var b=this;return function(c){c&&c.authResult&&c.authResult.login_hint?b.ru(function(d){_.Mv(b.Od,b.Le,d&&d.disabled,c.authResult.login_hint,function(){_.iw(b,c.authResult.login_hint,a)})}):a(c&&c.authResult&&c.authResult.error?c.authResult:c&&c.authResult&&!c.authResult.login_hint?{error:"wrong_response_type"}:{error:"unknown_error"})}});_.$v.prototype.tX=_.pb(12,function(){this.Ob&&_.Jv(this.Od,"startPolling",{clientId:this.Ob,origin:this.Cd,id:this.Jk})});
_.Gv.prototype.revoke=_.pb(8,function(a,b,c){_.Jv(this,"revoke",{clientId:a,token:b},c)});_.fw.prototype.revoke=_.pb(7,function(a){_.Yv(this,this.GZ,[a])});qw="openid email profile https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/plus.me https://www.googleapis.com/auth/plus.login".split(" ");
rw=function(){var a=navigator.userAgent,b;if(b=!!a&&a.indexOf("CriOS")!=-1)b=-1,(a=a.match(/CriOS\/(\d+)/))&&a[1]&&(b=parseInt(a[1],10)||-1),b=b<48;return b};
sw=function(){var a=navigator.userAgent.toLowerCase();if(!(a.indexOf("safari/")>-1&&a.indexOf("chrome/")<0&&a.indexOf("crios/")<0&&a.indexOf("android")<0))return!1;var b=RegExp("version/(\\d+)\\.(\\d+)[\\.0-9]*").exec(navigator.userAgent.toLowerCase());if(!b||b.length<3)return!1;a=parseInt(b[1],10);b=parseInt(b[2],10);return a>12||a==12&&b>=1};tw=function(a){return a.length>0&&a.every(function(b){return qw.includes(b)})};
uw=function(a,b,c,d,e,f,h){var k=_.mv(a,"authServerUrl");if(!k)throw Error("X`"+a);a=_.bv(d);a.response_type=h||"permission";a.client_id=c;a.ss_domain=b;if(f&&f.extraQueryParams)for(var l in f.extraQueryParams)a[l]=f.extraQueryParams[l];(b=e)&&!(b=sw())&&(b=navigator.userAgent.toLowerCase(),b.indexOf("ipad;")>-1||b.indexOf("iphone;")>-1?(b=RegExp("os (\\d+)_\\d+(_\\d+)? like mac os x").exec(navigator.userAgent.toLowerCase()),b=!b||b.length<2?!1:parseInt(b[1],10)>=14):b=!1);b&&!a.prompt&&(a.prompt=
"select_account");return k+(k.indexOf("?")<0?"?":"&")+mw(a)};vw=function(a,b,c,d){if(!a.Ob)throw Error("na");a.Jk=c||a.m7||"auth"+Math.floor(Math.random()*1E6+1);b=b||{};b.extraQueryParams=b.extraQueryParams||{};if(!b.extraQueryParams.redirect_uri){var e=a.Cd.split("//");c=b.extraQueryParams;var f=e[0],h=e[1];e=a.Jk;var k=f.indexOf(":");k>0&&(f=f.substring(0,k));f=["storagerelay://",f,"/",h,"?"];f.push("id="+e);c.redirect_uri=f.join("")}return uw(a.Bb,a.Xd,a.Ob,a.rn,!0,b,d)};
ww=function(a,b,c){if(!a.Ob)throw Error("na");return uw(a.Bb,a.Xd,a.Ob,a.rn,!1,b,c)};xw=function(a,b){a.Uo&&window.clearTimeout(a.Uo);a.Uo=window.setTimeout(function(){a.Jk==b&&(_.ov=void 0,a.Uo=null,a.Jk=void 0,a.Po({authResult:{error:"popup_closed_by_user"}}))},1E3)};
yw=function(a,b,c){if(!a.Ob)throw Error("oa");c=c||{};c=vw(a,c.sessionMeta,c.oneTimeId,c.responseType);(Object.hasOwnProperty.call(window,"ActiveXObject")&&!window.ActiveXObject||rw())&&_.Yv(a,a.tX,[]);var d=a.Jk;a.BY.open(c,b,function(){a.Jk==d&&xw(a,d)},function(){a.Jk=void 0;a.Po({authResult:{error:"popup_blocked_by_browser"}})})};
zw=function(a,b){var c=b||{};b=_.bv(a.rn);if(c.sessionMeta&&c.sessionMeta.extraQueryParams)for(var d in c.sessionMeta.extraQueryParams)b[d]=c.sessionMeta.extraQueryParams[d];var e;c.sessionMeta.extraQueryParams.scope&&(e=c.sessionMeta.extraQueryParams.scope.split(" "));!e&&b.scope&&(e=b.scope.split(" "));delete b.redirect_uri;delete b.origin;delete b.client_id;delete b.scope;b.prompt=="select_account"&&delete b.prompt;b.gsiwebsdk="fedcm";b.ss_domain=a.Xd;d=_.mv(a.Bb,"fedcmConfigUrl");c=c.responseType;
b.response_type=c;b.scope=e.join(" ");!b.nonce&&c.includes("id_token")&&(b.nonce="notprovided");c=navigator.userActivation.isActive?"active":"passive";e=tw(e)?["name","email","picture"]:[];return{identity:{providers:[{configURL:d,clientId:a.Ob,fields:e,params:b}],mode:c},mediation:"required"}};
Aw=function(a,b,c){if(!a.Ob)throw Error("oa");b=zw(a,b);navigator.credentials.get(b).then(function(d){d=JSON.parse(d.token);var e={client_id:d.client_id,login_hint:d.login_hint,expires_in:3600,scope:d.scope};d.code&&(e.code=d.code);d.id_token&&(e.id_token=d.id_token);a.Po({type:_.Ov.Rs,idpId:a.Bb,authResult:e})},function(d){d.message.indexOf("identity-credentials-get")>=0||d.message.indexOf("Content Security Policy")>=0?c():a.Po({type:_.Ov.Rs,idpId:a.Bb,authResult:{error:d}})})};
Bw=function(a,b,c){a.Fs&&_.kv()?Aw(a,c,function(){return yw(a,b,c)}):yw(a,b,c)};Cw=function(a,b){b=b||{};var c=ww(a,b.sessionMeta,b.responseType);a.Fs&&_.kv()&&a.I1?(a.aC=b.sessionMeta.extraQueryParams.redirect_uri,Aw(a,b,function(){return nw(c,!0)})):nw(c,!0)};Dw=function(a,b,c){a.Ck?c(a.Wn()):_.Yv(a,a.HW,[b,c])};_.Ew=function(a){_.Be(_.Me,"le",[]).push(a)};
_.Fw=function(a){for(var b=[],c=0,d=0;c<a.length;){var e=a[c++];if(e<128)b[d++]=String.fromCharCode(e);else if(e>191&&e<224){var f=a[c++];b[d++]=String.fromCharCode((e&31)<<6|f&63)}else if(e>239&&e<365){f=a[c++];var h=a[c++],k=a[c++];e=((e&7)<<18|(f&63)<<12|(h&63)<<6|k&63)-65536;b[d++]=String.fromCharCode(55296+(e>>10));b[d++]=String.fromCharCode(56320+(e&1023))}else f=a[c++],h=a[c++],b[d++]=String.fromCharCode((e&15)<<12|(f&63)<<6|h&63)}return b.join("")};
Gw=function(a){var b=[];_.Oh(a,function(c){b.push(c)});return b};_.Hw=function(a,b){_.ti[b||"token"]=a};_.Iw=function(a){delete _.ti[a||"token"]};Kw=function(){if(typeof MessageChannel!=="undefined"){var a=new MessageChannel,b={},c=b;a.port1.onmessage=function(){if(b.next!==void 0){b=b.next;var d=b.cb;b.cb=null;d()}};return function(d){c.next={cb:d};c=c.next;a.port2.postMessage(0)}}return function(d){_.Xa.setTimeout(d,0)}};_.gv={parse:function(a){a=_.Qf("["+String(a)+"]");if(a===!1||a.length!==1)throw new SyntaxError("JSON parsing failed.");return a[0]},stringify:function(a){return _.Rf(a)}};_.hw.prototype.pG=function(a,b){_.Yv(this,this.e9,[a,b])};_.hw.prototype.e9=function(a,b){this.Od.pG(this.Ob,a,this.Ij,this.Le,b)};_.Gv.prototype.pG=function(a,b,c,d,e){c=_.bv(c);_.Jv(this,"gsi:fetchLoginHint",{clientId:a,loginHint:b,request:c,sessionSelector:d},e)};var Lw,Mw=["client_id","cookie_policy","scope"],Nw="client_id cookie_policy fetch_basic_profile hosted_domain scope openid_realm disable_token_refresh login_hint ux_mode redirect_uri state prompt oidc_spec_compliant nonce enable_serial_consent enable_granular_consent include_granted_scopes response_type session_selection plugin_name ack_extension_date use_fedcm gsiwebsdk".split(" "),Ow=["authuser","after_redirect","access_type","hl"],Pw=["login_hint","prompt"],Qw={clientid:"client_id",cookiepolicy:"cookie_policy"},
Rw=["approval_prompt","authuser","login_hint","prompt","hd"],Sw=["login_hint","g-oauth-window","status"],Tw=Math.min(_.Xe("oauth-flow/authWindowWidth",599),screen.width-20),Uw=Math.min(_.Xe("oauth-flow/authWindowHeight",600),screen.height-30);var Vw=function(a){_.lb.call(this,a)};_.y(Vw,_.lb);Vw.prototype.name="gapi.auth2.ExternallyVisibleError";var Ww=function(){};Ww.prototype.select=function(a,b){if(a.sessions&&a.sessions.length==1&&(a=a.sessions[0],a.login_hint)){b(a);return}b()};var Xw=function(){};Xw.prototype.select=function(a,b){if(a.sessions&&a.sessions.length)for(var c=0;c<a.sessions.length;c++){var d=a.sessions[c];if(d.login_hint){b(d);return}}b()};var Yw=function(a){this.n7=a};
Yw.prototype.select=function(a,b){if(a.sessions)for(var c=0;c<a.sessions.length;c++){var d=a.sessions[c];if(d.session_state&&d.session_state.extraQueryParams&&d.session_state.extraQueryParams.authuser==this.n7){d.login_hint?b(d):b();return}}b()};var Zw=function(a){this.ue=a;this.MC=[]};Zw.prototype.select=function(a){var b=0,c=this,d=function(e){if(e)a(e);else{var f=c.MC[b];f?(b++,c.ue.hB(function(h){h?f.select(h,d):d()})):a()}};d()};var $w=function(a){a=new Zw(a);a.MC.push(new Ww);return a},ax=function(a){a=new Zw(a);a.MC.push(new Xw);return a},bx=function(a,b){b===void 0||b===null?b=$w(a):(a=new Zw(a),a.MC.push(new Yw(b)),b=a);return b};var cx=function(a){this.Jf=a;this.isActive=!0};cx.prototype.remove=function(){this.isActive=!1};cx.prototype.trigger=function(){};var dx=function(a){this.remove=function(){a.remove()};this.trigger=function(){a.trigger()}},ex=function(){this.qc=[]};ex.prototype.add=function(a){this.qc.push(a)};ex.prototype.notify=function(a){for(var b=this.qc,c=[],d=0;d<b.length;d++){var e=b[d];e.isActive&&(c.push(e),e=fx(e.Jf,a),e=(0,_.qk)(e),e=(0,_.ok)(e),Jw||(Jw=Kw()),Jw(e))}this.qc=c};var fx=function(a,b){return function(){a(b)}};var ox=function(a){this.La=null;this.Iha=new gx(this);this.qc=new ex;a!=void 0&&this.set(a)};ox.prototype.set=function(a){a!=this.La&&(this.La=a,this.Iha.value=a,this.qc.notify(this.La))};ox.prototype.get=function(){return this.La};ox.prototype.na=function(a){a=new px(this,a);this.qc.add(a);return a};ox.prototype.get=ox.prototype.get;var px=function(a,b){cx.call(this,b);this.Eca=a};_.y(px,cx);px.prototype.trigger=function(){var a=this.Jf;a(this.Eca.get())};
var gx=function(a){this.value=null;this.na=function(b){return new dx(a.na(b))}};var qx={cka:"fetch_basic_profile",dla:"login_hint",Cma:"prompt",Ima:"redirect_uri",ana:"scope",xoa:"ux_mode",Mna:"state"},rx=function(a){this.Ka={};if(a&&!_.Gh(a))if(typeof a.get=="function")this.Ka=a.get();else for(var b in qx){var c=qx[b];c in a&&(this.Ka[c]=a[c])}};rx.prototype.get=function(){return this.Ka};rx.prototype.J_=function(a){this.Ka.scope=a;return this};rx.prototype.Iu=function(){return this.Ka.scope};
var sx=function(a,b){var c=a.Ka.scope;b=kw(b.split(" "),c?c.split(" "):[]);_.Fh(b);a.Ka.scope=b.join(" ")};_.g=rx.prototype;_.g.vga=function(a){this.Ka.prompt=a;return this};_.g.A$=function(){return this.Ka.prompt};_.g.Yfa=function(){_.Vf.warn("Property app_package_name no longer supported and was not set");return this};_.g.D9=function(){_.Vf.warn("Property app_package_name no longer supported")};_.g.uf=function(a){this.Ka.state=a};_.g.getState=function(){return this.Ka.state};var tx=function(){return["toolbar=no","location="+(window.opera?"no":"yes"),"directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,copyhistory=no","width="+Tw,"height="+Uw,"top="+(screen.height-Uw)/2,"left="+(screen.width-Tw)/2].join()},ux=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;var b=(a.split(".")[1]+"...").replace(/^((....)+).?.?.?$/,"$1");a=JSON;var c=a.parse;b=Gw(b);return c.call(a,_.Fw(b))},vx=function(){Lw=_.Xe("auth2/idpValue","google");var a=_.Xe("oauth-flow/authUrl",
"https://accounts.google.com/o/oauth2/auth"),b=_.Xe("oauth-flow/idpIframeUrl","https://accounts.google.com/o/oauth2/iframe");a={fedcmConfigUrl:_.Xe("oauth-flow/fedcmConfigUrl","https://accounts.google.com/o/fedcm/config.json"),authServerUrl:a,idpIFrameUrl:b};_.nv(Lw,a)},wx=function(a,b,c){for(var d=0;d<b.length;d++){var e=b[d];if(d===b.length-1){a[e]=c;break}_.vb(a[e])||(a[e]={});a=a[e]}},xx=function(){var a=window.location.origin;a||(a=window.location.protocol+"//"+window.location.host);return a},
zx=function(){var a=yx();a.storage_path&&window.sessionStorage.setItem(a.storage_path,xx()+window.location.pathname);if(a.status.toLowerCase()=="enforced")throw new Vw("gapi.auth2 is disabled on this website, but it is still used on page "+window.location.href);a.status.toLowerCase()=="informational"&&_.Vf.warn("gapi.auth2 is disabled on this website, but it is still used on page "+window.location.href)},Ax=function(a){return _.li.get("GSI_ALLOW_3PCD")==="1"?!0:a===!1?!1:a===!0||(_.Me.le||[]).includes("fedcm_migration_mod")?
!0:!1};var Bx=function(a){var b=a?(b=ux(a))?b.sub:null:null;this.Da=b;this.Ic=a?_.fk(a):null};_.g=Bx.prototype;_.g.getId=function(){return this.Da};_.g.MG=function(){var a=ux(this.Ic);return a?a.hd:null};_.g.xg=function(){return!!this.Ic};_.g.Tl=function(a){if(a)return this.Ic;a=Cx;var b=_.fk(this.Ic);!a.PA||a.MH||a.dba||(delete b.access_token,delete b.scope);return b};_.g.rK=function(){return Cx.rK()};_.g.Zk=function(){this.Ic=null};_.g.e$=function(){return this.Ic?this.Ic.scope:null};
_.g.update=function(a){this.Da=a.Da;this.Ic=a.Ic;this.Ic.id_token?this.ly=new Dx(this.Ic):this.ly&&(this.ly=null)};var Ex=function(a){return a.Ic&&typeof a.Ic.session_state=="object"?_.fk(a.Ic.session_state.extraQueryParams||{}):{}};_.g=Bx.prototype;_.g.BG=function(){var a=Ex(this);return a&&a.authuser!==void 0&&a.authuser!==null?a.authuser:null};
_.g.Yk=function(a){var b=Cx,c=new rx(a);b.MH=c.Iu()?!0:!1;Cx.PA&&sx(c,"openid profile email");return new _.xk(function(d,e){var f=Ex(this);f.login_hint=this.getId();f.scope=c.Iu();Fx(b,d,e,f)},this)};_.g.Pu=function(a){return new _.xk(function(b,c){var d=a||{},e=Cx;d.login_hint=this.getId();e.Pu(d).then(b,c)},this)};_.g.Q$=function(a){return this.Yk(a)};_.g.disconnect=function(){return Cx.disconnect()};_.g.G9=function(){return this.ly};
_.g.xA=function(a){if(!this.xg())return!1;var b=this.Ic&&this.Ic.scope?this.Ic.scope.split(" "):"";return _.Nb(a?a.split(" "):[],function(c){return _.tb(b,c)})};var Dx=function(a){a=ux(a);this.x9=a.sub;this.dh=a.name;this.M$=a.given_name;this.b9=a.family_name;this.jV=a.picture;this.gz=a.email};_.g=Dx.prototype;_.g.getId=function(){return this.x9};_.g.getName=function(){return this.dh};_.g.c$=function(){return this.M$};_.g.Y9=function(){return this.b9};_.g.k$=function(){return this.jV};_.g.Qn=function(){return this.gz};var yx,Gx;yx=function(){var a=_.li.get("G_AUTH2_MIGRATION");if(!a)return{status:"none"};a=/(enforced|informational)(?::(.*))?/i.exec(a);return a?{status:a[1].toLowerCase(),storage_path:a[2]}:(_.Vf.warn("The G_AUTH2_MIGRATION cookie value is not valid."),{status:"none"})};Gx=function(a){var b=location;if(a&&a!="none")return a=="single_host_origin"?b.protocol+"//"+b.host:a};
_.Hx=function(a){if(!a)throw new Vw("No cookiePolicy");var b=window.location.hostname;a=="single_host_origin"&&(a=window.location.protocol+"//"+b);if(a=="none")return null;var c=/^(https?:\/\/)([0-9.\-_A-Za-z]+)(?::(\d+))?$/.exec(a);if(!c)throw new Vw("Invalid cookiePolicy");a=c[2];c=c[1];var d={};d.dotValue=a.split(".").length;d.isSecure=c.indexOf("https")!=-1;d.domain=a;if(!_.Mj(b,"."+a)&&!_.Mj(b,a))throw new Vw("Invalid cookiePolicy domain");return d};var Jx=function(a){var b=a||{},c=Ix();_.Bb(Nw,function(d){typeof b[d]==="undefined"&&typeof c[d]!=="undefined"&&(b[d]=c[d])});return b},Ix=function(){for(var a={},b=document.getElementsByTagName("meta"),c=0;c<b.length;++c)if(b[c].name){var d=b[c].name;if(d.indexOf("google-signin-")==0){d=d.substring(14);var e=b[c].content;Qw[d]&&(d=Qw[d]);_.tb(Nw,d)&&e&&(a[d]=e=="true"?!0:e=="false"?!1:e)}}return a},Kx=function(a){return String(a).replace(/_([a-z])/g,function(b,c){return c.toUpperCase()})},Lx=function(a){_.Bb(Nw,
function(b){var c=Kx(b);typeof a[c]!=="undefined"&&typeof a[b]==="undefined"&&(a[b]=a[c],delete a[c])})},Mx=function(a){a=Jx(a);Lx(a);a.cookie_policy||(a.cookie_policy="single_host_origin");var b=Nw+Ow,c;for(c in a)b.indexOf(c)<0&&delete a[c];return a},Nx=function(a,b){if(!a)throw new Vw("Empty initial options.");for(var c=0;c<Mw.length;++c)if(!(b&&Mw[c]=="scope"||a[Mw[c]]))throw new Vw("Missing required parameter '"+Mw[c]+"'");_.Hx(a.cookie_policy)},Px=function(a){var b={authParameters:{redirect_uri:void 0,
response_type:"token id_token",scope:a.scope,"openid.realm":a.openid_realm,include_granted_scopes:!0},clientId:a.client_id,crossSubDomains:!0,domain:Gx(a.cookie_policy),disableTokenRefresh:!!a.disable_token_refresh,idpId:Lw};Ox(b,a);_.Bb(Pw,function(d){a[d]&&(b.authParameters[d]=a[d])});typeof a.enable_serial_consent=="boolean"&&(b.enableSerialConsent=a.enable_serial_consent);typeof a.enable_granular_consent=="boolean"&&(b.enableGranularConsent=a.enable_granular_consent);if(a.plugin_name)b.pluginName=
a.plugin_name;else{var c=_.Xe("auth2/pluginName");c&&(b.pluginName=c)}a.ack_extension_date&&(b.authParameters.ack_extension_date=a.ack_extension_date,b.ackExtensionDate=a.ack_extension_date);typeof a.use_fedcm==="boolean"&&(b.useFedCm=a.use_fedcm);return b},Ox=function(a,b){var c=b.oidc_spec_compliant;b=b.nonce;c&&(a.spec_compliant=c,b=b||lw());b&&(a.authParameters.nonce=b,a.forceTokenRefresh=!0,a.skipTokenCache=!0)},Ux=function(a){var b=a.client_id,c=a.cookie_policy,d=a.scope,e=a.openid_realm,f=
a.hosted_domain,h=a.oidc_spec_compliant,k=a.nonce,l=Qx(a),m={authParameters:{response_type:l,scope:d,"openid.realm":e},rpcAuthParameters:{response_type:l,scope:d,"openid.realm":e},clientId:b,crossSubDomains:!0,domain:Gx(c),idpId:Lw};f&&(m.authParameters.hd=f,m.rpcAuthParameters.hd=f);h&&(m.rpcAuthParameters.spec_compliant=h,k=k||lw());k&&(m.authParameters.nonce=k,m.rpcAuthParameters.nonce=k,m.forceTokenRefresh=!0,m.skipTokenCache=!0);_.Bb(Pw.concat(Ow),function(n){a[n]&&(m.authParameters[n]=a[n])});
a.authuser!==void 0&&a.authuser!==null&&(m.authParameters.authuser=a.authuser);typeof a.include_granted_scopes=="boolean"&&(b=new Rx(a.response_type||"token"),Sx(b)&&(m.authParameters.include_granted_scopes=a.include_granted_scopes),Tx(b)&&(m.rpcAuthParameters.include_granted_scopes=a.include_granted_scopes,a.include_granted_scopes===!1&&(m.forceTokenRefresh=!0,m.skipTokenCache=!0)));typeof a.enable_serial_consent=="boolean"&&(m.enableSerialConsent=a.enable_serial_consent);typeof a.enable_granular_consent==
"boolean"&&(m.enableGranularConsent=a.enable_granular_consent);a.plugin_name?m.pluginName=a.plugin_name:(b=_.Xe("auth2/pluginName"))&&(m.pluginName=b);a.ack_extension_date&&(m.authParameters.ack_extension_date=a.ack_extension_date,m.rpcAuthParameters.ack_extension_date=a.ack_extension_date,m.ackExtensionDate=a.ack_extension_date);typeof a.use_fedcm==="boolean"&&(m.useFedCm=a.use_fedcm);return m},Qx=function(a){a=new Rx(a.response_type||"token");var b=[];Tx(a)&&b.push("token");Vx(a,"id_token")&&b.push("id_token");
b.length==0&&(b=["token","id_token"]);return b.join(" ")},Wx=["permission","id_token"],Xx=/(^|[^_])token/,Rx=function(a){this.Pr=[];this.hI(a)};Rx.prototype.hI=function(a){a?((a.indexOf("permission")>=0||a.match(Xx))&&this.Pr.push("permission"),a.indexOf("id_token")>=0&&this.Pr.push("id_token"),a.indexOf("code")>=0&&this.Pr.push("code")):this.Pr=Wx};var Sx=function(a){return Vx(a,"code")},Tx=function(a){return Vx(a,"permission")};Rx.prototype.toString=function(){return this.Pr.join(" ")};
var Vx=function(a,b){var c=!1;_.Bb(a.Pr,function(d){d==b&&(c=!0)});return c};var Zx=function(a,b,c){this.tJ=b;this.sda=a;for(var d in a)a.hasOwnProperty(d)&&Yx(this,d);if(c&&c.length)for(a=0;a<c.length;a++)this[c[a]]=this.tJ[c[a]]},Yx=function(a,b){a[b]=function(){return a.sda[b].apply(a.tJ,arguments)}};Zx.prototype.then=function(a,b,c){var d=this;return _.Bk().then(function(){return $x(d.tJ,a,b,c)})};_.mk(Zx);var Cx,ay,cy;Cx=null;_.by=function(){return Cx?ay():null};ay=function(){return new Zx(cy.prototype,Cx,["currentUser","isSignedIn"])};cy=function(a){delete a.include_granted_scopes;this.Ka=Px(a);this.i8=a.cookie_policy;this.dba=!!a.scope;(this.PA=a.fetch_basic_profile!==!1)&&(this.Ka.authParameters.scope=dy(this,"openid profile email"));this.Ka.supportBlocked3PCookies=Ax(a.use_fedcm);this.gv=a.hosted_domain;this.Gha=a.ux_mode||"popup";this.aC=a.redirect_uri||null;this.eI()};
cy.prototype.eI=function(){this.currentUser=new ox(new Bx(null));this.isSignedIn=new ox(!1);this.ue=new _.fw(this.Ka);this.UA=this.ir=null;this.oca=new _.xk(function(a,b){this.ir=a;this.UA=b},this);this.DB={};this.vv=!0;ey(this);this.ue.start()};
var ey=function(a){a.ue.addEventListener("error",function(b){a.vv&&a.ir&&(a.vv=!1,a.UA({error:b.error,details:b.details}),a.ir=null,a.UA=null)});a.ue.addEventListener("authResult",function(b){b&&b.authResult&&a.Cf(b);a.ue.yu()(b)});a.ue.addEventListener("tokenReady",function(b){var c=new Bx(b.response);if(a.gv&&a.gv!=c.MG())a.Cf({type:"tokenFailed",reason:"Account domain does not match hosted_domain specified by gapi.auth2.init.",accountDomain:c.MG(),expectedDomain:a.gv});else{a.currentUser.get().update(c);
var d=a.currentUser;d.qc.notify(d.La);a.isSignedIn.set(!0);c=c.BG();(d=_.Hx(a.i8))&&c&&_.li.set(["G_AUTHUSER_",window.location.protocol==="https:"&&d.ff?"S":"H",d.Ti].join(""),c,{domain:d.domain,secure:d.isSecure});_.Hw(b.response);a.Cf(b)}});a.ue.addEventListener("noSessionBound",function(b){a.vv&&b.autoOpenAuthUrl?(a.vv=!1,$w(a.ue).select(function(c){if(c&&c.login_hint){var d=a.ue;_.Yv(d,d.tD,[c.login_hint,!0])}else a.currentUser.set(new Bx(null)),a.isSignedIn.set(!1),_.Iw(),a.Cf(b)})):(a.currentUser.set(new Bx(null)),
a.isSignedIn.set(!1),_.Iw(),a.Cf(b))});a.ue.addEventListener("tokenFailed",function(b){a.Cf(b)});a.ue.addEventListener("userLoggedOut",function(b){a.currentUser.get().Zk();var c=a.currentUser;c.qc.notify(c.La);a.isSignedIn.set(!1);_.Iw();a.Cf(b)})},$x=function(a,b,c,d){return a.oca.then(function(e){if(b)return b(e.O$)},c,d)};cy.prototype.Cf=function(a){if(a){this.vv=!1;var b=a.type||"";if(this.DB[b])this.DB[b](a);this.ir&&(this.ir({O$:this}),this.UA=this.ir=null)}};
var fy=function(a,b){_.Zb(b,function(c,d){a.DB[d]=function(e){a.DB={};c(e)}})},Fx=function(a,b,c,d){d=_.fk(d);a.gv&&(d.hd=a.gv);var e=d.ux_mode||a.Gha;delete d.ux_mode;delete d.app_package_name;var f={sessionMeta:{extraQueryParams:d},responseType:"permission id_token"};e=="redirect"?(d.redirect_uri||(d.redirect_uri=a.aC||xx()+window.location.pathname),gy(a,f)):(delete d.redirect_uri,hy(a,f),fy(a,{authResult:function(h){h.authResult&&h.authResult.error?c(h.authResult):fy(a,{tokenReady:function(){b(a.currentUser.get())},
tokenFailed:c})}}))};cy.prototype.Yk=function(a){return new _.xk(function(b,c){var d=new rx(a);this.MH=d.Iu()?!0:!1;this.PA?(d.Ka.fetch_basic_profile=!0,sx(d,"email profile openid")):d.Ka.fetch_basic_profile=!1;var e=dy(this,d.Iu());d.J_(e);Fx(this,b,c,d.get())},this)};
cy.prototype.Pu=function(a){var b=a||{};this.MH=!!b.scope;a=dy(this,b.scope);if(a=="")return _.Ck({error:"Missing required parameter: scope"});var c={scope:a,access_type:"offline",include_granted_scopes:!0};_.Bb(Rw,function(d){b[d]!=null&&(c[d]=b[d])});c.hasOwnProperty("prompt")||c.hasOwnProperty("approval_prompt")||(c.prompt="consent");b.redirect_uri=="postmessage"||b.redirect_uri==void 0?a=iy(this,c):(c.redirect_uri=b.redirect_uri,gy(this,{sessionMeta:{extraQueryParams:c},responseType:"code id_token"}),
a=_.Bk({message:"Redirecting to IDP."}));return a};
var iy=function(a,b){b.origin=xx();delete b.redirect_uri;hy(a,{sessionMeta:{extraQueryParams:b},responseType:"code permission id_token"});return new _.xk(function(c,d){fy(this,{authResult:function(e){(e=e&&e.authResult)&&e.code?c({code:e.code}):d(e&&e.error?e:{error:"unknown_error"})}})},a)},hy=function(a,b){wx(b,["sessionMeta","extraQueryParams","gsiwebsdk"],"2");Bw(a.ue,tx(),b)},gy=function(a,b){wx(b,["sessionMeta","extraQueryParams","gsiwebsdk"],"2");Cw(a.ue,b)};
cy.prototype.Zk=function(a){var b=a||!1;return new _.xk(function(c){ow(this.ue,b,function(){c()})},this)};cy.prototype.ET=function(){return this.Ka.authParameters.scope};var dy=function(a,b){a=a.ET();b=kw(b?b.split(" "):[],a?a.split(" "):[]);_.Fh(b);return b.join(" ")};cy.prototype.rK=function(){var a=this;return new _.xk(function(b,c){fy(a,{noSessionBound:c,tokenFailed:c,userLoggedOut:c,tokenReady:function(d){b(d.response)}});a.ue.TS()})};
cy.prototype.BP=function(a,b,c,d){if(a=typeof a==="string"?document.getElementById(a):a){var e=this;_.Dj(a,"click",function(){var f=b;typeof b=="function"&&(f=b());e.Yk(f).then(function(h){c&&c(h)},function(h){d&&d(h)})})}else d&&d({error:"Could not attach click handler to the element. Reason: element not found."})};cy.prototype.disconnect=function(){return new _.xk(function(a){this.ue.revoke(function(){a()})},this)};cy.prototype.attachClickHandler=cy.prototype.BP;var jy;_.xk.prototype["catch"]=_.xk.prototype.zD;jy=null;_.ky=function(a){zx();a=Mx(a);if(Cx){if(_.Vu(a,jy||{}))return ay();throw new Vw("gapi.auth2 has been initialized with different options. Consider calling gapi.auth2.getAuthInstance() instead of gapi.auth2.init().");}Nx(a,a.fetch_basic_profile!==!1);vx();jy=a;Cx=new cy(a);_.Me.ga=1;return ay()};var my,oy,ly,qy,py,ry;
_.ny=function(a,b){zx();vx();a=Mx(a);Nx(a);var c=Ux(a);c.supportBlocked3PCookies=Ax(a.use_fedcm);var d=new _.hw(c);a.prompt=="none"?ly(d,a,function(e){e.status=e.error?{signed_in:!1,method:null,google_logged_in:!1}:{signed_in:!0,method:"AUTO",google_logged_in:!0};b(e)}):my(d,a,function(e){if(e.error)e.status={signed_in:!1,method:null,google_logged_in:!1};else{var f=e.access_token||e.id_token;e.status={signed_in:!!f,method:"PROMPT",google_logged_in:!!f}}e["g-oauth-window"]=d.BY.xi;b(e)})};
my=function(a,b,c){var d=new Rx(b.response_type);c=oy(a,d,c);var e={responseType:d.toString()};wx(e,["sessionMeta","extraQueryParams","gsiwebsdk"],b.gsiwebsdk||"2");Sx(d)&&wx(e,["sessionMeta","extraQueryParams","access_type"],b.access_type||"offline");b.redirect_uri&&wx(e,["sessionMeta","extraQueryParams","redirect_uri"],b.redirect_uri);b.state&&wx(e,["sessionMeta","extraQueryParams","state"],b.state);b=tx();a.Ck?c({authResult:{error:"idpiframe_initialization_failed",details:a.Wn().error}}):(a.tn=
c,Bw(a,b,e))};oy=function(a,b,c){if(Tx(b)){var d=py(c);return function(e){e&&e.authResult&&!e.authResult.error?a.yu(function(f){f&&!f.error?(f=_.fk(f),Sx(b)&&(f.code=e.authResult.code),d(f)):d(f?f:{error:"unknown_error"})})(e):d(e&&e.authResult?e.authResult:{error:"unknown_error"})}}return function(e){e&&e.authResult&&!e.authResult.error?c(_.fk(e.authResult)):c(e&&e.authResult?e.authResult:{error:"unknown_error"})}};
ly=function(a,b,c){if(Sx(new Rx(b.response_type))&&b.access_type=="offline")c({error:"immediate_failed",error_subtype:"access_denied"});else{var d=py(c);b.login_hint?a.pG(b.login_hint,function(e){e?qy(a,b,e,d):c({error:"immediate_failed",error_subtype:"access_denied"})}):b.authuser!==void 0&&b.authuser!==null?bx(a,b.authuser).select(function(e){e&&e.login_hint?qy(a,b,e.login_hint,d):d({error:"immediate_failed",error_subtype:"access_denied"})}):a.ru(function(e){e&&e.hint?qy(a,b,e.hint,d):e&&e.disabled?
d({error:"immediate_failed",error_subtype:"no_user_bound"}):(b.session_selection=="first_valid"?ax(a):$w(a)).select(function(f){f&&f.login_hint?qy(a,b,f.login_hint,d):d({error:"immediate_failed",error_subtype:"no_user_bound"})})})}};qy=function(a,b,c,d){b=new Rx(b.response_type);var e=0,f={},h=function(k){!k||k.error?d(k):(e--,_.ij(f,k),e==0&&d(f))};(Tx(b)||Vx(b,"id_token"))&&e++;Sx(b)&&e++;(Tx(b)||Vx(b,"id_token"))&&_.iw(a,c,h);Sx(b)&&Dw(a,c,h)};
py=function(a){return function(b){if(!b||b.error)_.Iw(),b?a(b):a({error:"unknown_error"});else{if(b.access_token){var c=_.fk(b);ry(c);delete c.id_token;delete c.code;_.Hw(c)}a(b)}}};ry=function(a){_.Bb(Sw,function(b){delete a[b]})};_.t("gapi.auth2.init",_.ky);_.t("gapi.auth2.authorize",function(a,b){if(Cx!=null)throw new Vw("gapi.auth2.authorize cannot be called after GoogleAuth has been initialized (i.e. with a call to gapi.auth2.init, or gapi.client.init when given a 'clientId' and a 'scope' parameters).");_.ny(a,function(c){ry(c);b(c)})});_.t("gapi.auth2._gt",function(){return _.ui()});_.t("gapi.auth2.enableDebugLogs",function(a){a=a!==!1;_.Wu=a!="0"&&!!a});_.t("gapi.auth2.getAuthInstance",_.by);
_.t("gapi.auth2.BasicProfile",Dx);_.t("gapi.auth2.BasicProfile.prototype.getId",Dx.prototype.getId);_.t("gapi.auth2.BasicProfile.prototype.getName",Dx.prototype.getName);_.t("gapi.auth2.BasicProfile.prototype.getGivenName",Dx.prototype.c$);_.t("gapi.auth2.BasicProfile.prototype.getFamilyName",Dx.prototype.Y9);_.t("gapi.auth2.BasicProfile.prototype.getImageUrl",Dx.prototype.k$);_.t("gapi.auth2.BasicProfile.prototype.getEmail",Dx.prototype.Qn);_.t("gapi.auth2.GoogleAuth",cy);
_.t("gapi.auth2.GoogleAuth.prototype.attachClickHandler",cy.prototype.BP);_.t("gapi.auth2.GoogleAuth.prototype.disconnect",cy.prototype.disconnect);_.t("gapi.auth2.GoogleAuth.prototype.grantOfflineAccess",cy.prototype.Pu);_.t("gapi.auth2.GoogleAuth.prototype.signIn",cy.prototype.Yk);_.t("gapi.auth2.GoogleAuth.prototype.signOut",cy.prototype.Zk);_.t("gapi.auth2.GoogleAuth.prototype.getInitialScopes",cy.prototype.ET);_.t("gapi.auth2.GoogleUser",Bx);_.t("gapi.auth2.GoogleUser.prototype.grant",Bx.prototype.Q$);
_.t("gapi.auth2.GoogleUser.prototype.getId",Bx.prototype.getId);_.t("gapi.auth2.GoogleUser.prototype.isSignedIn",Bx.prototype.xg);_.t("gapi.auth2.GoogleUser.prototype.getAuthResponse",Bx.prototype.Tl);_.t("gapi.auth2.GoogleUser.prototype.getBasicProfile",Bx.prototype.G9);_.t("gapi.auth2.GoogleUser.prototype.getGrantedScopes",Bx.prototype.e$);_.t("gapi.auth2.GoogleUser.prototype.getHostedDomain",Bx.prototype.MG);_.t("gapi.auth2.GoogleUser.prototype.grantOfflineAccess",Bx.prototype.Pu);
_.t("gapi.auth2.GoogleUser.prototype.hasGrantedScopes",Bx.prototype.xA);_.t("gapi.auth2.GoogleUser.prototype.reloadAuthResponse",Bx.prototype.rK);_.t("gapi.auth2.LiveValue",ox);_.t("gapi.auth2.LiveValue.prototype.listen",ox.prototype.na);_.t("gapi.auth2.LiveValue.prototype.get",ox.prototype.get);_.t("gapi.auth2.SigninOptionsBuilder",rx);_.t("gapi.auth2.SigninOptionsBuilder.prototype.getAppPackageName",rx.prototype.D9);_.t("gapi.auth2.SigninOptionsBuilder.prototype.setAppPackageName",rx.prototype.Yfa);
_.t("gapi.auth2.SigninOptionsBuilder.prototype.getScope",rx.prototype.Iu);_.t("gapi.auth2.SigninOptionsBuilder.prototype.setScope",rx.prototype.J_);_.t("gapi.auth2.SigninOptionsBuilder.prototype.getPrompt",rx.prototype.A$);_.t("gapi.auth2.SigninOptionsBuilder.prototype.setPrompt",rx.prototype.vga);_.t("gapi.auth2.SigninOptionsBuilder.prototype.get",rx.prototype.get);
_.af=_.af||{};
(function(){function a(b){var c="";if(b.nodeType==3||b.nodeType==4)c=b.nodeValue;else if(b.innerText)c=b.innerText;else if(b.innerHTML)c=b.innerHTML;else if(b.firstChild){c=[];for(b=b.firstChild;b;b=b.nextSibling)c.push(a(b));c=c.join("")}return c}_.af.createElement=function(b){if(!document.body||document.body.namespaceURI)try{var c=document.createElementNS("http://www.w3.org/1999/xhtml",b)}catch(d){}return c||document.createElement(b)};_.af.CQ=function(b){var c=_.af.createElement("iframe");try{var d=
["<","iframe"],e=b||{},f;for(f in e)e.hasOwnProperty(f)&&(d.push(" "),d.push(f),d.push('="'),d.push(_.af.aG(e[f])),d.push('"'));d.push("></");d.push("iframe");d.push(">");var h=_.af.createElement(d.join(""));h&&(!c||h.tagName==c.tagName&&h.namespaceURI==c.namespaceURI)&&(c=h)}catch(l){}d=c;b=b||{};for(var k in b)b.hasOwnProperty(k)&&(d[k]=b[k]);return c};_.af.gT=function(){if(document.body)return document.body;try{var b=document.getElementsByTagNameNS("http://www.w3.org/1999/xhtml","body");if(b&&
b.length==1)return b[0]}catch(c){}return document.documentElement||document};_.af.Vqa=function(b){return a(b)}})();
_.Gg=window.gapi&&window.gapi.util||{};
_.Gg=_.Gg={};_.Gg.getOrigin=function(a){return _.Ig(a)};
_.Ny=function(a){if(a.indexOf("GCSC")!==0)return null;var b={xj:!1};a=a.substr(4);if(!a)return b;var c=a.charAt(0);a=a.substr(1);var d=a.lastIndexOf("_");if(d==-1)return b;var e=_.Ly(a.substr(d+1));if(e==null)return b;a=a.substring(0,d);if(a.charAt(0)!=="_")return b;d=c==="E"&&e.ff;return!d&&(c!=="U"||e.ff)||d&&!_.My?b:{xj:!0,ff:d,S7:a.substr(1),domain:e.domain,Ti:e.Ti}};_.Oy=function(a,b){this.dh=a;a=b||{};this.Wca=Number(a.maxAge)||0;this.Xd=a.domain;this.Mm=a.path;this.Efa=!!a.secure};_.Oy.prototype.read=function(){for(var a=this.dh+"=",b=document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c];if(d.indexOf(a)==0)return d.substr(a.length)}};
_.Oy.prototype.write=function(a,b){if(!Py.test(this.dh))throw"Invalid cookie name";if(!Qy.test(a))throw"Invalid cookie value";a=this.dh+"="+a;this.Xd&&(a+=";domain="+this.Xd);this.Mm&&(a+=";path="+this.Mm);b=typeof b==="number"?b:this.Wca;if(b>=0){var c=new Date;c.setSeconds(c.getSeconds()+b);a+=";expires="+c.toUTCString()}this.Efa&&(a+=";secure");document.cookie=a;return!0};_.Oy.prototype.clear=function(){this.write("",0)};var Qy=/^[-+/_=.:|%&a-zA-Z0-9@]*$/,Py=/^[A-Z_][A-Z0-9_]{0,63}$/;
_.Oy.iterate=function(a){for(var b=document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c].split("="),e=d.shift();a(e,d.join("="))}};_.Ry=function(a){this.Nf=a};_.Ry.prototype.read=function(){if(Sy.hasOwnProperty(this.Nf))return Sy[this.Nf]};_.Ry.prototype.write=function(a){Sy[this.Nf]=a;return!0};_.Ry.prototype.clear=function(){delete Sy[this.Nf]};var Sy={};_.Ry.iterate=function(a){for(var b in Sy)Sy.hasOwnProperty(b)&&a(b,Sy[b])};var Ty=function(){this.La=null;this.key=function(){return null};this.getItem=function(){return this.La};this.setItem=function(a,b){this.La=b;this.length=1};this.removeItem=function(){this.clear()};this.clear=function(){this.La=null;this.length=0};this.length=0},Uy=function(a){try{var b=a||window.sessionStorage;if(!b)return!1;b.setItem("gapi.sessionStorageTest","gapi.sessionStorageTest"+b.length);b.removeItem("gapi.sessionStorageTest");return!0}catch(c){return!1}},Vy=function(a,b){this.dh=a;this.rN=
Uy(b)?b||window.sessionStorage:new Ty};Vy.prototype.read=function(){return this.rN.getItem(this.dh)};Vy.prototype.write=function(a){try{this.rN.setItem(this.dh,a)}catch(b){return!1}return!0};Vy.prototype.clear=function(){this.rN.removeItem(this.dh)};Vy.iterate=function(a){if(Uy())for(var b=window.sessionStorage.length,c=0;c<b;++c){var d=window.sessionStorage.key(c);a(d,window.sessionStorage[d])}};_.My=window.location.protocol==="https:";_.Wy=_.My||window.location.protocol==="http:"?_.Oy:_.Ry;_.Ly=function(a){var b=a.substr(1),c="",d=window.location.hostname;if(b!==""){c=parseInt(b,10);if(isNaN(c))return null;b=d.split(".");if(b.length<c-1)return null;b.length==c-1&&(d="."+d)}else d="";return{ff:a.charAt(0)=="S",domain:d,Ti:c}};var Xy,Yy,az,bz;Xy=_.Ce();Yy=_.Ce();_.Zy=_.Ce();_.$y=_.Ce();az="state code cookie_policy g_user_cookie_policy authuser prompt g-oauth-window status".split(" ");bz=function(a){this.vY=a;this.aJ=null};
bz.prototype.write=function(a){var b=_.Ce(),c=_.Ce(),d=window.decodeURIComponent?decodeURIComponent:unescape,e;for(e in a)if(_.De(a,e)){var f=a[e];f=f.replace(/\+/g," ");c[e]=d(f);b[e]=a[e]}d=az.length;for(e=0;e<d;++e)delete c[az[e]];a=String(a.authuser||0);d=_.Ce();d[a]=c;c=_.Rf(d);this.vY.write(c);this.aJ=b};bz.prototype.read=function(){return this.aJ};bz.prototype.clear=function(){this.vY.clear();this.aJ=_.Ce()};_.cz=function(a){return a?{domain:a.domain,path:"/",secure:a.ff}:null};
Vy.iterate(function(a){var b=_.Ny(a);b&&b.xj&&(Xy[a]=new bz(new Vy(a)))});_.Wy.iterate(function(a){Xy[a]&&(Yy[a]=new _.Wy(a,_.cz(_.Ny(a))))});
_.mi=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var q=h,r=0;r<64;r+=4)q[r/4]=p[r]<<24|p[r+1]<<16|p[r+2]<<8|p[r+3];for(r=16;r<80;r++)p=q[r-3]^q[r-8]^q[r-14]^q[r-16],q[r]=(p<<1|p>>>31)&4294967295;p=e[0];var w=e[1],u=e[2],x=e[3],A=e[4];for(r=0;r<80;r++){if(r<40)if(r<20){var D=x^w&(u^x);var E=1518500249}else D=w^u^x,E=1859775393;else r<60?(D=w&u|x&(w|u),E=2400959708):(D=w^u^x,E=3395469782);D=((p<<5|p>>>27)&4294967295)+
D+A+E+q[r]&4294967295;A=x;x=u;u=(w<<30|w>>>2)&4294967295;w=p;p=D}e[0]=e[0]+p&4294967295;e[1]=e[1]+w&4294967295;e[2]=e[2]+u&4294967295;e[3]=e[3]+x&4294967295;e[4]=e[4]+A&4294967295}function c(p,q){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var r=[],w=0,u=p.length;w<u;++w)r.push(p.charCodeAt(w));p=r}q||(q=p.length);r=0;if(m==0)for(;r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64;for(;r<q;)if(f[m++]=p[r++],n++,m==64)for(m=0,b(f);r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64}function d(){var p=[],
q=n*8;m<56?c(k,56-m):c(k,64-(m-56));for(var r=63;r>=56;r--)f[r]=q&255,q>>>=8;b(f);for(r=q=0;r<5;r++)for(var w=24;w>=0;w-=8)p[q++]=e[r]>>w&255;return p}for(var e=[],f=[],h=[],k=[128],l=1;l<64;++l)k[l]=0;var m,n;a();return{reset:a,update:c,digest:d,Si:function(){for(var p=d(),q="",r=0;r<p.length;r++)q+="0123456789ABCDEF".charAt(Math.floor(p[r]/16))+"0123456789ABCDEF".charAt(p[r]%16);return q}}};var oi=function(a,b,c){var d=String(_.Xa.location.href);return d&&a&&b?[b,ni(_.Ig(d),a,c||null)].join(" "):null},ni=function(a,b,c){var d=[],e=[];if((Array.isArray(c)?2:1)==1)return e=[b,a],_.Bb(d,function(k){e.push(k)}),pi(e.join(" "));var f=[],h=[];_.Bb(c,function(k){h.push(k.key);f.push(k.value)});c=Math.floor((new Date).getTime()/1E3);e=f.length==0?[c,b,a]:[f.join(":"),c,b,a];_.Bb(d,function(k){e.push(k)});a=pi(e.join(" "));a=[c,a];h.length==0||a.push(h.join(""));return a.join("_")},pi=function(a){var b=
_.mi();b.update(a);return b.Si().toLowerCase()};var ri;_.qi=function(){var a=_.Xa.__SAPISID||_.Xa.__APISID||_.Xa.__3PSAPISID||_.Xa.__1PSAPISID||_.Xa.__OVERRIDE_SID;if(a)return!0;typeof document!=="undefined"&&(a=new _.ji(document),a=a.get("SAPISID")||a.get("APISID")||a.get("__Secure-3PAPISID")||a.get("__Secure-1PAPISID"));return!!a};ri=function(a,b,c,d){(a=_.Xa[a])||typeof document==="undefined"||(a=(new _.ji(document)).get(b));return a?oi(a,c,d):null};
_.si=function(a){var b=_.Ig(String(_.Xa.location.href)),c=[];if(_.qi()){b=b.indexOf("https:")==0||b.indexOf("chrome-extension:")==0||b.indexOf("chrome-untrusted://new-tab-page")==0||b.indexOf("moz-extension:")==0;var d=b?_.Xa.__SAPISID:_.Xa.__APISID;d||typeof document==="undefined"||(d=new _.ji(document),d=d.get(b?"SAPISID":"APISID")||d.get("__Secure-3PAPISID"));(d=d?oi(d,b?"SAPISIDHASH":"APISIDHASH",a):null)&&c.push(d);b&&((b=ri("__1PSAPISID","__Secure-1PAPISID","SAPISID1PHASH",a))&&c.push(b),(a=
ri("__3PSAPISID","__Secure-3PAPISID","SAPISID3PHASH",a))&&c.push(a))}return c.length==0?null:c.join(" ")};
var ts,us;_.ls=function(a){if(a instanceof _.gc)return a.PY;throw Error("j");};_.ms=function(a,b,c,d){this.top=a;this.right=b;this.bottom=c;this.left=d};_.ns=function(a,b){return a==b?!0:a&&b?a.x==b.x&&a.y==b.y:!1};_.os=function(a,b){this.x=a!==void 0?a:0;this.y=b!==void 0?b:0};_.g=_.os.prototype;_.g.clone=function(){return new _.os(this.x,this.y)};_.g.equals=function(a){return a instanceof _.os&&_.ns(this,a)};_.g.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};
_.g.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};_.g.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};_.g.translate=function(a,b){a instanceof _.os?(this.x+=a.x,this.y+=a.y):(this.x+=Number(a),typeof b==="number"&&(this.y+=b));return this};_.g.scale=function(a,b){this.x*=a;this.y*=typeof b==="number"?b:a;return this};_.ps=function(a){return a.scrollingElement?a.scrollingElement:!_.Cd&&_.he(a)?a.documentElement:a.body||a.documentElement};
_.qs=function(a){var b=_.ps(a);a=a.defaultView;return new _.os(a.pageXOffset||b.scrollLeft,a.pageYOffset||b.scrollTop)};_.rs=function(a,b,c,d){return _.ce(a.Bc,b,c,d)};_.ss=function(a){return _.qs(a.Bc)};ts=function(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};us=function(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};_.vs=function(a){return _.be(document,a)};_.g=_.ms.prototype;_.g.Qb=function(){return this.right-this.left};_.g.Nc=function(){return this.bottom-this.top};_.g.clone=function(){return new _.ms(this.top,this.right,this.bottom,this.left)};_.g.contains=function(a){return this&&a?a instanceof _.ms?a.left>=this.left&&a.right<=this.right&&a.top>=this.top&&a.bottom<=this.bottom:a.x>=this.left&&a.x<=this.right&&a.y>=this.top&&a.y<=this.bottom:!1};
_.g.expand=function(a,b,c,d){_.vb(a)?(this.top-=a.top,this.right+=a.right,this.bottom+=a.bottom,this.left-=a.left):(this.top-=a,this.right+=Number(b),this.bottom+=Number(c),this.left-=Number(d));return this};_.g.ceil=function(){this.top=Math.ceil(this.top);this.right=Math.ceil(this.right);this.bottom=Math.ceil(this.bottom);this.left=Math.ceil(this.left);return this};
_.g.floor=function(){this.top=Math.floor(this.top);this.right=Math.floor(this.right);this.bottom=Math.floor(this.bottom);this.left=Math.floor(this.left);return this};_.g.round=function(){this.top=Math.round(this.top);this.right=Math.round(this.right);this.bottom=Math.round(this.bottom);this.left=Math.round(this.left);return this};
_.g.translate=function(a,b){a instanceof _.os?(this.left+=a.x,this.right+=a.x,this.top+=a.y,this.bottom+=a.y):(this.left+=a,this.right+=a,typeof b==="number"&&(this.top+=b,this.bottom+=b));return this};_.g.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.right*=a;this.top*=b;this.bottom*=b;return this};var ys,ws,Cs,Es;_.xs=function(a,b,c){if(typeof b==="string")(b=ws(a,b))&&(a.style[b]=c);else for(var d in b){c=a;var e=b[d],f=ws(c,d);f&&(c.style[f]=e)}};ys={};ws=function(a,b){var c=ys[b];if(!c){var d=ts(b);c=d;a.style[d]===void 0&&(d=(_.Cd?"Webkit":_.Bd?"Moz":null)+us(d),a.style[d]!==void 0&&(c=d));ys[b]=c}return c};_.zs=function(a,b){var c=a.style[ts(b)];return typeof c!=="undefined"?c:a.style[ws(a,b)]||""};
_.As=function(a,b){var c=_.$d(a);return c.defaultView&&c.defaultView.getComputedStyle&&(a=c.defaultView.getComputedStyle(a,null))?a[b]||a.getPropertyValue(b)||"":""};_.Bs=function(a,b){return _.As(a,b)||(a.currentStyle?a.currentStyle[b]:null)||a.style&&a.style[b]};Cs=function(a){try{return a.getBoundingClientRect()}catch(b){return{left:0,top:0,right:0,bottom:0}}};
_.Fs=function(a,b){b=b||_.ps(document);var c=b||_.ps(document);var d=_.Ds(a),e=_.Ds(c),f=_.As(c,"borderLeftWidth");var h=_.As(c,"borderRightWidth");var k=_.As(c,"borderTopWidth"),l=_.As(c,"borderBottomWidth");h=new _.ms(parseFloat(k),parseFloat(h),parseFloat(l),parseFloat(f));c==_.ps(document)?(f=d.x-c.scrollLeft,d=d.y-c.scrollTop):(f=d.x-e.x-h.left,d=d.y-e.y-h.top);a=Es(a);e=c.clientHeight-a.height;h=c.scrollLeft;k=c.scrollTop;h+=Math.min(f,Math.max(f-(c.clientWidth-a.width),0));k+=Math.min(d,Math.max(d-
e,0));c=new _.os(h,k);b.scrollLeft=c.x;b.scrollTop=c.y};_.Ds=function(a){var b=_.$d(a),c=new _.os(0,0);if(a==(b?_.$d(b):document).documentElement)return c;a=Cs(a);b=_.ss(_.ae(b));c.x=a.left+b.x;c.y=a.top+b.y;return c};_.Hs=function(a,b){var c=new _.os(0,0),d=_.ie(_.$d(a));a:{try{_.Wb(d.parent);var e=!0;break a}catch(f){}e=!1}if(!e)return c;do e=d==b?_.Ds(a):_.Gs(a),c.x+=e.x,c.y+=e.y;while(d&&d!=b&&d!=d.parent&&(a=d.frameElement)&&(d=d.parent));return c};
_.Gs=function(a){a=Cs(a);return new _.os(a.left,a.top)};_.Js=function(a,b,c){if(b instanceof _.rd)c=b.height,b=b.width;else if(c==void 0)throw Error("J");a.style.width=_.Is(b,!0);a.style.height=_.Is(c,!0)};_.Is=function(a,b){typeof a=="number"&&(a=(b?Math.round(a):a)+"px");return a};
_.Ks=function(a){var b=Es;if(_.Bs(a,"display")!="none")return b(a);var c=a.style,d=c.display,e=c.visibility,f=c.position;c.visibility="hidden";c.position="absolute";c.display="inline";a=b(a);c.display=d;c.position=f;c.visibility=e;return a};Es=function(a){var b=a.offsetWidth,c=a.offsetHeight,d=_.Cd&&!b&&!c;return(b===void 0||d)&&a.getBoundingClientRect?(a=Cs(a),new _.rd(a.right-a.left,a.bottom-a.top)):new _.rd(b,c)};_.Ls=function(a,b){a.style.display=b?"":"none"};
_.Ns=function(a){var b=_.ae(void 0),c=_.rs(b,"HEAD")[0];if(!c){var d=_.rs(b,"BODY")[0];c=b.wa("HEAD");d.parentNode.insertBefore(c,d)}d=b.wa("STYLE");var e;(e=_.Gc("style",document))&&d.setAttribute("nonce",e);_.Ms(d,a);b.appendChild(c,d)};_.Ms=function(a,b){b=_.ls(b);_.Xa.trustedTypes?_.ve(a,b):a.innerHTML=b};_.Os=_.Bd?"MozUserSelect":_.Cd||_.zd?"WebkitUserSelect":null;
_.dz=function(a){_.dj.call(this);this.Nf=1;this.UB=[];this.ZB=0;this.Tf=[];this.Rj={};this.h7=!!a};_.eb(_.dz,_.dj);_.g=_.dz.prototype;_.g.subscribe=function(a,b,c){var d=this.Rj[a];d||(d=this.Rj[a]=[]);var e=this.Nf;this.Tf[e]=a;this.Tf[e+1]=b;this.Tf[e+2]=c;this.Nf=e+3;d.push(e);return e};_.g.Sw=_.jb(18);_.g.unsubscribe=function(a,b,c){if(a=this.Rj[a]){var d=this.Tf;if(a=a.find(function(e){return d[e+1]==b&&d[e+2]==c}))return this.ol(a)}return!1};
_.g.ol=function(a){var b=this.Tf[a];if(b){var c=this.Rj[b];this.ZB!=0?(this.UB.push(a),this.Tf[a+1]=function(){}):(c&&_.gj(c,a),delete this.Tf[a],delete this.Tf[a+1],delete this.Tf[a+2])}return!!b};
_.g.bp=function(a,b){var c=this.Rj[a];if(c){var d=Array(arguments.length-1),e=arguments.length,f;for(f=1;f<e;f++)d[f-1]=arguments[f];if(this.h7)for(f=0;f<c.length;f++)e=c[f],ez(this.Tf[e+1],this.Tf[e+2],d);else{this.ZB++;try{for(f=0,e=c.length;f<e&&!this.isDisposed();f++){var h=c[f];this.Tf[h+1].apply(this.Tf[h+2],d)}}finally{if(this.ZB--,this.UB.length>0&&this.ZB==0)for(;c=this.UB.pop();)this.ol(c)}}return f!=0}return!1};var ez=function(a,b,c){_.vk(function(){a.apply(b,c)})};
_.dz.prototype.clear=function(a){if(a){var b=this.Rj[a];b&&(b.forEach(this.ol,this),delete this.Rj[a])}else this.Tf.length=0,this.Rj={}};_.dz.prototype.Zb=function(a){if(a){var b=this.Rj[a];return b?b.length:0}a=0;for(b in this.Rj)a+=this.Zb(b);return a};_.dz.prototype.ua=function(){_.dz.N.ua.call(this);this.clear();this.UB.length=0};
_.fz=function(a){this.pha=a};_.gz=function(a){_.dj.call(this);this.ke=new _.dz(a);_.fj(this,this.ke)};_.fz.prototype.toString=function(){return this.pha};_.eb(_.gz,_.dj);_.g=_.gz.prototype;_.g.subscribe=function(a,b,c){return this.ke.subscribe(a.toString(),b,c)};_.g.Sw=_.jb(17);_.g.unsubscribe=function(a,b,c){return this.ke.unsubscribe(a.toString(),b,c)};_.g.ol=function(a){return this.ke.ol(a)};_.g.bp=function(a,b){return this.ke.bp(a.toString(),b)};_.g.clear=function(a){this.ke.clear(a!==void 0?a.toString():void 0)};_.g.Zb=function(a){return this.ke.Zb(a!==void 0?a.toString():void 0)};
var hz,iz,lz,jz,mz,nz,kz;hz=function(a){var b=_.tc("");return _.ec(a.map(function(c){return _.fc(_.tc(c))}).join(_.fc(b).toString()))};iz=function(a){return hz(a)};lz=function(a){for(var b="",c=Object.keys(a),d=0;d<c.length;d++){var e=c[d],f=a[e];if(!jz.test(e))throw Error("j");if(f!==void 0&&f!==null){if(/^on./i.test(e))throw Error("j");kz.indexOf(e.toLowerCase())!==-1&&(f=_.mc(f)?f.toString():_.rc(String(f))||"about:invalid#zClosurez");f=e+'="'+_.tc(String(f))+'"';b+=" "+f}}return b};
_.oz=function(a,b){if(!jz.test("div"))throw Error("j");if(mz.indexOf("DIV")!==-1)throw Error("j");var c="<div";a&&(c+=lz(a));Array.isArray(b)||(b=b===void 0?[]:[b]);nz.indexOf("DIV")!==-1?c+=">":(a=iz(b.map(function(d){return d instanceof _.dc?d:_.tc(String(d))})),c+=">"+a.toString()+"</div>");return _.ec(c)};jz=/^[a-z][a-z\d-]*$/i;mz="APPLET BASE EMBED IFRAME LINK MATH META OBJECT SCRIPT STYLE SVG TEMPLATE".split(" ");nz="AREA BR COL COMMAND HR IMG INPUT KEYGEN PARAM SOURCE TRACK WBR".split(" ");
kz=["action","formaction","href"];_.pz=function(a,b){Array.isArray(b)||(b=[b]);b=b.map(function(c){return typeof c==="string"?c:c.Zo+" "+c.duration+"s "+c.timing+" "+c.delay+"s"});_.xs(a,"transition",b.join(","))};_.qz=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){var a=_.me("DIV"),b=_.Cd?"-webkit":_.Bd?"-moz":null,c="transition:opacity 1s linear;";b&&(c+=b+"-transition:opacity 1s linear;");_.Hc(a,_.oz({style:c}));return _.zs(a.firstChild,"transition")!=""});
_.rz=function(a,b){_.Oj.call(this);this.Cm=a||1;this.gx=b||_.Xa;this.OP=(0,_.z)(this.mha,this);this.xW=_.ld()};_.eb(_.rz,_.Oj);_.g=_.rz.prototype;_.g.enabled=!1;_.g.Hc=null;_.g.setInterval=function(a){this.Cm=a;this.Hc&&this.enabled?(this.stop(),this.start()):this.Hc&&this.stop()};
_.g.mha=function(){if(this.enabled){var a=_.ld()-this.xW;a>0&&a<this.Cm*.8?this.Hc=this.gx.setTimeout(this.OP,this.Cm-a):(this.Hc&&(this.gx.clearTimeout(this.Hc),this.Hc=null),this.dispatchEvent("tick"),this.enabled&&(this.stop(),this.start()))}};_.g.start=function(){this.enabled=!0;this.Hc||(this.Hc=this.gx.setTimeout(this.OP,this.Cm),this.xW=_.ld())};_.g.stop=function(){this.enabled=!1;this.Hc&&(this.gx.clearTimeout(this.Hc),this.Hc=null)};_.g.ua=function(){_.rz.N.ua.call(this);this.stop();delete this.gx};
_.sz=function(a,b,c){if(typeof a==="function")c&&(a=(0,_.z)(a,c));else if(a&&typeof a.handleEvent=="function")a=(0,_.z)(a.handleEvent,a);else throw Error("wa");return Number(b)>2147483647?-1:_.Xa.setTimeout(a,b||0)};_.tz=function(a){_.Xa.clearTimeout(a)};
_.vz=function(){_.uz="oauth2relay"+String(2147483647*(0,_.Qg)()|0)};_.wz=new _.gz;_.xz=new _.fz("oauth");_.vz();_.Xe("oauth-flow/client_id");var yz=String(_.Xe("oauth-flow/redirectUri"));if(yz)yz.replace(/[#][\s\S]*/,"");else{var zz=_.Gg.getOrigin(window.location.href);_.Xe("oauth-flow/callbackUrl");encodeURIComponent(zz)}_.Gg.getOrigin(window.location.href);
var Bz,Cz,Dz,Ez,Fz,Gz,Hz,Iz,Jz,Kz,Lz,Nz,Oz,Pz,Qz,Rz,Sz,Yz,Zz,$z,aA,bA,cA,dA,eA,fA,gA,hA,iA,jA,kA,lA,mA,nA,oA,pA,qA,rA,sA,tA,uA,xA,wA,yA,zA,AA,BA,CA,DA,EA;_.Az=function(a,b){if(_.Lh&&!b)return _.Xa.atob(a);var c="";_.Oh(a,function(d){c+=String.fromCharCode(d)});return c};Bz=function(a){var b=String(a("immediate")||"");a=String(a("prompt")||"");return b==="true"||a==="none"};Cz=function(a){return _.ei("enableMultilogin")&&a("cookie_policy")&&!Bz(a)?!0:!1};
Fz=function(){var a,b=null;_.Wy.iterate(function(c,d){c.indexOf("G_AUTHUSER_")===0&&(c=c.substring(11),c=_.Ly(c),!a||c.ff&&!a.ff||c.ff==a.ff&&c.Ti>a.Ti)&&(a=c,b=d)});return{t7:a,authuser:b}};Gz=[".APPS.GOOGLEUSERCONTENT.COM","@DEVELOPER.GSERVICEACCOUNT.COM"];Hz=function(a){a=a.toUpperCase();for(var b=Gz.length,c=0;c<b;++c){var d=a.split(Gz[c]);d.length==2&&d[1]===""&&(a=d[0])}a=a.replace(/-/g,"_").toUpperCase();a.length>40&&(b=new _.Pg,b.ux(a),a=b.Si().toUpperCase());return a};
Iz=function(a){if(!a)return[];a=a.split("=");return a[1]?a[1].split("|"):[]};Jz=function(a){a=a.split(":");return{clientId:a[0].split("=")[1],Wfa:Iz(a[1]),Lra:Iz(a[2]),Eqa:Iz(a[3])}};Kz=function(a){var b=Fz(),c=b.t7;b=b.authuser;var d=a&&Hz(a);if(b!==null){var e;_.Wy.iterate(function(h,k){(h=_.Ny(h))&&h.xj&&(d&&h.S7!=d||h.ff==c.ff&&h.Ti==c.Ti&&(e=k))});if(e){var f=Jz(e);a=f&&f.Wfa[Number(b)];f=f&&f.clientId;if(a)return{authuser:b,Jsa:a,clientId:f}}}return null};
Lz=function(a,b){a=_.ui(a);if(!a||!b&&a.error)return null;b=Math.floor((new Date).getTime()/1E3);return a.expires_at&&b>a.expires_at?null:a};_.Mz=function(a,b){if(b){var c=b;var d=a}else typeof a==="string"?d=a:c=a;c?_.Hw(c,d):_.Iw(d)};
Nz=function(a){if(!a)return null;a!=="single_host_origin"&&(a=_.Ig(a));var b=window.location.hostname,c=b,d=_.My;if(a!=="single_host_origin"){c=a.split("://");if(c.length==2)d=c.shift()==="https";else return _.Vf.log("WARNING invalid cookie_policy: "+a),null;c=c[0]}if(c.indexOf(":")!==-1)c=b="";else{a="."+c;if(b.lastIndexOf(a)!==b.length-a.length)return _.Vf.log("Invalid cookie_policy domain: "+c),null;c=a;b=c.split(".").length-1}return{domain:c,ff:d,Ti:b}};
Oz=function(a){var b=Nz(a);if(!b)return new _.Ry("G_USERSTATE_");a=["G_USERSTATE_",_.My&&b.ff?"S":"H",b.Ti].join("");var c=_.$y[a];c||(c={YI:63072E3},_.Ee(_.cz(b),c),c=new _.Oy(a,c),_.$y[a]=c,b=c.read(),typeof b!=="undefined"&&b!==null&&(document.cookie=a+"=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/",c.write(b)));return c};Pz=function(a){var b=Oz(a).read();a=_.Ce();if(b){b=b.split(":");for(var c;c=b.shift();)c=c.split("="),a[c[0]]=c[1]}return a};
Qz=function(a,b,c){var d=Pz(b),e=d[a];d[a]="0";var f=[];_.Qm(d,function(k,l){f.push(l+"="+k)});var h=f.join(":");b=Oz(b);h?b.write(h):b.clear();d[a]!==e&&c&&c()};Rz=function(a,b){b=Pz(b);return b[a]=="0"||b[a]=="X"};Sz=function(a){a=Nz(a.g_user_cookie_policy);if(!a||a.ff&&!_.My)a=null;else{var b=["G_AUTHUSER_",_.My&&a.ff?"S":"H",a.Ti].join(""),c=_.Zy[b];c||(c=new _.Wy(b,_.cz(a)),_.Zy[b]=c);a=c}_.Ye("googleapis.config/sessionIndex",null);a.clear()};Yz=function(a){return Bz(function(b){return a[b]})};
Zz=0;$z=!1;aA=[];bA={};cA={};dA=null;eA=function(a){var b=_.uz;return function(c){if(this.f==b&&this.t==_.$f.Pn(this.f)&&this.origin==_.$f.co(this.f))return a.apply(this,arguments)}};fA=function(a){if(a&&!decodeURIComponent(a).startsWith("m;/_/scs/"))throw Error("ya");};gA=function(a){var b=_.af.Rg,c=b(a).jsh;if(c!=null)return fA(c),a;if(b=String(b().jsh||_.Me.h||""))fA(b),c=(a+"#").indexOf("#"),a=a.substr(0,c)+(a.substr(0,c).indexOf("?")!==-1?"&":"?")+"jsh="+encodeURIComponent(b)+a.substr(c);return a};
hA=function(){return!!_.Xe("oauth-flow/usegapi")};iA=function(a,b){hA()?dA.unregister(a):_.$f.unregister(a+":"+b)};jA=function(a,b,c){hA()?dA.register(a,c,_.dn):_.$f.register(a+":"+b,eA(c))};kA=function(){Dz.parentNode.removeChild(Dz)};
lA=function(a){var b=Dz;_.pz(b,[{Zo:"-webkit-transform",duration:1,timing:"ease",delay:0}]);_.pz(b,[{Zo:"transform",duration:1,timing:"ease",delay:0}]);_.sz(function(){b.style.webkitTransform="translate3d(0px,"+a+"px,0px)";b.style.transform="translate3d(0px,"+a+"px,0px)"},0)};mA=function(){var a=Ez+88;lA(a);Ez=a};nA=function(){var a=Ez-88;lA(a);Ez=a};
oA=function(a){var b=a?mA:nA,c=a?nA:mA;a=a?"-":"";Ez=parseInt(a+88,10);Dz.style.webkitTransform="translate3d(0px,"+a+88+"px,0px)";Dz.style.transform="translate3d(0px,"+a+88+"px,0px)";Dz.style.display="";Dz.style.visibility="visible";b();_.sz(c,4E3);_.sz(kA,5E3)};
pA=function(a){var b=_.Xe("oauth-flow/toast/position");b!=="top"&&(b="bottom");var c=document.createElement("div");Dz=c;c.style.cssText="position:fixed;left:0px;z-index:1000;width:100%;";_.xs(c,"visibility","hidden");_.xs(c,b,"-40px");_.xs(c,"height","128px");var d=c;if(_.Kr()){d=document.createElement("div");d.style.cssText="float:left;position:relative;left:50%;";c.appendChild(d);var e=document.createElement("div");e.style.cssText="float:left;position:relative;left:-50%";d.appendChild(e);d=e}e=
b=="top"?"-":"";Ez=parseInt(e+88,10);Dz.style.webkitTransform="translate3d(0px,"+e+88+"px,0px)";Dz.style.transform="translate3d(0px,"+e+88+"px,0px)";e=window;try{for(;e.parent!=e&&e.parent.document;)e=e.parent}catch(f){}e=e.document.body;try{e.insertBefore(c,e.firstChild)}catch(f){}_.an.openChild({url:":socialhost:/:session_prefix:_/widget/oauthflow/toast",queryParams:{clientId:a.client_id,idToken:a.id_token},where:d,onRestyle:function(){b==="top"?oA(!0):oA(!1)}})};
qA=function(a){var b=_.Po(),c=b&&b.scope;b=a&&a.scope;b=typeof b==="string"?b.split(" "):b||[];if(c){c=c.split(" ");for(var d=0;d<c.length;++d){var e=c[d];_.Om.call(b,e)==-1&&b.push(e)}b.length>0&&(a.scope=b.join(" "))}return a};
rA=function(a,b){var c=null;a&&b&&(c=b.client_id=b.client_id||a.client_id,b.scope=b.scope||a.scope,b.g_user_cookie_policy=a.cookie_policy,b.cookie_policy=b.cookie_policy||a.cookie_policy,b.response_type=b.response_type||a.response_type);if(b){b.issued_at||(b.issued_at=String(Math.floor((new Date).getTime()/1E3)));var d=parseInt(b.expires_in,10)||86400;b.error&&(d=_.Xe("oauth-flow/errorMaxAge")||86400);b.expires_in=String(d);b.expires_at||(b.expires_at=String(Math.floor((new Date).getTime()/1E3)+d));
b._aa||b.error||Kz(c)!=null||!Yz(a)||(b._aa="1");a=b.status={};a.google_logged_in=!!b.session_state;c=a.signed_in=!!b.access_token;a.method=c?b["g-oauth-window"]?"PROMPT":"AUTO":null}return b};sA=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;a=(a.split(".")[1]+"...").replace(/^((....)+)\.?\.?\.?$/,"$1");a=_.Qf(_.Az(a,!0));if(a===!1)throw Error("za");return a};tA=function(a){return(a=sA(a))?a.sub:null};
uA=function(a){a&&aA.push(a);a=_.uz;var b=document.getElementById(a),c=(new Date).getTime();if(b){if(Zz&&c-Zz<6E4)return;var d=_.$f.Pn(a);d&&(iA("oauth2relayReady",d),iA("oauth2callback",d));b.parentNode.removeChild(b);if(/Firefox/.test(navigator.userAgent))try{window.frames[a]=void 0}catch(f){}_.vz();a=_.uz}Zz=c;var e=String(2147483647*(0,_.Qg)()|0);b=_.Xe("oauth-flow/proxyUrl")||_.Xe("oauth-flow/relayUrl");hA()?dA=_.an.openChild({where:_.af.gT(),url:b,id:a,attributes:{style:{width:"1px",height:"1px",
position:"absolute",top:"-100px",display:"none"},"aria-hidden":"true"},dontclear:!0}):(b=[b,"?parent=",encodeURIComponent(_.Gg.getOrigin(window.location.href)),"#rpctoken=",e,"&forcesecure=1"].join(""),c=_.af.gT(),d=_.af.CQ({name:a,id:a}),d.src=gA(b),d.style.width="1px",d.style.height="1px",d.style.position="absolute",d.style.top="-100px",d.tabIndex=-1,d.setAttribute("aria-hidden","true"),c.appendChild(d),_.$f.Iw(a));jA("oauth2relayReady",e,function(){iA("oauth2relayReady",e);var f=aA;if(f!==null){aA=
null;for(var h=f.length,k=0;k<h;++k)f[k]()}});jA("oauth2callback",e,function(f){var h=_.af.Rg;h=h(f);var k=h.state;f=k;f=f.replace(/\|.*$/,"");f={}.hasOwnProperty.call(cA,f)?cA[f]:null;h.state=f;if(h.state!=null){f=bA[k];delete bA[k];k=f&&f.key||"token";var l=h=rA(f&&f.params,h);var m=(m=tA(l))?Rz(m,l.cookie_policy):!1;!m&&l&&(" "+(l.scope||"")+" ").indexOf(" https://www.googleapis.com/auth/plus.login ")>=0&&_.Xe("isLoggedIn")&&(l&&l._aa)==="1"&&(l._aa="0",$z||($z=!0,pA(l)));_.Mz(k,h);h=Lz(k);if(f){k=
f.popup;l=f.after_redirect;if(k&&"keep_open"!=l)try{k.close()}catch(n){}f.callback&&(f.callback(h),f.callback=null)}}})};_.vA=function(a){aA!==null?uA(a):a&&a()};xA=function(a,b){var c=wA,d=tA(a);d&&(Sz(a),Qz(d,b,function(){if(c){var e={error:"user_signed_out"};e.client_id=a.client_id;e.g_user_cookie_policy=a.g_user_cookie_policy;e.scope=a.scope;e.response_type=a.response_type;e.session_state=a.session_state;e=rA(null,e);c(e)}}))};
wA=function(a){a||(a=Lz(void 0,!0));a&&typeof a==="object"||(a={error:"invalid_request",error_description:"no callback data"});var b=a.error_description;b&&window.console&&(window.console.error(a.error),window.console.error(b));a.error||(_.Me.drw=null);_.Mz(a);if(b=a.authuser)_.Xe("googleapis.config/sessionIndex"),_.Ye("googleapis.config/sessionIndex",b);_.wz.bp(_.xz,a);return a};yA=["client_id","cookie_policy","response_type"];zA="client_id response_type login_hint authuser prompt include_granted_scopes after_redirect access_type hl state".split(" ");
AA=function(a){var b=_.fk(a);b.session_state&&b.session_state.extraQueryParams&&(b.authuser=b.session_state.extraQueryParams.authuser);b.session_state=null;a.expires_at&&(b.expires_at=parseInt(a.expires_at/1E3).toString());a.expires_in&&(b.expires_in=a.expires_in.toString());a.first_issued_at&&(b.issued_at=parseInt(a.first_issued_at/1E3).toString(),delete b.first_issued_at);_.Hw(b);return b};
BA=function(a){if(a.include_granted_scopes===void 0){var b=_.Xe("include_granted_scopes");a.include_granted_scopes=!!b}};CA=function(a){window.console&&(typeof window.console.warn==="function"?window.console.warn(a):typeof window.console.log==="function"&&window.console.log(a))};
DA=function(a){var b=a||{},c={};_.Bb(zA,function(d){b[d]!=null&&(c[d]=b[d])});a=_.Xe("googleapis/overrideClientId");a!=null&&(c.client_id=a);BA(c);typeof b.scope==="string"?c.scope=b.scope:Array.isArray(b.scope)&&(c.scope=b.scope.join(" "));b["openid.realm"]!=null&&(c.openid_realm=b["openid.realm"]);b.cookie_policy!=null?c.cookie_policy=b.cookie_policy:b.cookiepolicy!=null&&(c.cookie_policy=b.cookiepolicy);c.login_hint==null&&b.user_id!=null&&(c.login_hint=b.user_id);try{_.Hx(c.cookie_policy)}catch(d){c.cookie_policy&&
CA("The cookie_policy configuration: '"+c.cookie_policy+"' is illegal, and thus ignored."),delete c.cookie_policy}b.hd!=null&&(c.hosted_domain=b.hd);c.prompt==null&&(b.immediate==1||b.immediate=="true"?c.prompt="none":b.approval_prompt=="force"&&(c.prompt="consent"));c.prompt=="none"&&(c.session_selection="first_valid");c.prompt=="none"&&c.access_type=="offline"&&delete c.access_type;typeof c.authuser==="undefined"&&(a=_.ii(),a!=null&&(c.authuser=a));a=b.redirect_uri||_.Xe("oauth-flow/redirectUri");
a!=null&&a!="postmessage"&&(c.redirect_uri=a);c.gsiwebsdk="shim";return c};
EA=function(a,b){var c=DA(a),d=new _.xk(function(e,f){_.ny(c,function(h){var k=h||{};_.Bb(yA,function(l){k[l]==null&&(k[l]=c[l])});!c.include_granted_scopes&&a&&a.scope&&(k.scope=a.scope);a&&a.state!=null&&(k.state=a.state);k.error?(c.prompt=="none"&&k.error=="user_logged_out"&&(k.error="immediate_failed_user_logged_out"),f(k)):(h=AA(k),h.authuser!=null&&_.Ye("googleapis.config/sessionIndex",h.authuser),e(h))})});b&&d.then(b,b);return d};var FA,HA;FA=null;_.IA=function(a,b){if(a.approvalprompt!=="force"){a=_.GA(a);a.prompt="none";delete a.redirect_uri;delete a.approval_prompt;delete a.immediate;if(b=!b)FA?(a.client_id!==FA.client_id&&window.console&&window.console.log&&window.console.log("Ignoring mismatched page-level auth param client_id="+a.client_id),b=!0):(FA=a,b=!1);b||HA(a)}};
_.GA=function(a){var b=a.redirecturi||"postmessage",c=_.zc((a.scope||"").replace(/[\s\xa0]+/g," "));b={client_id:a.clientid,redirect_uri:b,response_type:"code token id_token gsession",scope:c};a.approvalprompt&&(b.approval_prompt=a.approvalprompt);a.state&&(b.state=a.state);a.openidrealm&&(b["openid.realm"]=a.openidrealm);c=a.accesstype=="offline"?!0:(c=a.redirecturi)&&c!="postmessage";c&&(b.access_type="offline");a.requestvisibleactions&&(b.request_visible_actions=_.zc(a.requestvisibleactions.replace(/[\s\xa0]+/g,
" ")));a.after_redirect&&(b.after_redirect=a.after_redirect);a.cookiepolicy&&a.cookiepolicy!=="none"&&(b.cookie_policy=a.cookiepolicy);typeof a.includegrantedscopes!="undefined"&&(b.include_granted_scopes=a.includegrantedscopes);a.e&&(b.e=a.e);(a=a.authuser||_.Xe("googleapis.config/sessionIndex"))&&(b.authuser=a);(a=_.Xe("useoriginassocialhost"))&&(b.use_origin_as_socialhost=a);return b};HA=function(a){_.Bp("waaf0","signin","0");EA(a,function(b){_.Bp("waaf1","signin","0");wA(b)})};
_.JA=function(a){a=_.GA(a);_.Ye("oauth-flow/authWindowWidth",445);_.Ye("oauth-flow/authWindowHeight",615);HA(a)};_.KA=function(a){_.wz.unsubscribe(_.xz,a);_.wz.subscribe(_.xz,a)};var RA,UA;_.MA=function(a){return a.cookiepolicy?!0:(_.LA("cookiepolicy is a required field.  See https://developers.google.com/+/web/signin/#button_attr_cookiepolicy for more information."),!1)};_.LA=function(a){window.console&&(window.console.error?window.console.error(a):window.console.log&&window.console.log(a))};_.QA=function(a,b){var c=_.Po();_.Ee(a,c);c=qA(c);if(_.MA(c)){var d=_.NA();_.OA(c);b?_.Le(b,"click",function(){_.PA(c,d)}):_.PA(c,d)}};
_.NA=function(){var a=new RA;_.KA(function(b){a.ZI&&b&&(b.access_token&&_.Ye("isPlusUser",!0),b["g-oauth-window"]&&(a.ZI=!1,_.Vf.warn("OTA app install is no longer supported.")))});return a};RA=function(){this.ZI=!1};_.OA=function(a){a=_.SA(a);_.TA(a.callback);_.vA(function(){_.IA(a)})};_.SA=function(a){UA(a);a.redirecturi&&delete a.redirecturi;Cz(function(b){return a[b]})||(a.authuser=0);return a};UA=function(a){/^\s*$/.test(a.scope||"")&&(a.scope="https://www.googleapis.com/auth/plus.login")};
_.TA=function(a){if(typeof a==="string")if(window[a])a=window[a];else{_.LA('Callback function named "'+a+'" not found');return}a&&_.KA(a)};_.PA=function(a,b){b.ZI=!0;a=_.SA(a);_.JA(a)};_.t("gapi.auth.authorize",EA);_.t("gapi.auth.checkSessionState",function(a,b){var c=_.Ce();c.client_id=a.client_id;c.session_state=a.session_state;_.vA(function(){hA()?dA.send("check_session_state",c,function(d){b.call(null,d[0])},_.dn):_.$f.call(_.uz,"check_session_state",eA(function(d){b.call(null,d)}),c.session_state,c.client_id)})});_.t("gapi.auth.getAuthHeaderValueForFirstParty",function(a,b){_.Hi(_.Gi(),51).rb();return _.si(a,b)});_.t("gapi.auth.getToken",Lz);
_.t("gapi.auth.getVersionInfo",function(a,b){_.vA(function(){var c=_.si()||"",d=null,e=null;c&&(e=c.split(" "),e.length==2&&(d=e[1]));d?hA()?dA.send("get_versioninfo",{xapisidHash:d,sessionIndex:b},function(f){a(f[0])},_.dn):_.$f.call(_.uz,"get_versioninfo",eA(function(f){a(f)}),d,b):a()})});_.t("gapi.auth.init",_.vA);_.t("gapi.auth.setToken",_.Mz);_.t("gapi.auth.signIn",function(a){_.QA(a)});_.t("gapi.auth.signOut",function(){var a=Lz();a&&xA(a,a.cookie_policy)});
_.t("gapi.auth.unsafeUnpackIdToken",sA);_.t("gapi.auth._pimf",_.IA);_.t("gapi.auth._oart",pA);_.t("gapi.auth._guss",function(a){return Oz(a).read()});
var VA=_.Po();VA.clientid&&VA.scope&&VA.callback&&!_.Xe("disableRealtimeCallback")&&_.OA(VA);
});
// Google Inc.

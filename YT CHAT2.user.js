// ==UserScript==
// @name         YT CHAT1
// @namespace    https://greasyfork.org/en/users/160457-stan60250
// @version      1.5.0
// @description  CHATTER - Multi-tab optimized with proper timing intervals
// <AUTHOR>
// @match        *://www.youtube.com/live*
// @match        *://www.youtube.com/live_chat?is_popout=1&v=*
// @match        *://www.youtube.com/watch?*
// @icon         https://www.google.com/s2/favicons?domain=youtube.com
// @grant        none
// @license      MIT
// @run-at       document-start
// ==/UserScript==

// Configuration - 30 minutes to 1 hour intervals
var INTERVAL_SECONDS_MIN = 1800; // 30 minutes in seconds
var INTERVAL_SECONDS_MAX = 3600; // 1 hour in seconds
var STARTUP_DELAY_SECONDS = 5; // Initial delay before first execution

var UNICODE_SUN = '\u2600\ufe0f';
var UNICODE_MOON = '\ud83c\udf19';

(function() {
    'use strict';

    // Global variables for timing control
    var nextExecutionTimeout = null;
    var countdownInterval = null;
    var isScriptActive = true;
    var lastExecutionTime = 0;
    var nextExecutionTime = 0;
    var originalTitle = document.title;
    var isCountdownActive = false;
    var tabId = 'tab_' + Math.random().toString(36).substr(2, 9);
    var isMainTab = false;

    // Enhanced timer function with better error handling
    function executeTask() {
        try {
            var url = window.location.href;
            var currentTime = Date.now();

            console.log(`[YT CHAT] 🚀 Executing task at ${new Date().toLocaleTimeString()}`);

            // Temporarily show execution status in title
            var tempTitle = `🚀 Sending... - ${originalTitle}`;
            document.title = tempTitle;

            var success = false;
            if (url) {
                if (url.indexOf('/live_chat?') > -1) {
                    success = sendChat(document);
                } else if (url.indexOf('/watch?') > -1) {
                    var iframe = document.getElementById('chatframe');
                    var chatFrame = iframe ? (iframe.contentDocument || iframe.contentWindow.document) : null;
                    success = sendChat(chatFrame);
                }
            }

            // Show result briefly in title
            setTimeout(() => {
                document.title = success ? `✅ Sent! - ${originalTitle}` : `❌ Failed - ${originalTitle}`;
                setTimeout(() => {
                    lastExecutionTime = currentTime;
                    scheduleNextExecution();
                }, 2000); // Show result for 2 seconds
            }, 500);

        } catch (error) {
            console.error('[YT CHAT] Error in executeTask:', error);
            document.title = `❌ Error - ${originalTitle}`;
            setTimeout(() => {
                scheduleNextExecution(); // Continue even if there's an error
            }, 2000);
        }
    }

    // Multi-tab coordination functions
    function getTabCoordinationKey() {
        return 'ytchat_coordination';
    }

    function getTabInfo() {
        var stored = localStorage.getItem(getTabCoordinationKey());
        return stored ? JSON.parse(stored) : { tabs: {}, lastExecution: 0, activeTab: null };
    }

    function updateTabInfo(info) {
        localStorage.setItem(getTabCoordinationKey(), JSON.stringify(info));
    }

    function registerTab() {
        var info = getTabInfo();
        info.tabs[tabId] = {
            url: window.location.href,
            lastSeen: Date.now(),
            isActive: !document.hidden
        };

        // If no active tab or this is the first tab, make this the main tab
        if (!info.activeTab || !info.tabs[info.activeTab]) {
            info.activeTab = tabId;
            isMainTab = true;
            console.log(`[YT CHAT] 👑 This tab (${tabId}) is now the main coordinator`);
        }

        updateTabInfo(info);
        return isMainTab;
    }

    function cleanupInactiveTabs() {
        var info = getTabInfo();
        var now = Date.now();
        var activeTabExists = false;

        // Remove tabs that haven't been seen for 30 seconds
        Object.keys(info.tabs).forEach(id => {
            if (now - info.tabs[id].lastSeen > 30000) {
                console.log(`[YT CHAT] 🗑️ Removing inactive tab: ${id}`);
                delete info.tabs[id];
            } else if (id === info.activeTab) {
                activeTabExists = true;
            }
        });

        // If active tab is gone, elect a new one
        if (!activeTabExists && Object.keys(info.tabs).length > 0) {
            var newActiveTab = Object.keys(info.tabs)[0];
            info.activeTab = newActiveTab;
            if (newActiveTab === tabId) {
                isMainTab = true;
                console.log(`[YT CHAT] 👑 This tab (${tabId}) became the new main coordinator`);
            }
        }

        updateTabInfo(info);
    }

    // Improved random interval generator
    function getRandomInt(min, max) {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // Update tab title with countdown and tab status
    function updateTabTitle() {
        if (!isCountdownActive || !nextExecutionTime) {
            return;
        }

        var now = Date.now();
        var timeLeft = nextExecutionTime - now;

        if (timeLeft <= 0) {
            document.title = originalTitle;
            return;
        }

        var minutes = Math.floor(timeLeft / 60000);
        var seconds = Math.floor((timeLeft % 60000) / 1000);

        var statusIcon = isMainTab ? '👑' : '⏰';
        var countdownText = `${statusIcon} ${minutes}m ${seconds}s - ${originalTitle}`;
        document.title = countdownText;
    }

    // Start countdown display
    function startCountdown() {
        if (countdownInterval) {
            clearInterval(countdownInterval);
        }

        isCountdownActive = true;
        updateTabTitle();

        // Update every second
        countdownInterval = setInterval(updateTabTitle, 1000);
    }

    // Stop countdown display
    function stopCountdown() {
        isCountdownActive = false;
        if (countdownInterval) {
            clearInterval(countdownInterval);
            countdownInterval = null;
        }
        document.title = originalTitle;
    }

    // Schedule next execution with proper timing (only for main tab)
    function scheduleNextExecution() {
        // Clear any existing timeout
        if (nextExecutionTimeout) {
            clearTimeout(nextExecutionTimeout);
        }

        if (!isScriptActive) {
            console.log('[YT CHAT] Script is inactive, not scheduling next execution');
            stopCountdown();
            return;
        }

        // Only the main tab should schedule executions
        if (!isMainTab) {
            console.log(`[YT CHAT] 📱 Non-main tab (${tabId}) - not scheduling execution`);
            return;
        }

        var delay = getRandomInt(INTERVAL_SECONDS_MIN, INTERVAL_SECONDS_MAX) * 1000;
        nextExecutionTime = Date.now() + delay;
        var nextExecutionDate = new Date(nextExecutionTime);

        // Store global execution time for all tabs
        var info = getTabInfo();
        info.nextExecution = nextExecutionTime;
        updateTabInfo(info);

        console.log(`[YT CHAT] 👑 Main tab scheduling execution in ${Math.round(delay / 1000 / 60)} minutes (at ${nextExecutionDate.toLocaleTimeString()})`);

        // Start countdown in tab title
        startCountdown();

        nextExecutionTimeout = setTimeout(() => {
            stopCountdown();
            executeTask();
        }, delay);
    }

    // Enhanced visibility change handler for background operation
    function handleVisibilityChange() {
        // Update tab info
        var info = getTabInfo();
        if (info.tabs[tabId]) {
            info.tabs[tabId].isActive = !document.hidden;
            info.tabs[tabId].lastSeen = Date.now();
            updateTabInfo(info);
        }

        if (document.hidden) {
            console.log('[YT CHAT] 📱 Page hidden - continuing background operation');
        } else {
            console.log('[YT CHAT] 📱 Page visible - resuming normal operation');
            // Restore original title when page becomes visible
            originalTitle = document.title.replace(/^[👑⏰] \d+m \d+s - /, '');

            // Check if we need to sync with global execution time
            syncWithGlobalTimer();
        }
    }

    // Sync with global execution timer from other tabs
    function syncWithGlobalTimer() {
        var info = getTabInfo();
        if (info.nextExecution && info.nextExecution > Date.now()) {
            nextExecutionTime = info.nextExecution;
            if (!isCountdownActive && isMainTab) {
                startCountdown();
            }
        }
    }

    // Enhanced chat sending function with better error handling
    function sendChat(chatFrame) {
        try {
            if (!chatFrame) {
                console.error('[YT CHAT] Unable to find chat iframe');
                return false;
            }

            var chat_input = chatFrame.querySelector('div#input');
            var chat_submit = chatFrame.querySelector('.yt-spec-button-shape-next.yt-spec-button-shape-next--text.yt-spec-button-shape-next--mono.yt-spec-button-shape-next--size-m.yt-spec-button-shape-next--icon-button');

            if (!chat_input || !chat_submit) {
                console.error('[YT CHAT] Unable to find chat components');
                return false;
            }

            var storedComments = JSON.parse(localStorage.getItem('comments')) || [];
            if (storedComments.length === 0) {
                console.error('[YT CHAT] No comments found in localStorage.');
                return false;
            }

            var msg = storedComments[Math.floor(Math.random() * storedComments.length)];
            console.log(`[YT CHAT] Sending message: ${msg}`);

            // Focus and set message
            chat_input.focus();
            chat_input.textContent = msg;

            // Trigger input event
            chat_input.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));

            // Small delay before clicking submit to ensure the input is processed
            setTimeout(() => {
                chat_submit.click();
                console.log('[YT CHAT] Message sent successfully');
            }, 100);

            return true;

        } catch (error) {
            console.error('[YT CHAT] Error in sendChat:', error);
            return false;
        }
    }

    // Add keyboard shortcuts for manual control
    function addKeyboardShortcuts() {
        document.addEventListener('keydown', function(e) {
            // Ctrl+Shift+Y to toggle script
            if (e.ctrlKey && e.shiftKey && e.key === 'Y') {
                e.preventDefault();
                toggleScript();
            }
            // Ctrl+Shift+E to execute immediately
            else if (e.ctrlKey && e.shiftKey && e.key === 'E') {
                e.preventDefault();
                executeTaskManually();
            }
            // Ctrl+Shift+S to show status
            else if (e.ctrlKey && e.shiftKey && e.key === 'S') {
                e.preventDefault();
                showStatus();
            }
        });
    }

    // Toggle script on/off
    function toggleScript() {
        isScriptActive = !isScriptActive;
        if (isScriptActive) {
            console.log('[YT CHAT] ✅ Script activated');
            document.title = `✅ Activated - ${originalTitle}`;
            setTimeout(() => scheduleNextExecution(), 2000);
        } else {
            console.log('[YT CHAT] ⏸️ Script paused');
            document.title = `⏸️ Paused - ${originalTitle}`;
            stopCountdown();
            if (nextExecutionTimeout) {
                clearTimeout(nextExecutionTimeout);
            }
        }
    }

    // Execute task manually
    function executeTaskManually() {
        if (!isScriptActive) {
            console.log('[YT CHAT] Script is paused. Activate first with Ctrl+Shift+Y');
            return;
        }
        console.log('[YT CHAT] 🔧 Manual execution triggered');
        stopCountdown();
        if (nextExecutionTimeout) {
            clearTimeout(nextExecutionTimeout);
        }
        executeTask();
    }

    // Show current status including multi-tab info
    function showStatus() {
        var info = getTabInfo();
        var status = isScriptActive ? 'Active' : 'Paused';
        var nextTime = nextExecutionTime ? new Date(nextExecutionTime).toLocaleTimeString() : 'Not scheduled';
        var timeLeft = nextExecutionTime ? Math.round((nextExecutionTime - Date.now()) / 60000) : 0;
        var tabCount = Object.keys(info.tabs).length;
        var role = isMainTab ? 'Main Coordinator' : 'Secondary Tab';

        console.log(`[YT CHAT] 📊 Status: ${status} | Role: ${role} | Tabs: ${tabCount} | Next: ${nextTime} | Time left: ${timeLeft}m`);

        var tabList = Object.keys(info.tabs).map(id => {
            var tab = info.tabs[id];
            var isActive = (Date.now() - tab.lastSeen) < 5000;
            var isCurrent = id === tabId;
            var isMain = id === info.activeTab;
            return `${isCurrent ? '→ ' : '  '}${id.substr(0,8)} ${isMain ? '👑' : '📱'} ${isActive ? '🟢' : '🔴'}`;
        }).join('\n');

        alert(`YT Chat Script Status:

Status: ${status}
Role: ${role}
Active Tabs: ${tabCount}
Next execution: ${nextTime}
Time remaining: ${timeLeft} minutes

Tab List:
${tabList}

Shortcuts:
Ctrl+Shift+Y - Toggle on/off
Ctrl+Shift+E - Execute now
Ctrl+Shift+S - Show status`);
    }

    // Initialize script with proper timing and multi-tab coordination
    function initializeScript() {
        console.log(`[YT CHAT] 🎬 Script initialized - intervals set to ${INTERVAL_SECONDS_MIN/60}-${INTERVAL_SECONDS_MAX/60} minutes`);
        console.log('[YT CHAT] 🎮 Keyboard shortcuts: Ctrl+Shift+Y (toggle), Ctrl+Shift+E (execute), Ctrl+Shift+S (status)');

        // Register this tab and determine if it's the main coordinator
        registerTab();

        // Set up periodic tab coordination
        setInterval(() => {
            cleanupInactiveTabs();
            registerTab(); // Update our presence
            syncWithGlobalTimer();
        }, 5000); // Every 5 seconds

        // Add keyboard shortcuts
        addKeyboardShortcuts();

        // Add visibility change listener for background operation
        document.addEventListener('visibilitychange', handleVisibilityChange);

        // Add beforeunload listener to cleanup
        window.addEventListener('beforeunload', function() {
            isScriptActive = false;
            stopCountdown();
            if (nextExecutionTimeout) {
                clearTimeout(nextExecutionTimeout);
            }

            // Remove this tab from coordination
            var info = getTabInfo();
            delete info.tabs[tabId];
            if (info.activeTab === tabId) {
                info.activeTab = null;
            }
            updateTabInfo(info);
        });

        // Start the execution with a startup delay (only for main tab)
        setTimeout(() => {
            if (isMainTab) {
                console.log('[YT CHAT] 🚀 Main tab starting first execution after startup delay');
                executeTask();
            } else {
                console.log('[YT CHAT] 📱 Secondary tab ready - waiting for main tab coordination');
                syncWithGlobalTimer();
            }
        }, STARTUP_DELAY_SECONDS * 1000);
    }

    // Wait for DOM to be ready before initializing
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeScript);
    } else {
        initializeScript();
    }

})();
